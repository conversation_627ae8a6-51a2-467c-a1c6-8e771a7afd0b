/*!****************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/styles/theme.scss ***!
  \****************************************************************************************************************/
@import url(https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap);
/*!********************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/styles/theme.scss (1) ***!
  \********************************************************************************************************************/
@import url(https://fonts.googleapis.com/css2?family=Josefin+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&display=swap);
/*!*********************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js!./node_modules/swiper/swiper-bundle.min.css ***!
  \*********************************************************************************************/
/**
 * Swiper 8.0.7
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2022 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: March 4, 2022
 */

@font-face{font-family:swiper-icons;src:url("data:application/font-woff;charset=utf-8;base64, 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");font-weight:400;font-style:normal}:root{--swiper-theme-color:#007aff}.swiper{margin-left:auto;margin-right:auto;position:relative;overflow:hidden;list-style:none;padding:0;z-index:1}.swiper-vertical>.swiper-wrapper{flex-direction:column}.swiper-wrapper{position:relative;width:100%;height:100%;z-index:1;display:flex;transition-property:transform;box-sizing:content-box}.swiper-android .swiper-slide,.swiper-wrapper{transform:translate3d(0px,0,0)}.swiper-pointer-events{touch-action:pan-y}.swiper-pointer-events.swiper-vertical{touch-action:pan-x}.swiper-slide{flex-shrink:0;width:100%;height:100%;position:relative;transition-property:transform}.swiper-slide-invisible-blank{visibility:hidden}.swiper-autoheight,.swiper-autoheight .swiper-slide{height:auto}.swiper-autoheight .swiper-wrapper{align-items:flex-start;transition-property:transform,height}.swiper-backface-hidden .swiper-slide{transform:translateZ(0);-webkit-backface-visibility:hidden;backface-visibility:hidden}.swiper-3d,.swiper-3d.swiper-css-mode .swiper-wrapper{perspective:1200px}.swiper-3d .swiper-cube-shadow,.swiper-3d .swiper-slide,.swiper-3d .swiper-slide-shadow,.swiper-3d .swiper-slide-shadow-bottom,.swiper-3d .swiper-slide-shadow-left,.swiper-3d .swiper-slide-shadow-right,.swiper-3d .swiper-slide-shadow-top,.swiper-3d .swiper-wrapper{transform-style:preserve-3d}.swiper-3d .swiper-slide-shadow,.swiper-3d .swiper-slide-shadow-bottom,.swiper-3d .swiper-slide-shadow-left,.swiper-3d .swiper-slide-shadow-right,.swiper-3d .swiper-slide-shadow-top{position:absolute;left:0;top:0;width:100%;height:100%;pointer-events:none;z-index:10}.swiper-3d .swiper-slide-shadow{background:rgba(0,0,0,.15)}.swiper-3d .swiper-slide-shadow-left{background-image:linear-gradient(to left,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-3d .swiper-slide-shadow-right{background-image:linear-gradient(to right,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-3d .swiper-slide-shadow-top{background-image:linear-gradient(to top,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-3d .swiper-slide-shadow-bottom{background-image:linear-gradient(to bottom,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-css-mode>.swiper-wrapper{overflow:auto;scrollbar-width:none;-ms-overflow-style:none}.swiper-css-mode>.swiper-wrapper::-webkit-scrollbar{display:none}.swiper-css-mode>.swiper-wrapper>.swiper-slide{scroll-snap-align:start start}.swiper-horizontal.swiper-css-mode>.swiper-wrapper{scroll-snap-type:x mandatory}.swiper-vertical.swiper-css-mode>.swiper-wrapper{scroll-snap-type:y mandatory}.swiper-centered>.swiper-wrapper::before{content:'';flex-shrink:0;order:9999}.swiper-centered.swiper-horizontal>.swiper-wrapper>.swiper-slide:first-child{margin-inline-start:var(--swiper-centered-offset-before)}.swiper-centered.swiper-horizontal>.swiper-wrapper::before{height:100%;min-height:1px;width:var(--swiper-centered-offset-after)}.swiper-centered.swiper-vertical>.swiper-wrapper>.swiper-slide:first-child{margin-block-start:var(--swiper-centered-offset-before)}.swiper-centered.swiper-vertical>.swiper-wrapper::before{width:100%;min-width:1px;height:var(--swiper-centered-offset-after)}.swiper-centered>.swiper-wrapper>.swiper-slide{scroll-snap-align:center center}.swiper-virtual .swiper-slide{-webkit-backface-visibility:hidden;transform:translateZ(0)}.swiper-virtual.swiper-css-mode .swiper-wrapper::after{content:'';position:absolute;left:0;top:0;pointer-events:none}.swiper-virtual.swiper-css-mode.swiper-horizontal .swiper-wrapper::after{height:1px;width:var(--swiper-virtual-size)}.swiper-virtual.swiper-css-mode.swiper-vertical .swiper-wrapper::after{width:1px;height:var(--swiper-virtual-size)}:root{--swiper-navigation-size:44px}.swiper-button-next,.swiper-button-prev{position:absolute;top:50%;width:calc(var(--swiper-navigation-size)/ 44 * 27);height:var(--swiper-navigation-size);margin-top:calc(0px - (var(--swiper-navigation-size)/ 2));z-index:10;cursor:pointer;display:flex;align-items:center;justify-content:center;color:var(--swiper-navigation-color,var(--swiper-theme-color))}.swiper-button-next.swiper-button-disabled,.swiper-button-prev.swiper-button-disabled{opacity:.35;cursor:auto;pointer-events:none}.swiper-button-next:after,.swiper-button-prev:after{font-family:swiper-icons;font-size:var(--swiper-navigation-size);text-transform:none!important;letter-spacing:0;text-transform:none;font-variant:initial;line-height:1}.swiper-button-prev,.swiper-rtl .swiper-button-next{left:10px;right:auto}.swiper-button-prev:after,.swiper-rtl .swiper-button-next:after{content:'prev'}.swiper-button-next,.swiper-rtl .swiper-button-prev{right:10px;left:auto}.swiper-button-next:after,.swiper-rtl .swiper-button-prev:after{content:'next'}.swiper-button-lock{display:none}.swiper-pagination{position:absolute;text-align:center;transition:.3s opacity;transform:translate3d(0,0,0);z-index:10}.swiper-pagination.swiper-pagination-hidden{opacity:0}.swiper-horizontal>.swiper-pagination-bullets,.swiper-pagination-bullets.swiper-pagination-horizontal,.swiper-pagination-custom,.swiper-pagination-fraction{bottom:10px;left:0;width:100%}.swiper-pagination-bullets-dynamic{overflow:hidden;font-size:0}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transform:scale(.33);position:relative}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active{transform:scale(1)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main{transform:scale(1)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev{transform:scale(.66)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev{transform:scale(.33)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next{transform:scale(.66)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next{transform:scale(.33)}.swiper-pagination-bullet{width:var(--swiper-pagination-bullet-width,var(--swiper-pagination-bullet-size,8px));height:var(--swiper-pagination-bullet-height,var(--swiper-pagination-bullet-size,8px));display:inline-block;border-radius:50%;background:var(--swiper-pagination-bullet-inactive-color,#000);opacity:var(--swiper-pagination-bullet-inactive-opacity, .2)}button.swiper-pagination-bullet{border:none;margin:0;padding:0;box-shadow:none;-webkit-appearance:none;appearance:none}.swiper-pagination-clickable .swiper-pagination-bullet{cursor:pointer}.swiper-pagination-bullet:only-child{display:none!important}.swiper-pagination-bullet-active{opacity:var(--swiper-pagination-bullet-opacity, 1);background:var(--swiper-pagination-color,var(--swiper-theme-color))}.swiper-pagination-vertical.swiper-pagination-bullets,.swiper-vertical>.swiper-pagination-bullets{right:10px;top:50%;transform:translate3d(0px,-50%,0)}.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet,.swiper-vertical>.swiper-pagination-bullets .swiper-pagination-bullet{margin:var(--swiper-pagination-bullet-vertical-gap,6px) 0;display:block}.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{top:50%;transform:translateY(-50%);width:8px}.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{display:inline-block;transition:.2s transform,.2s top}.swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet,.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet{margin:0 var(--swiper-pagination-bullet-horizontal-gap,4px)}.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{left:50%;transform:translateX(-50%);white-space:nowrap}.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transition:.2s transform,.2s left}.swiper-horizontal.swiper-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transition:.2s transform,.2s right}.swiper-pagination-progressbar{background:rgba(0,0,0,.25);position:absolute}.swiper-pagination-progressbar .swiper-pagination-progressbar-fill{background:var(--swiper-pagination-color,var(--swiper-theme-color));position:absolute;left:0;top:0;width:100%;height:100%;transform:scale(0);transform-origin:left top}.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill{transform-origin:right top}.swiper-horizontal>.swiper-pagination-progressbar,.swiper-pagination-progressbar.swiper-pagination-horizontal,.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite,.swiper-vertical>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite{width:100%;height:4px;left:0;top:0}.swiper-horizontal>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite,.swiper-pagination-progressbar.swiper-pagination-vertical,.swiper-vertical>.swiper-pagination-progressbar{width:4px;height:100%;left:0;top:0}.swiper-pagination-lock{display:none}.swiper-scrollbar{border-radius:10px;position:relative;-ms-touch-action:none;background:rgba(0,0,0,.1)}.swiper-horizontal>.swiper-scrollbar{position:absolute;left:1%;bottom:3px;z-index:50;height:5px;width:98%}.swiper-vertical>.swiper-scrollbar{position:absolute;right:3px;top:1%;z-index:50;width:5px;height:98%}.swiper-scrollbar-drag{height:100%;width:100%;position:relative;background:rgba(0,0,0,.5);border-radius:10px;left:0;top:0}.swiper-scrollbar-cursor-drag{cursor:move}.swiper-scrollbar-lock{display:none}.swiper-zoom-container{width:100%;height:100%;display:flex;justify-content:center;align-items:center;text-align:center}.swiper-zoom-container>canvas,.swiper-zoom-container>img,.swiper-zoom-container>svg{max-width:100%;max-height:100%;object-fit:contain}.swiper-slide-zoomed{cursor:move}.swiper-lazy-preloader{width:42px;height:42px;position:absolute;left:50%;top:50%;margin-left:-21px;margin-top:-21px;z-index:10;transform-origin:50%;box-sizing:border-box;border:4px solid var(--swiper-preloader-color,var(--swiper-theme-color));border-radius:50%;border-top-color:transparent}.swiper-slide-visible .swiper-lazy-preloader{animation:swiper-preloader-spin 1s infinite linear}.swiper-lazy-preloader-white{--swiper-preloader-color:#fff}.swiper-lazy-preloader-black{--swiper-preloader-color:#000}@keyframes swiper-preloader-spin{100%{transform:rotate(360deg)}}.swiper .swiper-notification{position:absolute;left:0;top:0;pointer-events:none;opacity:0;z-index:-1000}.swiper-free-mode>.swiper-wrapper{transition-timing-function:ease-out;margin:0 auto}.swiper-grid>.swiper-wrapper{flex-wrap:wrap}.swiper-grid-column>.swiper-wrapper{flex-wrap:wrap;flex-direction:column}.swiper-fade.swiper-free-mode .swiper-slide{transition-timing-function:ease-out}.swiper-fade .swiper-slide{pointer-events:none;transition-property:opacity}.swiper-fade .swiper-slide .swiper-slide{pointer-events:none}.swiper-fade .swiper-slide-active,.swiper-fade .swiper-slide-active .swiper-slide-active{pointer-events:auto}.swiper-cube{overflow:visible}.swiper-cube .swiper-slide{pointer-events:none;-webkit-backface-visibility:hidden;backface-visibility:hidden;z-index:1;visibility:hidden;transform-origin:0 0;width:100%;height:100%}.swiper-cube .swiper-slide .swiper-slide{pointer-events:none}.swiper-cube.swiper-rtl .swiper-slide{transform-origin:100% 0}.swiper-cube .swiper-slide-active,.swiper-cube .swiper-slide-active .swiper-slide-active{pointer-events:auto}.swiper-cube .swiper-slide-active,.swiper-cube .swiper-slide-next,.swiper-cube .swiper-slide-next+.swiper-slide,.swiper-cube .swiper-slide-prev{pointer-events:auto;visibility:visible}.swiper-cube .swiper-slide-shadow-bottom,.swiper-cube .swiper-slide-shadow-left,.swiper-cube .swiper-slide-shadow-right,.swiper-cube .swiper-slide-shadow-top{z-index:0;-webkit-backface-visibility:hidden;backface-visibility:hidden}.swiper-cube .swiper-cube-shadow{position:absolute;left:0;bottom:0px;width:100%;height:100%;opacity:.6;z-index:0}.swiper-cube .swiper-cube-shadow:before{content:'';background:#000;position:absolute;left:0;top:0;bottom:0;right:0;filter:blur(50px)}.swiper-flip{overflow:visible}.swiper-flip .swiper-slide{pointer-events:none;-webkit-backface-visibility:hidden;backface-visibility:hidden;z-index:1}.swiper-flip .swiper-slide .swiper-slide{pointer-events:none}.swiper-flip .swiper-slide-active,.swiper-flip .swiper-slide-active .swiper-slide-active{pointer-events:auto}.swiper-flip .swiper-slide-shadow-bottom,.swiper-flip .swiper-slide-shadow-left,.swiper-flip .swiper-slide-shadow-right,.swiper-flip .swiper-slide-shadow-top{z-index:0;-webkit-backface-visibility:hidden;backface-visibility:hidden}.swiper-creative .swiper-slide{-webkit-backface-visibility:hidden;backface-visibility:hidden;overflow:hidden;transition-property:transform,opacity,height}.swiper-cards{overflow:visible}.swiper-cards .swiper-slide{transform-origin:center bottom;-webkit-backface-visibility:hidden;backface-visibility:hidden;overflow:hidden}
/*!********************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/styles/theme.scss (2) ***!
  \********************************************************************************************************************/
/*
=========================================================================
| Node Modules
=========================================================================
*/
/*!
 * Bootstrap v4.6.0 (https://getbootstrap.com/)
 * Copyright 2011-2021 The Bootstrap Authors
 * Copyright 2011-2021 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root {
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #dc3545;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffc107;
  --bs-green: #198754;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-white: #fff;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-gray-100: #f8f9fa;
  --bs-gray-200: #e9ecef;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-primary-rgb: 13, 110, 253;
  --bs-secondary-rgb: 108, 117, 125;
  --bs-success-rgb: 25, 135, 84;
  --bs-info-rgb: 13, 202, 240;
  --bs-warning-rgb: 255, 193, 7;
  --bs-danger-rgb: 220, 53, 69;
  --bs-light-rgb: 248, 249, 250;
  --bs-dark-rgb: 33, 37, 41;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-body-color-rgb: 33, 37, 41;
  --bs-body-bg-rgb: 255, 255, 255;
  --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --bs-body-font-family: var(--bs-font-sans-serif);
  --bs-body-font-size: 1rem;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.5;
  --bs-body-color: #212529;
  --bs-body-bg: #fff; }

*,
*::before,
*::after {
  box-sizing: border-box; }

@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth; } }

body {
  margin: 0;
  font-family: var(--bs-body-font-family);
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  color: var(--bs-body-color);
  text-align: var(--bs-body-text-align);
  background-color: var(--bs-body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }

hr {
  margin: 1rem 0;
  color: inherit;
  background-color: currentColor;
  border: 0;
  opacity: 0.25; }

hr:not([size]) {
  height: 1px; }

h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2; }

h1, .h1 {
  font-size: calc(1.375rem + 1.5vw); }
  @media (min-width: 1200px) {
    h1, .h1 {
      font-size: 2.5rem; } }

h2, .h2 {
  font-size: calc(1.325rem + 0.9vw); }
  @media (min-width: 1200px) {
    h2, .h2 {
      font-size: 2rem; } }

h3, .h3 {
  font-size: calc(1.3rem + 0.6vw); }
  @media (min-width: 1200px) {
    h3, .h3 {
      font-size: 1.75rem; } }

h4, .h4 {
  font-size: calc(1.275rem + 0.3vw); }
  @media (min-width: 1200px) {
    h4, .h4 {
      font-size: 1.5rem; } }

h5, .h5 {
  font-size: 1.25rem; }

h6, .h6 {
  font-size: 1rem; }

p {
  margin-top: 0;
  margin-bottom: 1rem; }

abbr[title],
abbr[data-bs-original-title] {
  text-decoration: underline dotted;
  cursor: help;
  text-decoration-skip-ink: none; }

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit; }

ol,
ul {
  padding-left: 2rem; }

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem; }

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0; }

dt {
  font-weight: 700; }

dd {
  margin-bottom: .5rem;
  margin-left: 0; }

blockquote {
  margin: 0 0 1rem; }

b,
strong {
  font-weight: bolder; }

small, .small {
  font-size: 0.875em; }

mark, .mark {
  padding: 0.2em;
  background-color: #fcf8e3; }

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline; }

sub {
  bottom: -.25em; }

sup {
  top: -.5em; }

a {
  color: #0d6efd;
  text-decoration: underline; }
  a:hover {
    color: #0a58ca; }

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none; }

pre,
code,
kbd,
samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em;
  direction: ltr /* rtl:ignore */;
  unicode-bidi: bidi-override; }

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 0.875em; }
  pre code {
    font-size: inherit;
    color: inherit;
    word-break: normal; }

code {
  font-size: 0.875em;
  color: #d63384;
  word-wrap: break-word; }
  a > code {
    color: inherit; }

kbd {
  padding: 0.2rem 0.4rem;
  font-size: 0.875em;
  color: #fff;
  background-color: #212529;
  border-radius: 0.2rem; }
  kbd kbd {
    padding: 0;
    font-size: 1em;
    font-weight: 700; }

figure {
  margin: 0 0 1rem; }

img,
svg {
  vertical-align: middle; }

table {
  caption-side: bottom;
  border-collapse: collapse; }

caption {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: #6c757d;
  text-align: left; }

th {
  text-align: inherit;
  text-align: -webkit-match-parent; }

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0; }

label {
  display: inline-block; }

button {
  border-radius: 0; }

button:focus:not(:focus-visible) {
  outline: 0; }

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit; }

button,
select {
  text-transform: none; }

[role="button"] {
  cursor: pointer; }

select {
  word-wrap: normal; }
  select:disabled {
    opacity: 1; }

[list]::-webkit-calendar-picker-indicator {
  display: none; }

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button; }
  button:not(:disabled),
  [type="button"]:not(:disabled),
  [type="reset"]:not(:disabled),
  [type="submit"]:not(:disabled) {
    cursor: pointer; }

::-moz-focus-inner {
  padding: 0;
  border-style: none; }

textarea {
  resize: vertical; }

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0; }

legend {
  float: left;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit; }
  @media (min-width: 1200px) {
    legend {
      font-size: 1.5rem; } }
  legend + * {
    clear: left; }

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0; }

::-webkit-inner-spin-button {
  height: auto; }

[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: textfield; }

/* rtl:raw:
[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
*/
::-webkit-search-decoration {
  -webkit-appearance: none; }

::-webkit-color-swatch-wrapper {
  padding: 0; }

::file-selector-button {
  font: inherit; }

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button; }

output {
  display: inline-block; }

iframe {
  border: 0; }

summary {
  display: list-item;
  cursor: pointer; }

progress {
  vertical-align: baseline; }

[hidden] {
  display: none !important; }

/*!
 * Bootstrap Grid v5.1.3 (https://getbootstrap.com/)
 * Copyright 2011-2021 The Bootstrap Authors
 * Copyright 2011-2021 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root {
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #dc3545;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffc107;
  --bs-green: #198754;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-white: #fff;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-gray-100: #f8f9fa;
  --bs-gray-200: #e9ecef;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-primary-rgb: 13, 110, 253;
  --bs-secondary-rgb: 108, 117, 125;
  --bs-success-rgb: 25, 135, 84;
  --bs-info-rgb: 13, 202, 240;
  --bs-warning-rgb: 255, 193, 7;
  --bs-danger-rgb: 220, 53, 69;
  --bs-light-rgb: 248, 249, 250;
  --bs-dark-rgb: 33, 37, 41;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-body-color-rgb: 33, 37, 41;
  --bs-body-bg-rgb: 255, 255, 255;
  --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --bs-body-font-family: var(--bs-font-sans-serif);
  --bs-body-font-size: 1rem;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.5;
  --bs-body-color: #212529;
  --bs-body-bg: #fff; }

.container,
.container-fluid,
.container-sm,
.container-md,
.container-lg,
.container-xl,
.container-xxl {
  width: 100%;
  padding-right: var(--bs-gutter-x, 0.95rem);
  padding-left: var(--bs-gutter-x, 0.95rem);
  margin-right: auto;
  margin-left: auto; }

@media (min-width: 576px) {
  .container, .container-sm {
    max-width: 100%; } }

@media (min-width: 768px) {
  .container, .container-sm, .container-md {
    max-width: 100%; } }

@media (min-width: 992px) {
  .container, .container-sm, .container-md, .container-lg {
    max-width: 1140px; } }

@media (min-width: 1200px) {
  .container, .container-sm, .container-md, .container-lg, .container-xl {
    max-width: 1140px; } }

@media (min-width: 1400px) {
  .container, .container-sm, .container-md, .container-lg, .container-xl, .container-xxl {
    max-width: 1320px; } }

.row {
  --bs-gutter-x: 1.9rem;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: calc(-.5 * var(--bs-gutter-x));
  margin-left: calc(-.5 * var(--bs-gutter-x)); }
  .row > * {
    box-sizing: border-box;
    flex-shrink: 0;
    width: 100%;
    max-width: 100%;
    padding-right: calc(var(--bs-gutter-x) * .5);
    padding-left: calc(var(--bs-gutter-x) * .5);
    margin-top: var(--bs-gutter-y); }

.col {
  flex: 1 0 0%; }

.row-cols-auto > * {
  flex: 0 0 auto;
  width: auto; }

.row-cols-1 > * {
  flex: 0 0 auto;
  width: 100%; }

.row-cols-2 > * {
  flex: 0 0 auto;
  width: 50%; }

.row-cols-3 > * {
  flex: 0 0 auto;
  width: 33.33333%; }

.row-cols-4 > * {
  flex: 0 0 auto;
  width: 25%; }

.row-cols-5 > * {
  flex: 0 0 auto;
  width: 20%; }

.row-cols-6 > * {
  flex: 0 0 auto;
  width: 16.66667%; }

.col-auto {
  flex: 0 0 auto;
  width: auto; }

.col-1 {
  flex: 0 0 auto;
  width: 8.33333%; }

.col-2 {
  flex: 0 0 auto;
  width: 16.66667%; }

.col-3 {
  flex: 0 0 auto;
  width: 25%; }

.col-4 {
  flex: 0 0 auto;
  width: 33.33333%; }

.col-5 {
  flex: 0 0 auto;
  width: 41.66667%; }

.col-6 {
  flex: 0 0 auto;
  width: 50%; }

.col-7 {
  flex: 0 0 auto;
  width: 58.33333%; }

.col-8 {
  flex: 0 0 auto;
  width: 66.66667%; }

.col-9 {
  flex: 0 0 auto;
  width: 75%; }

.col-10 {
  flex: 0 0 auto;
  width: 83.33333%; }

.col-11 {
  flex: 0 0 auto;
  width: 91.66667%; }

.col-12 {
  flex: 0 0 auto;
  width: 100%; }

.offset-1 {
  margin-left: 8.33333%; }

.offset-2 {
  margin-left: 16.66667%; }

.offset-3 {
  margin-left: 25%; }

.offset-4 {
  margin-left: 33.33333%; }

.offset-5 {
  margin-left: 41.66667%; }

.offset-6 {
  margin-left: 50%; }

.offset-7 {
  margin-left: 58.33333%; }

.offset-8 {
  margin-left: 66.66667%; }

.offset-9 {
  margin-left: 75%; }

.offset-10 {
  margin-left: 83.33333%; }

.offset-11 {
  margin-left: 91.66667%; }

.g-0,
.gx-0 {
  --bs-gutter-x: 0; }

.g-0,
.gy-0 {
  --bs-gutter-y: 0; }

.g-1,
.gx-1 {
  --bs-gutter-x: 0.25rem; }

.g-1,
.gy-1 {
  --bs-gutter-y: 0.25rem; }

.g-2,
.gx-2 {
  --bs-gutter-x: 0.5rem; }

.g-2,
.gy-2 {
  --bs-gutter-y: 0.5rem; }

.g-3,
.gx-3 {
  --bs-gutter-x: 1rem; }

.g-3,
.gy-3 {
  --bs-gutter-y: 1rem; }

.g-4,
.gx-4 {
  --bs-gutter-x: 1.5rem; }

.g-4,
.gy-4 {
  --bs-gutter-y: 1.5rem; }

.g-5,
.gx-5 {
  --bs-gutter-x: 3rem; }

.g-5,
.gy-5 {
  --bs-gutter-y: 3rem; }

@media (min-width: 576px) {
  .col-sm {
    flex: 1 0 0%; }
  .row-cols-sm-auto > * {
    flex: 0 0 auto;
    width: auto; }
  .row-cols-sm-1 > * {
    flex: 0 0 auto;
    width: 100%; }
  .row-cols-sm-2 > * {
    flex: 0 0 auto;
    width: 50%; }
  .row-cols-sm-3 > * {
    flex: 0 0 auto;
    width: 33.33333%; }
  .row-cols-sm-4 > * {
    flex: 0 0 auto;
    width: 25%; }
  .row-cols-sm-5 > * {
    flex: 0 0 auto;
    width: 20%; }
  .row-cols-sm-6 > * {
    flex: 0 0 auto;
    width: 16.66667%; }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto; }
  .col-sm-1 {
    flex: 0 0 auto;
    width: 8.33333%; }
  .col-sm-2 {
    flex: 0 0 auto;
    width: 16.66667%; }
  .col-sm-3 {
    flex: 0 0 auto;
    width: 25%; }
  .col-sm-4 {
    flex: 0 0 auto;
    width: 33.33333%; }
  .col-sm-5 {
    flex: 0 0 auto;
    width: 41.66667%; }
  .col-sm-6 {
    flex: 0 0 auto;
    width: 50%; }
  .col-sm-7 {
    flex: 0 0 auto;
    width: 58.33333%; }
  .col-sm-8 {
    flex: 0 0 auto;
    width: 66.66667%; }
  .col-sm-9 {
    flex: 0 0 auto;
    width: 75%; }
  .col-sm-10 {
    flex: 0 0 auto;
    width: 83.33333%; }
  .col-sm-11 {
    flex: 0 0 auto;
    width: 91.66667%; }
  .col-sm-12 {
    flex: 0 0 auto;
    width: 100%; }
  .offset-sm-0 {
    margin-left: 0; }
  .offset-sm-1 {
    margin-left: 8.33333%; }
  .offset-sm-2 {
    margin-left: 16.66667%; }
  .offset-sm-3 {
    margin-left: 25%; }
  .offset-sm-4 {
    margin-left: 33.33333%; }
  .offset-sm-5 {
    margin-left: 41.66667%; }
  .offset-sm-6 {
    margin-left: 50%; }
  .offset-sm-7 {
    margin-left: 58.33333%; }
  .offset-sm-8 {
    margin-left: 66.66667%; }
  .offset-sm-9 {
    margin-left: 75%; }
  .offset-sm-10 {
    margin-left: 83.33333%; }
  .offset-sm-11 {
    margin-left: 91.66667%; }
  .g-sm-0,
  .gx-sm-0 {
    --bs-gutter-x: 0; }
  .g-sm-0,
  .gy-sm-0 {
    --bs-gutter-y: 0; }
  .g-sm-1,
  .gx-sm-1 {
    --bs-gutter-x: 0.25rem; }
  .g-sm-1,
  .gy-sm-1 {
    --bs-gutter-y: 0.25rem; }
  .g-sm-2,
  .gx-sm-2 {
    --bs-gutter-x: 0.5rem; }
  .g-sm-2,
  .gy-sm-2 {
    --bs-gutter-y: 0.5rem; }
  .g-sm-3,
  .gx-sm-3 {
    --bs-gutter-x: 1rem; }
  .g-sm-3,
  .gy-sm-3 {
    --bs-gutter-y: 1rem; }
  .g-sm-4,
  .gx-sm-4 {
    --bs-gutter-x: 1.5rem; }
  .g-sm-4,
  .gy-sm-4 {
    --bs-gutter-y: 1.5rem; }
  .g-sm-5,
  .gx-sm-5 {
    --bs-gutter-x: 3rem; }
  .g-sm-5,
  .gy-sm-5 {
    --bs-gutter-y: 3rem; } }

@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0%; }
  .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto; }
  .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%; }
  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%; }
  .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.33333%; }
  .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%; }
  .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%; }
  .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.66667%; }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto; }
  .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333%; }
  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66667%; }
  .col-md-3 {
    flex: 0 0 auto;
    width: 25%; }
  .col-md-4 {
    flex: 0 0 auto;
    width: 33.33333%; }
  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66667%; }
  .col-md-6 {
    flex: 0 0 auto;
    width: 50%; }
  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333%; }
  .col-md-8 {
    flex: 0 0 auto;
    width: 66.66667%; }
  .col-md-9 {
    flex: 0 0 auto;
    width: 75%; }
  .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333%; }
  .col-md-11 {
    flex: 0 0 auto;
    width: 91.66667%; }
  .col-md-12 {
    flex: 0 0 auto;
    width: 100%; }
  .offset-md-0 {
    margin-left: 0; }
  .offset-md-1 {
    margin-left: 8.33333%; }
  .offset-md-2 {
    margin-left: 16.66667%; }
  .offset-md-3 {
    margin-left: 25%; }
  .offset-md-4 {
    margin-left: 33.33333%; }
  .offset-md-5 {
    margin-left: 41.66667%; }
  .offset-md-6 {
    margin-left: 50%; }
  .offset-md-7 {
    margin-left: 58.33333%; }
  .offset-md-8 {
    margin-left: 66.66667%; }
  .offset-md-9 {
    margin-left: 75%; }
  .offset-md-10 {
    margin-left: 83.33333%; }
  .offset-md-11 {
    margin-left: 91.66667%; }
  .g-md-0,
  .gx-md-0 {
    --bs-gutter-x: 0; }
  .g-md-0,
  .gy-md-0 {
    --bs-gutter-y: 0; }
  .g-md-1,
  .gx-md-1 {
    --bs-gutter-x: 0.25rem; }
  .g-md-1,
  .gy-md-1 {
    --bs-gutter-y: 0.25rem; }
  .g-md-2,
  .gx-md-2 {
    --bs-gutter-x: 0.5rem; }
  .g-md-2,
  .gy-md-2 {
    --bs-gutter-y: 0.5rem; }
  .g-md-3,
  .gx-md-3 {
    --bs-gutter-x: 1rem; }
  .g-md-3,
  .gy-md-3 {
    --bs-gutter-y: 1rem; }
  .g-md-4,
  .gx-md-4 {
    --bs-gutter-x: 1.5rem; }
  .g-md-4,
  .gy-md-4 {
    --bs-gutter-y: 1.5rem; }
  .g-md-5,
  .gx-md-5 {
    --bs-gutter-x: 3rem; }
  .g-md-5,
  .gy-md-5 {
    --bs-gutter-y: 3rem; } }

@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0%; }
  .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto; }
  .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%; }
  .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%; }
  .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.33333%; }
  .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%; }
  .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%; }
  .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.66667%; }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto; }
  .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333%; }
  .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66667%; }
  .col-lg-3 {
    flex: 0 0 auto;
    width: 25%; }
  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333%; }
  .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66667%; }
  .col-lg-6 {
    flex: 0 0 auto;
    width: 50%; }
  .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333%; }
  .col-lg-8 {
    flex: 0 0 auto;
    width: 66.66667%; }
  .col-lg-9 {
    flex: 0 0 auto;
    width: 75%; }
  .col-lg-10 {
    flex: 0 0 auto;
    width: 83.33333%; }
  .col-lg-11 {
    flex: 0 0 auto;
    width: 91.66667%; }
  .col-lg-12 {
    flex: 0 0 auto;
    width: 100%; }
  .offset-lg-0 {
    margin-left: 0; }
  .offset-lg-1 {
    margin-left: 8.33333%; }
  .offset-lg-2 {
    margin-left: 16.66667%; }
  .offset-lg-3 {
    margin-left: 25%; }
  .offset-lg-4 {
    margin-left: 33.33333%; }
  .offset-lg-5 {
    margin-left: 41.66667%; }
  .offset-lg-6 {
    margin-left: 50%; }
  .offset-lg-7 {
    margin-left: 58.33333%; }
  .offset-lg-8 {
    margin-left: 66.66667%; }
  .offset-lg-9 {
    margin-left: 75%; }
  .offset-lg-10 {
    margin-left: 83.33333%; }
  .offset-lg-11 {
    margin-left: 91.66667%; }
  .g-lg-0,
  .gx-lg-0 {
    --bs-gutter-x: 0; }
  .g-lg-0,
  .gy-lg-0 {
    --bs-gutter-y: 0; }
  .g-lg-1,
  .gx-lg-1 {
    --bs-gutter-x: 0.25rem; }
  .g-lg-1,
  .gy-lg-1 {
    --bs-gutter-y: 0.25rem; }
  .g-lg-2,
  .gx-lg-2 {
    --bs-gutter-x: 0.5rem; }
  .g-lg-2,
  .gy-lg-2 {
    --bs-gutter-y: 0.5rem; }
  .g-lg-3,
  .gx-lg-3 {
    --bs-gutter-x: 1rem; }
  .g-lg-3,
  .gy-lg-3 {
    --bs-gutter-y: 1rem; }
  .g-lg-4,
  .gx-lg-4 {
    --bs-gutter-x: 1.5rem; }
  .g-lg-4,
  .gy-lg-4 {
    --bs-gutter-y: 1.5rem; }
  .g-lg-5,
  .gx-lg-5 {
    --bs-gutter-x: 3rem; }
  .g-lg-5,
  .gy-lg-5 {
    --bs-gutter-y: 3rem; } }

@media (min-width: 1200px) {
  .col-xl {
    flex: 1 0 0%; }
  .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto; }
  .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%; }
  .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%; }
  .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.33333%; }
  .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%; }
  .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%; }
  .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.66667%; }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto; }
  .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333%; }
  .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66667%; }
  .col-xl-3 {
    flex: 0 0 auto;
    width: 25%; }
  .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333%; }
  .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66667%; }
  .col-xl-6 {
    flex: 0 0 auto;
    width: 50%; }
  .col-xl-7 {
    flex: 0 0 auto;
    width: 58.33333%; }
  .col-xl-8 {
    flex: 0 0 auto;
    width: 66.66667%; }
  .col-xl-9 {
    flex: 0 0 auto;
    width: 75%; }
  .col-xl-10 {
    flex: 0 0 auto;
    width: 83.33333%; }
  .col-xl-11 {
    flex: 0 0 auto;
    width: 91.66667%; }
  .col-xl-12 {
    flex: 0 0 auto;
    width: 100%; }
  .offset-xl-0 {
    margin-left: 0; }
  .offset-xl-1 {
    margin-left: 8.33333%; }
  .offset-xl-2 {
    margin-left: 16.66667%; }
  .offset-xl-3 {
    margin-left: 25%; }
  .offset-xl-4 {
    margin-left: 33.33333%; }
  .offset-xl-5 {
    margin-left: 41.66667%; }
  .offset-xl-6 {
    margin-left: 50%; }
  .offset-xl-7 {
    margin-left: 58.33333%; }
  .offset-xl-8 {
    margin-left: 66.66667%; }
  .offset-xl-9 {
    margin-left: 75%; }
  .offset-xl-10 {
    margin-left: 83.33333%; }
  .offset-xl-11 {
    margin-left: 91.66667%; }
  .g-xl-0,
  .gx-xl-0 {
    --bs-gutter-x: 0; }
  .g-xl-0,
  .gy-xl-0 {
    --bs-gutter-y: 0; }
  .g-xl-1,
  .gx-xl-1 {
    --bs-gutter-x: 0.25rem; }
  .g-xl-1,
  .gy-xl-1 {
    --bs-gutter-y: 0.25rem; }
  .g-xl-2,
  .gx-xl-2 {
    --bs-gutter-x: 0.5rem; }
  .g-xl-2,
  .gy-xl-2 {
    --bs-gutter-y: 0.5rem; }
  .g-xl-3,
  .gx-xl-3 {
    --bs-gutter-x: 1rem; }
  .g-xl-3,
  .gy-xl-3 {
    --bs-gutter-y: 1rem; }
  .g-xl-4,
  .gx-xl-4 {
    --bs-gutter-x: 1.5rem; }
  .g-xl-4,
  .gy-xl-4 {
    --bs-gutter-y: 1.5rem; }
  .g-xl-5,
  .gx-xl-5 {
    --bs-gutter-x: 3rem; }
  .g-xl-5,
  .gy-xl-5 {
    --bs-gutter-y: 3rem; } }

@media (min-width: 1400px) {
  .col-xxl {
    flex: 1 0 0%; }
  .row-cols-xxl-auto > * {
    flex: 0 0 auto;
    width: auto; }
  .row-cols-xxl-1 > * {
    flex: 0 0 auto;
    width: 100%; }
  .row-cols-xxl-2 > * {
    flex: 0 0 auto;
    width: 50%; }
  .row-cols-xxl-3 > * {
    flex: 0 0 auto;
    width: 33.33333%; }
  .row-cols-xxl-4 > * {
    flex: 0 0 auto;
    width: 25%; }
  .row-cols-xxl-5 > * {
    flex: 0 0 auto;
    width: 20%; }
  .row-cols-xxl-6 > * {
    flex: 0 0 auto;
    width: 16.66667%; }
  .col-xxl-auto {
    flex: 0 0 auto;
    width: auto; }
  .col-xxl-1 {
    flex: 0 0 auto;
    width: 8.33333%; }
  .col-xxl-2 {
    flex: 0 0 auto;
    width: 16.66667%; }
  .col-xxl-3 {
    flex: 0 0 auto;
    width: 25%; }
  .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333%; }
  .col-xxl-5 {
    flex: 0 0 auto;
    width: 41.66667%; }
  .col-xxl-6 {
    flex: 0 0 auto;
    width: 50%; }
  .col-xxl-7 {
    flex: 0 0 auto;
    width: 58.33333%; }
  .col-xxl-8 {
    flex: 0 0 auto;
    width: 66.66667%; }
  .col-xxl-9 {
    flex: 0 0 auto;
    width: 75%; }
  .col-xxl-10 {
    flex: 0 0 auto;
    width: 83.33333%; }
  .col-xxl-11 {
    flex: 0 0 auto;
    width: 91.66667%; }
  .col-xxl-12 {
    flex: 0 0 auto;
    width: 100%; }
  .offset-xxl-0 {
    margin-left: 0; }
  .offset-xxl-1 {
    margin-left: 8.33333%; }
  .offset-xxl-2 {
    margin-left: 16.66667%; }
  .offset-xxl-3 {
    margin-left: 25%; }
  .offset-xxl-4 {
    margin-left: 33.33333%; }
  .offset-xxl-5 {
    margin-left: 41.66667%; }
  .offset-xxl-6 {
    margin-left: 50%; }
  .offset-xxl-7 {
    margin-left: 58.33333%; }
  .offset-xxl-8 {
    margin-left: 66.66667%; }
  .offset-xxl-9 {
    margin-left: 75%; }
  .offset-xxl-10 {
    margin-left: 83.33333%; }
  .offset-xxl-11 {
    margin-left: 91.66667%; }
  .g-xxl-0,
  .gx-xxl-0 {
    --bs-gutter-x: 0; }
  .g-xxl-0,
  .gy-xxl-0 {
    --bs-gutter-y: 0; }
  .g-xxl-1,
  .gx-xxl-1 {
    --bs-gutter-x: 0.25rem; }
  .g-xxl-1,
  .gy-xxl-1 {
    --bs-gutter-y: 0.25rem; }
  .g-xxl-2,
  .gx-xxl-2 {
    --bs-gutter-x: 0.5rem; }
  .g-xxl-2,
  .gy-xxl-2 {
    --bs-gutter-y: 0.5rem; }
  .g-xxl-3,
  .gx-xxl-3 {
    --bs-gutter-x: 1rem; }
  .g-xxl-3,
  .gy-xxl-3 {
    --bs-gutter-y: 1rem; }
  .g-xxl-4,
  .gx-xxl-4 {
    --bs-gutter-x: 1.5rem; }
  .g-xxl-4,
  .gy-xxl-4 {
    --bs-gutter-y: 1.5rem; }
  .g-xxl-5,
  .gx-xxl-5 {
    --bs-gutter-x: 3rem; }
  .g-xxl-5,
  .gy-xxl-5 {
    --bs-gutter-y: 3rem; } }

.d-inline {
  display: inline !important; }

.d-inline-block {
  display: inline-block !important; }

.d-block {
  display: block !important; }

.d-grid {
  display: grid !important; }

.d-table {
  display: table !important; }

.d-table-row {
  display: table-row !important; }

.d-table-cell {
  display: table-cell !important; }

.d-flex {
  display: flex !important; }

.d-inline-flex {
  display: inline-flex !important; }

.d-none {
  display: none !important; }

.flex-fill {
  flex: 1 1 auto !important; }

.flex-row {
  flex-direction: row !important; }

.flex-column {
  flex-direction: column !important; }

.flex-row-reverse {
  flex-direction: row-reverse !important; }

.flex-column-reverse {
  flex-direction: column-reverse !important; }

.flex-grow-0 {
  flex-grow: 0 !important; }

.flex-grow-1 {
  flex-grow: 1 !important; }

.flex-shrink-0 {
  flex-shrink: 0 !important; }

.flex-shrink-1 {
  flex-shrink: 1 !important; }

.flex-wrap {
  flex-wrap: wrap !important; }

.flex-nowrap {
  flex-wrap: nowrap !important; }

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important; }

.justify-content-start {
  justify-content: flex-start !important; }

.justify-content-end {
  justify-content: flex-end !important; }

.justify-content-center {
  justify-content: center !important; }

.justify-content-between {
  justify-content: space-between !important; }

.justify-content-around {
  justify-content: space-around !important; }

.justify-content-evenly {
  justify-content: space-evenly !important; }

.align-items-start {
  align-items: flex-start !important; }

.align-items-end {
  align-items: flex-end !important; }

.align-items-center {
  align-items: center !important; }

.align-items-baseline {
  align-items: baseline !important; }

.align-items-stretch {
  align-items: stretch !important; }

.align-content-start {
  align-content: flex-start !important; }

.align-content-end {
  align-content: flex-end !important; }

.align-content-center {
  align-content: center !important; }

.align-content-between {
  align-content: space-between !important; }

.align-content-around {
  align-content: space-around !important; }

.align-content-stretch {
  align-content: stretch !important; }

.align-self-auto {
  align-self: auto !important; }

.align-self-start {
  align-self: flex-start !important; }

.align-self-end {
  align-self: flex-end !important; }

.align-self-center {
  align-self: center !important; }

.align-self-baseline {
  align-self: baseline !important; }

.align-self-stretch {
  align-self: stretch !important; }

.order-first {
  order: -1 !important; }

.order-0 {
  order: 0 !important; }

.order-1 {
  order: 1 !important; }

.order-2 {
  order: 2 !important; }

.order-3 {
  order: 3 !important; }

.order-4 {
  order: 4 !important; }

.order-5 {
  order: 5 !important; }

.order-last {
  order: 6 !important; }

.m-0 {
  margin: 0 !important; }

.m-1 {
  margin: 0.25rem !important; }

.m-2 {
  margin: 0.5rem !important; }

.m-3 {
  margin: 1rem !important; }

.m-4 {
  margin: 1.5rem !important; }

.m-5 {
  margin: 3rem !important; }

.m-auto {
  margin: auto !important; }

.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important; }

.mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0.25rem !important; }

.mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important; }

.mx-3 {
  margin-right: 1rem !important;
  margin-left: 1rem !important; }

.mx-4 {
  margin-right: 1.5rem !important;
  margin-left: 1.5rem !important; }

.mx-5 {
  margin-right: 3rem !important;
  margin-left: 3rem !important; }

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important; }

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important; }

.my-1 {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important; }

.my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important; }

.my-3 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important; }

.my-4 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important; }

.my-5 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important; }

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important; }

.mt-0 {
  margin-top: 0 !important; }

.mt-1 {
  margin-top: 0.25rem !important; }

.mt-2 {
  margin-top: 0.5rem !important; }

.mt-3 {
  margin-top: 1rem !important; }

.mt-4 {
  margin-top: 1.5rem !important; }

.mt-5 {
  margin-top: 3rem !important; }

.mt-auto {
  margin-top: auto !important; }

.me-0 {
  margin-right: 0 !important; }

.me-1 {
  margin-right: 0.25rem !important; }

.me-2 {
  margin-right: 0.5rem !important; }

.me-3 {
  margin-right: 1rem !important; }

.me-4 {
  margin-right: 1.5rem !important; }

.me-5 {
  margin-right: 3rem !important; }

.me-auto {
  margin-right: auto !important; }

.mb-0 {
  margin-bottom: 0 !important; }

.mb-1 {
  margin-bottom: 0.25rem !important; }

.mb-2 {
  margin-bottom: 0.5rem !important; }

.mb-3 {
  margin-bottom: 1rem !important; }

.mb-4 {
  margin-bottom: 1.5rem !important; }

.mb-5 {
  margin-bottom: 3rem !important; }

.mb-auto {
  margin-bottom: auto !important; }

.ms-0 {
  margin-left: 0 !important; }

.ms-1 {
  margin-left: 0.25rem !important; }

.ms-2 {
  margin-left: 0.5rem !important; }

.ms-3 {
  margin-left: 1rem !important; }

.ms-4 {
  margin-left: 1.5rem !important; }

.ms-5 {
  margin-left: 3rem !important; }

.ms-auto {
  margin-left: auto !important; }

.p-0 {
  padding: 0 !important; }

.p-1 {
  padding: 0.25rem !important; }

.p-2 {
  padding: 0.5rem !important; }

.p-3 {
  padding: 1rem !important; }

.p-4 {
  padding: 1.5rem !important; }

.p-5 {
  padding: 3rem !important; }

.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important; }

.px-1 {
  padding-right: 0.25rem !important;
  padding-left: 0.25rem !important; }

.px-2 {
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important; }

.px-3 {
  padding-right: 1rem !important;
  padding-left: 1rem !important; }

.px-4 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important; }

.px-5 {
  padding-right: 3rem !important;
  padding-left: 3rem !important; }

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important; }

.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important; }

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important; }

.py-3 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important; }

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important; }

.py-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important; }

.pt-0 {
  padding-top: 0 !important; }

.pt-1 {
  padding-top: 0.25rem !important; }

.pt-2 {
  padding-top: 0.5rem !important; }

.pt-3 {
  padding-top: 1rem !important; }

.pt-4 {
  padding-top: 1.5rem !important; }

.pt-5 {
  padding-top: 3rem !important; }

.pe-0 {
  padding-right: 0 !important; }

.pe-1 {
  padding-right: 0.25rem !important; }

.pe-2 {
  padding-right: 0.5rem !important; }

.pe-3 {
  padding-right: 1rem !important; }

.pe-4 {
  padding-right: 1.5rem !important; }

.pe-5 {
  padding-right: 3rem !important; }

.pb-0 {
  padding-bottom: 0 !important; }

.pb-1 {
  padding-bottom: 0.25rem !important; }

.pb-2 {
  padding-bottom: 0.5rem !important; }

.pb-3 {
  padding-bottom: 1rem !important; }

.pb-4 {
  padding-bottom: 1.5rem !important; }

.pb-5 {
  padding-bottom: 3rem !important; }

.ps-0 {
  padding-left: 0 !important; }

.ps-1 {
  padding-left: 0.25rem !important; }

.ps-2 {
  padding-left: 0.5rem !important; }

.ps-3 {
  padding-left: 1rem !important; }

.ps-4 {
  padding-left: 1.5rem !important; }

.ps-5 {
  padding-left: 3rem !important; }

@media (min-width: 576px) {
  .d-sm-inline {
    display: inline !important; }
  .d-sm-inline-block {
    display: inline-block !important; }
  .d-sm-block {
    display: block !important; }
  .d-sm-grid {
    display: grid !important; }
  .d-sm-table {
    display: table !important; }
  .d-sm-table-row {
    display: table-row !important; }
  .d-sm-table-cell {
    display: table-cell !important; }
  .d-sm-flex {
    display: flex !important; }
  .d-sm-inline-flex {
    display: inline-flex !important; }
  .d-sm-none {
    display: none !important; }
  .flex-sm-fill {
    flex: 1 1 auto !important; }
  .flex-sm-row {
    flex-direction: row !important; }
  .flex-sm-column {
    flex-direction: column !important; }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important; }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important; }
  .flex-sm-grow-0 {
    flex-grow: 0 !important; }
  .flex-sm-grow-1 {
    flex-grow: 1 !important; }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important; }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important; }
  .flex-sm-wrap {
    flex-wrap: wrap !important; }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important; }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important; }
  .justify-content-sm-start {
    justify-content: flex-start !important; }
  .justify-content-sm-end {
    justify-content: flex-end !important; }
  .justify-content-sm-center {
    justify-content: center !important; }
  .justify-content-sm-between {
    justify-content: space-between !important; }
  .justify-content-sm-around {
    justify-content: space-around !important; }
  .justify-content-sm-evenly {
    justify-content: space-evenly !important; }
  .align-items-sm-start {
    align-items: flex-start !important; }
  .align-items-sm-end {
    align-items: flex-end !important; }
  .align-items-sm-center {
    align-items: center !important; }
  .align-items-sm-baseline {
    align-items: baseline !important; }
  .align-items-sm-stretch {
    align-items: stretch !important; }
  .align-content-sm-start {
    align-content: flex-start !important; }
  .align-content-sm-end {
    align-content: flex-end !important; }
  .align-content-sm-center {
    align-content: center !important; }
  .align-content-sm-between {
    align-content: space-between !important; }
  .align-content-sm-around {
    align-content: space-around !important; }
  .align-content-sm-stretch {
    align-content: stretch !important; }
  .align-self-sm-auto {
    align-self: auto !important; }
  .align-self-sm-start {
    align-self: flex-start !important; }
  .align-self-sm-end {
    align-self: flex-end !important; }
  .align-self-sm-center {
    align-self: center !important; }
  .align-self-sm-baseline {
    align-self: baseline !important; }
  .align-self-sm-stretch {
    align-self: stretch !important; }
  .order-sm-first {
    order: -1 !important; }
  .order-sm-0 {
    order: 0 !important; }
  .order-sm-1 {
    order: 1 !important; }
  .order-sm-2 {
    order: 2 !important; }
  .order-sm-3 {
    order: 3 !important; }
  .order-sm-4 {
    order: 4 !important; }
  .order-sm-5 {
    order: 5 !important; }
  .order-sm-last {
    order: 6 !important; }
  .m-sm-0 {
    margin: 0 !important; }
  .m-sm-1 {
    margin: 0.25rem !important; }
  .m-sm-2 {
    margin: 0.5rem !important; }
  .m-sm-3 {
    margin: 1rem !important; }
  .m-sm-4 {
    margin: 1.5rem !important; }
  .m-sm-5 {
    margin: 3rem !important; }
  .m-sm-auto {
    margin: auto !important; }
  .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }
  .mx-sm-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important; }
  .mx-sm-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important; }
  .mx-sm-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important; }
  .mx-sm-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important; }
  .mx-sm-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important; }
  .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important; }
  .my-sm-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important; }
  .my-sm-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important; }
  .my-sm-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important; }
  .my-sm-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important; }
  .my-sm-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important; }
  .my-sm-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important; }
  .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important; }
  .mt-sm-0 {
    margin-top: 0 !important; }
  .mt-sm-1 {
    margin-top: 0.25rem !important; }
  .mt-sm-2 {
    margin-top: 0.5rem !important; }
  .mt-sm-3 {
    margin-top: 1rem !important; }
  .mt-sm-4 {
    margin-top: 1.5rem !important; }
  .mt-sm-5 {
    margin-top: 3rem !important; }
  .mt-sm-auto {
    margin-top: auto !important; }
  .me-sm-0 {
    margin-right: 0 !important; }
  .me-sm-1 {
    margin-right: 0.25rem !important; }
  .me-sm-2 {
    margin-right: 0.5rem !important; }
  .me-sm-3 {
    margin-right: 1rem !important; }
  .me-sm-4 {
    margin-right: 1.5rem !important; }
  .me-sm-5 {
    margin-right: 3rem !important; }
  .me-sm-auto {
    margin-right: auto !important; }
  .mb-sm-0 {
    margin-bottom: 0 !important; }
  .mb-sm-1 {
    margin-bottom: 0.25rem !important; }
  .mb-sm-2 {
    margin-bottom: 0.5rem !important; }
  .mb-sm-3 {
    margin-bottom: 1rem !important; }
  .mb-sm-4 {
    margin-bottom: 1.5rem !important; }
  .mb-sm-5 {
    margin-bottom: 3rem !important; }
  .mb-sm-auto {
    margin-bottom: auto !important; }
  .ms-sm-0 {
    margin-left: 0 !important; }
  .ms-sm-1 {
    margin-left: 0.25rem !important; }
  .ms-sm-2 {
    margin-left: 0.5rem !important; }
  .ms-sm-3 {
    margin-left: 1rem !important; }
  .ms-sm-4 {
    margin-left: 1.5rem !important; }
  .ms-sm-5 {
    margin-left: 3rem !important; }
  .ms-sm-auto {
    margin-left: auto !important; }
  .p-sm-0 {
    padding: 0 !important; }
  .p-sm-1 {
    padding: 0.25rem !important; }
  .p-sm-2 {
    padding: 0.5rem !important; }
  .p-sm-3 {
    padding: 1rem !important; }
  .p-sm-4 {
    padding: 1.5rem !important; }
  .p-sm-5 {
    padding: 3rem !important; }
  .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }
  .px-sm-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important; }
  .px-sm-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important; }
  .px-sm-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important; }
  .px-sm-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important; }
  .px-sm-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important; }
  .py-sm-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important; }
  .py-sm-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important; }
  .py-sm-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important; }
  .py-sm-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important; }
  .py-sm-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important; }
  .py-sm-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important; }
  .pt-sm-0 {
    padding-top: 0 !important; }
  .pt-sm-1 {
    padding-top: 0.25rem !important; }
  .pt-sm-2 {
    padding-top: 0.5rem !important; }
  .pt-sm-3 {
    padding-top: 1rem !important; }
  .pt-sm-4 {
    padding-top: 1.5rem !important; }
  .pt-sm-5 {
    padding-top: 3rem !important; }
  .pe-sm-0 {
    padding-right: 0 !important; }
  .pe-sm-1 {
    padding-right: 0.25rem !important; }
  .pe-sm-2 {
    padding-right: 0.5rem !important; }
  .pe-sm-3 {
    padding-right: 1rem !important; }
  .pe-sm-4 {
    padding-right: 1.5rem !important; }
  .pe-sm-5 {
    padding-right: 3rem !important; }
  .pb-sm-0 {
    padding-bottom: 0 !important; }
  .pb-sm-1 {
    padding-bottom: 0.25rem !important; }
  .pb-sm-2 {
    padding-bottom: 0.5rem !important; }
  .pb-sm-3 {
    padding-bottom: 1rem !important; }
  .pb-sm-4 {
    padding-bottom: 1.5rem !important; }
  .pb-sm-5 {
    padding-bottom: 3rem !important; }
  .ps-sm-0 {
    padding-left: 0 !important; }
  .ps-sm-1 {
    padding-left: 0.25rem !important; }
  .ps-sm-2 {
    padding-left: 0.5rem !important; }
  .ps-sm-3 {
    padding-left: 1rem !important; }
  .ps-sm-4 {
    padding-left: 1.5rem !important; }
  .ps-sm-5 {
    padding-left: 3rem !important; } }

@media (min-width: 768px) {
  .d-md-inline {
    display: inline !important; }
  .d-md-inline-block {
    display: inline-block !important; }
  .d-md-block {
    display: block !important; }
  .d-md-grid {
    display: grid !important; }
  .d-md-table {
    display: table !important; }
  .d-md-table-row {
    display: table-row !important; }
  .d-md-table-cell {
    display: table-cell !important; }
  .d-md-flex {
    display: flex !important; }
  .d-md-inline-flex {
    display: inline-flex !important; }
  .d-md-none {
    display: none !important; }
  .flex-md-fill {
    flex: 1 1 auto !important; }
  .flex-md-row {
    flex-direction: row !important; }
  .flex-md-column {
    flex-direction: column !important; }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important; }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important; }
  .flex-md-grow-0 {
    flex-grow: 0 !important; }
  .flex-md-grow-1 {
    flex-grow: 1 !important; }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important; }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important; }
  .flex-md-wrap {
    flex-wrap: wrap !important; }
  .flex-md-nowrap {
    flex-wrap: nowrap !important; }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important; }
  .justify-content-md-start {
    justify-content: flex-start !important; }
  .justify-content-md-end {
    justify-content: flex-end !important; }
  .justify-content-md-center {
    justify-content: center !important; }
  .justify-content-md-between {
    justify-content: space-between !important; }
  .justify-content-md-around {
    justify-content: space-around !important; }
  .justify-content-md-evenly {
    justify-content: space-evenly !important; }
  .align-items-md-start {
    align-items: flex-start !important; }
  .align-items-md-end {
    align-items: flex-end !important; }
  .align-items-md-center {
    align-items: center !important; }
  .align-items-md-baseline {
    align-items: baseline !important; }
  .align-items-md-stretch {
    align-items: stretch !important; }
  .align-content-md-start {
    align-content: flex-start !important; }
  .align-content-md-end {
    align-content: flex-end !important; }
  .align-content-md-center {
    align-content: center !important; }
  .align-content-md-between {
    align-content: space-between !important; }
  .align-content-md-around {
    align-content: space-around !important; }
  .align-content-md-stretch {
    align-content: stretch !important; }
  .align-self-md-auto {
    align-self: auto !important; }
  .align-self-md-start {
    align-self: flex-start !important; }
  .align-self-md-end {
    align-self: flex-end !important; }
  .align-self-md-center {
    align-self: center !important; }
  .align-self-md-baseline {
    align-self: baseline !important; }
  .align-self-md-stretch {
    align-self: stretch !important; }
  .order-md-first {
    order: -1 !important; }
  .order-md-0 {
    order: 0 !important; }
  .order-md-1 {
    order: 1 !important; }
  .order-md-2 {
    order: 2 !important; }
  .order-md-3 {
    order: 3 !important; }
  .order-md-4 {
    order: 4 !important; }
  .order-md-5 {
    order: 5 !important; }
  .order-md-last {
    order: 6 !important; }
  .m-md-0 {
    margin: 0 !important; }
  .m-md-1 {
    margin: 0.25rem !important; }
  .m-md-2 {
    margin: 0.5rem !important; }
  .m-md-3 {
    margin: 1rem !important; }
  .m-md-4 {
    margin: 1.5rem !important; }
  .m-md-5 {
    margin: 3rem !important; }
  .m-md-auto {
    margin: auto !important; }
  .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }
  .mx-md-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important; }
  .mx-md-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important; }
  .mx-md-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important; }
  .mx-md-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important; }
  .mx-md-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important; }
  .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important; }
  .my-md-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important; }
  .my-md-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important; }
  .my-md-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important; }
  .my-md-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important; }
  .my-md-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important; }
  .my-md-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important; }
  .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important; }
  .mt-md-0 {
    margin-top: 0 !important; }
  .mt-md-1 {
    margin-top: 0.25rem !important; }
  .mt-md-2 {
    margin-top: 0.5rem !important; }
  .mt-md-3 {
    margin-top: 1rem !important; }
  .mt-md-4 {
    margin-top: 1.5rem !important; }
  .mt-md-5 {
    margin-top: 3rem !important; }
  .mt-md-auto {
    margin-top: auto !important; }
  .me-md-0 {
    margin-right: 0 !important; }
  .me-md-1 {
    margin-right: 0.25rem !important; }
  .me-md-2 {
    margin-right: 0.5rem !important; }
  .me-md-3 {
    margin-right: 1rem !important; }
  .me-md-4 {
    margin-right: 1.5rem !important; }
  .me-md-5 {
    margin-right: 3rem !important; }
  .me-md-auto {
    margin-right: auto !important; }
  .mb-md-0 {
    margin-bottom: 0 !important; }
  .mb-md-1 {
    margin-bottom: 0.25rem !important; }
  .mb-md-2 {
    margin-bottom: 0.5rem !important; }
  .mb-md-3 {
    margin-bottom: 1rem !important; }
  .mb-md-4 {
    margin-bottom: 1.5rem !important; }
  .mb-md-5 {
    margin-bottom: 3rem !important; }
  .mb-md-auto {
    margin-bottom: auto !important; }
  .ms-md-0 {
    margin-left: 0 !important; }
  .ms-md-1 {
    margin-left: 0.25rem !important; }
  .ms-md-2 {
    margin-left: 0.5rem !important; }
  .ms-md-3 {
    margin-left: 1rem !important; }
  .ms-md-4 {
    margin-left: 1.5rem !important; }
  .ms-md-5 {
    margin-left: 3rem !important; }
  .ms-md-auto {
    margin-left: auto !important; }
  .p-md-0 {
    padding: 0 !important; }
  .p-md-1 {
    padding: 0.25rem !important; }
  .p-md-2 {
    padding: 0.5rem !important; }
  .p-md-3 {
    padding: 1rem !important; }
  .p-md-4 {
    padding: 1.5rem !important; }
  .p-md-5 {
    padding: 3rem !important; }
  .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }
  .px-md-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important; }
  .px-md-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important; }
  .px-md-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important; }
  .px-md-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important; }
  .px-md-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important; }
  .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important; }
  .py-md-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important; }
  .py-md-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important; }
  .py-md-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important; }
  .py-md-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important; }
  .py-md-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important; }
  .pt-md-0 {
    padding-top: 0 !important; }
  .pt-md-1 {
    padding-top: 0.25rem !important; }
  .pt-md-2 {
    padding-top: 0.5rem !important; }
  .pt-md-3 {
    padding-top: 1rem !important; }
  .pt-md-4 {
    padding-top: 1.5rem !important; }
  .pt-md-5 {
    padding-top: 3rem !important; }
  .pe-md-0 {
    padding-right: 0 !important; }
  .pe-md-1 {
    padding-right: 0.25rem !important; }
  .pe-md-2 {
    padding-right: 0.5rem !important; }
  .pe-md-3 {
    padding-right: 1rem !important; }
  .pe-md-4 {
    padding-right: 1.5rem !important; }
  .pe-md-5 {
    padding-right: 3rem !important; }
  .pb-md-0 {
    padding-bottom: 0 !important; }
  .pb-md-1 {
    padding-bottom: 0.25rem !important; }
  .pb-md-2 {
    padding-bottom: 0.5rem !important; }
  .pb-md-3 {
    padding-bottom: 1rem !important; }
  .pb-md-4 {
    padding-bottom: 1.5rem !important; }
  .pb-md-5 {
    padding-bottom: 3rem !important; }
  .ps-md-0 {
    padding-left: 0 !important; }
  .ps-md-1 {
    padding-left: 0.25rem !important; }
  .ps-md-2 {
    padding-left: 0.5rem !important; }
  .ps-md-3 {
    padding-left: 1rem !important; }
  .ps-md-4 {
    padding-left: 1.5rem !important; }
  .ps-md-5 {
    padding-left: 3rem !important; } }

@media (min-width: 992px) {
  .d-lg-inline {
    display: inline !important; }
  .d-lg-inline-block {
    display: inline-block !important; }
  .d-lg-block {
    display: block !important; }
  .d-lg-grid {
    display: grid !important; }
  .d-lg-table {
    display: table !important; }
  .d-lg-table-row {
    display: table-row !important; }
  .d-lg-table-cell {
    display: table-cell !important; }
  .d-lg-flex {
    display: flex !important; }
  .d-lg-inline-flex {
    display: inline-flex !important; }
  .d-lg-none {
    display: none !important; }
  .flex-lg-fill {
    flex: 1 1 auto !important; }
  .flex-lg-row {
    flex-direction: row !important; }
  .flex-lg-column {
    flex-direction: column !important; }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important; }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important; }
  .flex-lg-grow-0 {
    flex-grow: 0 !important; }
  .flex-lg-grow-1 {
    flex-grow: 1 !important; }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important; }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important; }
  .flex-lg-wrap {
    flex-wrap: wrap !important; }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important; }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important; }
  .justify-content-lg-start {
    justify-content: flex-start !important; }
  .justify-content-lg-end {
    justify-content: flex-end !important; }
  .justify-content-lg-center {
    justify-content: center !important; }
  .justify-content-lg-between {
    justify-content: space-between !important; }
  .justify-content-lg-around {
    justify-content: space-around !important; }
  .justify-content-lg-evenly {
    justify-content: space-evenly !important; }
  .align-items-lg-start {
    align-items: flex-start !important; }
  .align-items-lg-end {
    align-items: flex-end !important; }
  .align-items-lg-center {
    align-items: center !important; }
  .align-items-lg-baseline {
    align-items: baseline !important; }
  .align-items-lg-stretch {
    align-items: stretch !important; }
  .align-content-lg-start {
    align-content: flex-start !important; }
  .align-content-lg-end {
    align-content: flex-end !important; }
  .align-content-lg-center {
    align-content: center !important; }
  .align-content-lg-between {
    align-content: space-between !important; }
  .align-content-lg-around {
    align-content: space-around !important; }
  .align-content-lg-stretch {
    align-content: stretch !important; }
  .align-self-lg-auto {
    align-self: auto !important; }
  .align-self-lg-start {
    align-self: flex-start !important; }
  .align-self-lg-end {
    align-self: flex-end !important; }
  .align-self-lg-center {
    align-self: center !important; }
  .align-self-lg-baseline {
    align-self: baseline !important; }
  .align-self-lg-stretch {
    align-self: stretch !important; }
  .order-lg-first {
    order: -1 !important; }
  .order-lg-0 {
    order: 0 !important; }
  .order-lg-1 {
    order: 1 !important; }
  .order-lg-2 {
    order: 2 !important; }
  .order-lg-3 {
    order: 3 !important; }
  .order-lg-4 {
    order: 4 !important; }
  .order-lg-5 {
    order: 5 !important; }
  .order-lg-last {
    order: 6 !important; }
  .m-lg-0 {
    margin: 0 !important; }
  .m-lg-1 {
    margin: 0.25rem !important; }
  .m-lg-2 {
    margin: 0.5rem !important; }
  .m-lg-3 {
    margin: 1rem !important; }
  .m-lg-4 {
    margin: 1.5rem !important; }
  .m-lg-5 {
    margin: 3rem !important; }
  .m-lg-auto {
    margin: auto !important; }
  .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }
  .mx-lg-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important; }
  .mx-lg-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important; }
  .mx-lg-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important; }
  .mx-lg-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important; }
  .mx-lg-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important; }
  .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important; }
  .my-lg-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important; }
  .my-lg-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important; }
  .my-lg-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important; }
  .my-lg-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important; }
  .my-lg-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important; }
  .my-lg-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important; }
  .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important; }
  .mt-lg-0 {
    margin-top: 0 !important; }
  .mt-lg-1 {
    margin-top: 0.25rem !important; }
  .mt-lg-2 {
    margin-top: 0.5rem !important; }
  .mt-lg-3 {
    margin-top: 1rem !important; }
  .mt-lg-4 {
    margin-top: 1.5rem !important; }
  .mt-lg-5 {
    margin-top: 3rem !important; }
  .mt-lg-auto {
    margin-top: auto !important; }
  .me-lg-0 {
    margin-right: 0 !important; }
  .me-lg-1 {
    margin-right: 0.25rem !important; }
  .me-lg-2 {
    margin-right: 0.5rem !important; }
  .me-lg-3 {
    margin-right: 1rem !important; }
  .me-lg-4 {
    margin-right: 1.5rem !important; }
  .me-lg-5 {
    margin-right: 3rem !important; }
  .me-lg-auto {
    margin-right: auto !important; }
  .mb-lg-0 {
    margin-bottom: 0 !important; }
  .mb-lg-1 {
    margin-bottom: 0.25rem !important; }
  .mb-lg-2 {
    margin-bottom: 0.5rem !important; }
  .mb-lg-3 {
    margin-bottom: 1rem !important; }
  .mb-lg-4 {
    margin-bottom: 1.5rem !important; }
  .mb-lg-5 {
    margin-bottom: 3rem !important; }
  .mb-lg-auto {
    margin-bottom: auto !important; }
  .ms-lg-0 {
    margin-left: 0 !important; }
  .ms-lg-1 {
    margin-left: 0.25rem !important; }
  .ms-lg-2 {
    margin-left: 0.5rem !important; }
  .ms-lg-3 {
    margin-left: 1rem !important; }
  .ms-lg-4 {
    margin-left: 1.5rem !important; }
  .ms-lg-5 {
    margin-left: 3rem !important; }
  .ms-lg-auto {
    margin-left: auto !important; }
  .p-lg-0 {
    padding: 0 !important; }
  .p-lg-1 {
    padding: 0.25rem !important; }
  .p-lg-2 {
    padding: 0.5rem !important; }
  .p-lg-3 {
    padding: 1rem !important; }
  .p-lg-4 {
    padding: 1.5rem !important; }
  .p-lg-5 {
    padding: 3rem !important; }
  .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }
  .px-lg-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important; }
  .px-lg-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important; }
  .px-lg-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important; }
  .px-lg-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important; }
  .px-lg-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important; }
  .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important; }
  .py-lg-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important; }
  .py-lg-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important; }
  .py-lg-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important; }
  .py-lg-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important; }
  .py-lg-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important; }
  .pt-lg-0 {
    padding-top: 0 !important; }
  .pt-lg-1 {
    padding-top: 0.25rem !important; }
  .pt-lg-2 {
    padding-top: 0.5rem !important; }
  .pt-lg-3 {
    padding-top: 1rem !important; }
  .pt-lg-4 {
    padding-top: 1.5rem !important; }
  .pt-lg-5 {
    padding-top: 3rem !important; }
  .pe-lg-0 {
    padding-right: 0 !important; }
  .pe-lg-1 {
    padding-right: 0.25rem !important; }
  .pe-lg-2 {
    padding-right: 0.5rem !important; }
  .pe-lg-3 {
    padding-right: 1rem !important; }
  .pe-lg-4 {
    padding-right: 1.5rem !important; }
  .pe-lg-5 {
    padding-right: 3rem !important; }
  .pb-lg-0 {
    padding-bottom: 0 !important; }
  .pb-lg-1 {
    padding-bottom: 0.25rem !important; }
  .pb-lg-2 {
    padding-bottom: 0.5rem !important; }
  .pb-lg-3 {
    padding-bottom: 1rem !important; }
  .pb-lg-4 {
    padding-bottom: 1.5rem !important; }
  .pb-lg-5 {
    padding-bottom: 3rem !important; }
  .ps-lg-0 {
    padding-left: 0 !important; }
  .ps-lg-1 {
    padding-left: 0.25rem !important; }
  .ps-lg-2 {
    padding-left: 0.5rem !important; }
  .ps-lg-3 {
    padding-left: 1rem !important; }
  .ps-lg-4 {
    padding-left: 1.5rem !important; }
  .ps-lg-5 {
    padding-left: 3rem !important; } }

@media (min-width: 1200px) {
  .d-xl-inline {
    display: inline !important; }
  .d-xl-inline-block {
    display: inline-block !important; }
  .d-xl-block {
    display: block !important; }
  .d-xl-grid {
    display: grid !important; }
  .d-xl-table {
    display: table !important; }
  .d-xl-table-row {
    display: table-row !important; }
  .d-xl-table-cell {
    display: table-cell !important; }
  .d-xl-flex {
    display: flex !important; }
  .d-xl-inline-flex {
    display: inline-flex !important; }
  .d-xl-none {
    display: none !important; }
  .flex-xl-fill {
    flex: 1 1 auto !important; }
  .flex-xl-row {
    flex-direction: row !important; }
  .flex-xl-column {
    flex-direction: column !important; }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important; }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important; }
  .flex-xl-grow-0 {
    flex-grow: 0 !important; }
  .flex-xl-grow-1 {
    flex-grow: 1 !important; }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important; }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important; }
  .flex-xl-wrap {
    flex-wrap: wrap !important; }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important; }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important; }
  .justify-content-xl-start {
    justify-content: flex-start !important; }
  .justify-content-xl-end {
    justify-content: flex-end !important; }
  .justify-content-xl-center {
    justify-content: center !important; }
  .justify-content-xl-between {
    justify-content: space-between !important; }
  .justify-content-xl-around {
    justify-content: space-around !important; }
  .justify-content-xl-evenly {
    justify-content: space-evenly !important; }
  .align-items-xl-start {
    align-items: flex-start !important; }
  .align-items-xl-end {
    align-items: flex-end !important; }
  .align-items-xl-center {
    align-items: center !important; }
  .align-items-xl-baseline {
    align-items: baseline !important; }
  .align-items-xl-stretch {
    align-items: stretch !important; }
  .align-content-xl-start {
    align-content: flex-start !important; }
  .align-content-xl-end {
    align-content: flex-end !important; }
  .align-content-xl-center {
    align-content: center !important; }
  .align-content-xl-between {
    align-content: space-between !important; }
  .align-content-xl-around {
    align-content: space-around !important; }
  .align-content-xl-stretch {
    align-content: stretch !important; }
  .align-self-xl-auto {
    align-self: auto !important; }
  .align-self-xl-start {
    align-self: flex-start !important; }
  .align-self-xl-end {
    align-self: flex-end !important; }
  .align-self-xl-center {
    align-self: center !important; }
  .align-self-xl-baseline {
    align-self: baseline !important; }
  .align-self-xl-stretch {
    align-self: stretch !important; }
  .order-xl-first {
    order: -1 !important; }
  .order-xl-0 {
    order: 0 !important; }
  .order-xl-1 {
    order: 1 !important; }
  .order-xl-2 {
    order: 2 !important; }
  .order-xl-3 {
    order: 3 !important; }
  .order-xl-4 {
    order: 4 !important; }
  .order-xl-5 {
    order: 5 !important; }
  .order-xl-last {
    order: 6 !important; }
  .m-xl-0 {
    margin: 0 !important; }
  .m-xl-1 {
    margin: 0.25rem !important; }
  .m-xl-2 {
    margin: 0.5rem !important; }
  .m-xl-3 {
    margin: 1rem !important; }
  .m-xl-4 {
    margin: 1.5rem !important; }
  .m-xl-5 {
    margin: 3rem !important; }
  .m-xl-auto {
    margin: auto !important; }
  .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }
  .mx-xl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important; }
  .mx-xl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important; }
  .mx-xl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important; }
  .mx-xl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important; }
  .mx-xl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important; }
  .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important; }
  .my-xl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important; }
  .my-xl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important; }
  .my-xl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important; }
  .my-xl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important; }
  .my-xl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important; }
  .my-xl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important; }
  .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important; }
  .mt-xl-0 {
    margin-top: 0 !important; }
  .mt-xl-1 {
    margin-top: 0.25rem !important; }
  .mt-xl-2 {
    margin-top: 0.5rem !important; }
  .mt-xl-3 {
    margin-top: 1rem !important; }
  .mt-xl-4 {
    margin-top: 1.5rem !important; }
  .mt-xl-5 {
    margin-top: 3rem !important; }
  .mt-xl-auto {
    margin-top: auto !important; }
  .me-xl-0 {
    margin-right: 0 !important; }
  .me-xl-1 {
    margin-right: 0.25rem !important; }
  .me-xl-2 {
    margin-right: 0.5rem !important; }
  .me-xl-3 {
    margin-right: 1rem !important; }
  .me-xl-4 {
    margin-right: 1.5rem !important; }
  .me-xl-5 {
    margin-right: 3rem !important; }
  .me-xl-auto {
    margin-right: auto !important; }
  .mb-xl-0 {
    margin-bottom: 0 !important; }
  .mb-xl-1 {
    margin-bottom: 0.25rem !important; }
  .mb-xl-2 {
    margin-bottom: 0.5rem !important; }
  .mb-xl-3 {
    margin-bottom: 1rem !important; }
  .mb-xl-4 {
    margin-bottom: 1.5rem !important; }
  .mb-xl-5 {
    margin-bottom: 3rem !important; }
  .mb-xl-auto {
    margin-bottom: auto !important; }
  .ms-xl-0 {
    margin-left: 0 !important; }
  .ms-xl-1 {
    margin-left: 0.25rem !important; }
  .ms-xl-2 {
    margin-left: 0.5rem !important; }
  .ms-xl-3 {
    margin-left: 1rem !important; }
  .ms-xl-4 {
    margin-left: 1.5rem !important; }
  .ms-xl-5 {
    margin-left: 3rem !important; }
  .ms-xl-auto {
    margin-left: auto !important; }
  .p-xl-0 {
    padding: 0 !important; }
  .p-xl-1 {
    padding: 0.25rem !important; }
  .p-xl-2 {
    padding: 0.5rem !important; }
  .p-xl-3 {
    padding: 1rem !important; }
  .p-xl-4 {
    padding: 1.5rem !important; }
  .p-xl-5 {
    padding: 3rem !important; }
  .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }
  .px-xl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important; }
  .px-xl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important; }
  .px-xl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important; }
  .px-xl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important; }
  .px-xl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important; }
  .py-xl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important; }
  .py-xl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important; }
  .py-xl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important; }
  .py-xl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important; }
  .py-xl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important; }
  .py-xl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important; }
  .pt-xl-0 {
    padding-top: 0 !important; }
  .pt-xl-1 {
    padding-top: 0.25rem !important; }
  .pt-xl-2 {
    padding-top: 0.5rem !important; }
  .pt-xl-3 {
    padding-top: 1rem !important; }
  .pt-xl-4 {
    padding-top: 1.5rem !important; }
  .pt-xl-5 {
    padding-top: 3rem !important; }
  .pe-xl-0 {
    padding-right: 0 !important; }
  .pe-xl-1 {
    padding-right: 0.25rem !important; }
  .pe-xl-2 {
    padding-right: 0.5rem !important; }
  .pe-xl-3 {
    padding-right: 1rem !important; }
  .pe-xl-4 {
    padding-right: 1.5rem !important; }
  .pe-xl-5 {
    padding-right: 3rem !important; }
  .pb-xl-0 {
    padding-bottom: 0 !important; }
  .pb-xl-1 {
    padding-bottom: 0.25rem !important; }
  .pb-xl-2 {
    padding-bottom: 0.5rem !important; }
  .pb-xl-3 {
    padding-bottom: 1rem !important; }
  .pb-xl-4 {
    padding-bottom: 1.5rem !important; }
  .pb-xl-5 {
    padding-bottom: 3rem !important; }
  .ps-xl-0 {
    padding-left: 0 !important; }
  .ps-xl-1 {
    padding-left: 0.25rem !important; }
  .ps-xl-2 {
    padding-left: 0.5rem !important; }
  .ps-xl-3 {
    padding-left: 1rem !important; }
  .ps-xl-4 {
    padding-left: 1.5rem !important; }
  .ps-xl-5 {
    padding-left: 3rem !important; } }

@media (min-width: 1400px) {
  .d-xxl-inline {
    display: inline !important; }
  .d-xxl-inline-block {
    display: inline-block !important; }
  .d-xxl-block {
    display: block !important; }
  .d-xxl-grid {
    display: grid !important; }
  .d-xxl-table {
    display: table !important; }
  .d-xxl-table-row {
    display: table-row !important; }
  .d-xxl-table-cell {
    display: table-cell !important; }
  .d-xxl-flex {
    display: flex !important; }
  .d-xxl-inline-flex {
    display: inline-flex !important; }
  .d-xxl-none {
    display: none !important; }
  .flex-xxl-fill {
    flex: 1 1 auto !important; }
  .flex-xxl-row {
    flex-direction: row !important; }
  .flex-xxl-column {
    flex-direction: column !important; }
  .flex-xxl-row-reverse {
    flex-direction: row-reverse !important; }
  .flex-xxl-column-reverse {
    flex-direction: column-reverse !important; }
  .flex-xxl-grow-0 {
    flex-grow: 0 !important; }
  .flex-xxl-grow-1 {
    flex-grow: 1 !important; }
  .flex-xxl-shrink-0 {
    flex-shrink: 0 !important; }
  .flex-xxl-shrink-1 {
    flex-shrink: 1 !important; }
  .flex-xxl-wrap {
    flex-wrap: wrap !important; }
  .flex-xxl-nowrap {
    flex-wrap: nowrap !important; }
  .flex-xxl-wrap-reverse {
    flex-wrap: wrap-reverse !important; }
  .justify-content-xxl-start {
    justify-content: flex-start !important; }
  .justify-content-xxl-end {
    justify-content: flex-end !important; }
  .justify-content-xxl-center {
    justify-content: center !important; }
  .justify-content-xxl-between {
    justify-content: space-between !important; }
  .justify-content-xxl-around {
    justify-content: space-around !important; }
  .justify-content-xxl-evenly {
    justify-content: space-evenly !important; }
  .align-items-xxl-start {
    align-items: flex-start !important; }
  .align-items-xxl-end {
    align-items: flex-end !important; }
  .align-items-xxl-center {
    align-items: center !important; }
  .align-items-xxl-baseline {
    align-items: baseline !important; }
  .align-items-xxl-stretch {
    align-items: stretch !important; }
  .align-content-xxl-start {
    align-content: flex-start !important; }
  .align-content-xxl-end {
    align-content: flex-end !important; }
  .align-content-xxl-center {
    align-content: center !important; }
  .align-content-xxl-between {
    align-content: space-between !important; }
  .align-content-xxl-around {
    align-content: space-around !important; }
  .align-content-xxl-stretch {
    align-content: stretch !important; }
  .align-self-xxl-auto {
    align-self: auto !important; }
  .align-self-xxl-start {
    align-self: flex-start !important; }
  .align-self-xxl-end {
    align-self: flex-end !important; }
  .align-self-xxl-center {
    align-self: center !important; }
  .align-self-xxl-baseline {
    align-self: baseline !important; }
  .align-self-xxl-stretch {
    align-self: stretch !important; }
  .order-xxl-first {
    order: -1 !important; }
  .order-xxl-0 {
    order: 0 !important; }
  .order-xxl-1 {
    order: 1 !important; }
  .order-xxl-2 {
    order: 2 !important; }
  .order-xxl-3 {
    order: 3 !important; }
  .order-xxl-4 {
    order: 4 !important; }
  .order-xxl-5 {
    order: 5 !important; }
  .order-xxl-last {
    order: 6 !important; }
  .m-xxl-0 {
    margin: 0 !important; }
  .m-xxl-1 {
    margin: 0.25rem !important; }
  .m-xxl-2 {
    margin: 0.5rem !important; }
  .m-xxl-3 {
    margin: 1rem !important; }
  .m-xxl-4 {
    margin: 1.5rem !important; }
  .m-xxl-5 {
    margin: 3rem !important; }
  .m-xxl-auto {
    margin: auto !important; }
  .mx-xxl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }
  .mx-xxl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important; }
  .mx-xxl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important; }
  .mx-xxl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important; }
  .mx-xxl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important; }
  .mx-xxl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important; }
  .mx-xxl-auto {
    margin-right: auto !important;
    margin-left: auto !important; }
  .my-xxl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important; }
  .my-xxl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important; }
  .my-xxl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important; }
  .my-xxl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important; }
  .my-xxl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important; }
  .my-xxl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important; }
  .my-xxl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important; }
  .mt-xxl-0 {
    margin-top: 0 !important; }
  .mt-xxl-1 {
    margin-top: 0.25rem !important; }
  .mt-xxl-2 {
    margin-top: 0.5rem !important; }
  .mt-xxl-3 {
    margin-top: 1rem !important; }
  .mt-xxl-4 {
    margin-top: 1.5rem !important; }
  .mt-xxl-5 {
    margin-top: 3rem !important; }
  .mt-xxl-auto {
    margin-top: auto !important; }
  .me-xxl-0 {
    margin-right: 0 !important; }
  .me-xxl-1 {
    margin-right: 0.25rem !important; }
  .me-xxl-2 {
    margin-right: 0.5rem !important; }
  .me-xxl-3 {
    margin-right: 1rem !important; }
  .me-xxl-4 {
    margin-right: 1.5rem !important; }
  .me-xxl-5 {
    margin-right: 3rem !important; }
  .me-xxl-auto {
    margin-right: auto !important; }
  .mb-xxl-0 {
    margin-bottom: 0 !important; }
  .mb-xxl-1 {
    margin-bottom: 0.25rem !important; }
  .mb-xxl-2 {
    margin-bottom: 0.5rem !important; }
  .mb-xxl-3 {
    margin-bottom: 1rem !important; }
  .mb-xxl-4 {
    margin-bottom: 1.5rem !important; }
  .mb-xxl-5 {
    margin-bottom: 3rem !important; }
  .mb-xxl-auto {
    margin-bottom: auto !important; }
  .ms-xxl-0 {
    margin-left: 0 !important; }
  .ms-xxl-1 {
    margin-left: 0.25rem !important; }
  .ms-xxl-2 {
    margin-left: 0.5rem !important; }
  .ms-xxl-3 {
    margin-left: 1rem !important; }
  .ms-xxl-4 {
    margin-left: 1.5rem !important; }
  .ms-xxl-5 {
    margin-left: 3rem !important; }
  .ms-xxl-auto {
    margin-left: auto !important; }
  .p-xxl-0 {
    padding: 0 !important; }
  .p-xxl-1 {
    padding: 0.25rem !important; }
  .p-xxl-2 {
    padding: 0.5rem !important; }
  .p-xxl-3 {
    padding: 1rem !important; }
  .p-xxl-4 {
    padding: 1.5rem !important; }
  .p-xxl-5 {
    padding: 3rem !important; }
  .px-xxl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }
  .px-xxl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important; }
  .px-xxl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important; }
  .px-xxl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important; }
  .px-xxl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important; }
  .px-xxl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important; }
  .py-xxl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important; }
  .py-xxl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important; }
  .py-xxl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important; }
  .py-xxl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important; }
  .py-xxl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important; }
  .py-xxl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important; }
  .pt-xxl-0 {
    padding-top: 0 !important; }
  .pt-xxl-1 {
    padding-top: 0.25rem !important; }
  .pt-xxl-2 {
    padding-top: 0.5rem !important; }
  .pt-xxl-3 {
    padding-top: 1rem !important; }
  .pt-xxl-4 {
    padding-top: 1.5rem !important; }
  .pt-xxl-5 {
    padding-top: 3rem !important; }
  .pe-xxl-0 {
    padding-right: 0 !important; }
  .pe-xxl-1 {
    padding-right: 0.25rem !important; }
  .pe-xxl-2 {
    padding-right: 0.5rem !important; }
  .pe-xxl-3 {
    padding-right: 1rem !important; }
  .pe-xxl-4 {
    padding-right: 1.5rem !important; }
  .pe-xxl-5 {
    padding-right: 3rem !important; }
  .pb-xxl-0 {
    padding-bottom: 0 !important; }
  .pb-xxl-1 {
    padding-bottom: 0.25rem !important; }
  .pb-xxl-2 {
    padding-bottom: 0.5rem !important; }
  .pb-xxl-3 {
    padding-bottom: 1rem !important; }
  .pb-xxl-4 {
    padding-bottom: 1.5rem !important; }
  .pb-xxl-5 {
    padding-bottom: 3rem !important; }
  .ps-xxl-0 {
    padding-left: 0 !important; }
  .ps-xxl-1 {
    padding-left: 0.25rem !important; }
  .ps-xxl-2 {
    padding-left: 0.5rem !important; }
  .ps-xxl-3 {
    padding-left: 1rem !important; }
  .ps-xxl-4 {
    padding-left: 1.5rem !important; }
  .ps-xxl-5 {
    padding-left: 3rem !important; } }

@media print {
  .d-print-inline {
    display: inline !important; }
  .d-print-inline-block {
    display: inline-block !important; }
  .d-print-block {
    display: block !important; }
  .d-print-grid {
    display: grid !important; }
  .d-print-table {
    display: table !important; }
  .d-print-table-row {
    display: table-row !important; }
  .d-print-table-cell {
    display: table-cell !important; }
  .d-print-flex {
    display: flex !important; }
  .d-print-inline-flex {
    display: inline-flex !important; }
  .d-print-none {
    display: none !important; } }

.lead {
  font-size: 1.25rem;
  font-weight: 300; }

.display-1 {
  font-size: calc(1.625rem + 4.5vw);
  font-weight: 300;
  line-height: 1.2; }
  @media (min-width: 1200px) {
    .display-1 {
      font-size: 5rem; } }

.display-2 {
  font-size: calc(1.575rem + 3.9vw);
  font-weight: 300;
  line-height: 1.2; }
  @media (min-width: 1200px) {
    .display-2 {
      font-size: 4.5rem; } }

.display-3 {
  font-size: calc(1.525rem + 3.3vw);
  font-weight: 300;
  line-height: 1.2; }
  @media (min-width: 1200px) {
    .display-3 {
      font-size: 4rem; } }

.display-4 {
  font-size: calc(1.475rem + 2.7vw);
  font-weight: 300;
  line-height: 1.2; }
  @media (min-width: 1200px) {
    .display-4 {
      font-size: 3.5rem; } }

.display-5 {
  font-size: calc(1.425rem + 2.1vw);
  font-weight: 300;
  line-height: 1.2; }
  @media (min-width: 1200px) {
    .display-5 {
      font-size: 3rem; } }

.display-6 {
  font-size: calc(1.375rem + 1.5vw);
  font-weight: 300;
  line-height: 1.2; }
  @media (min-width: 1200px) {
    .display-6 {
      font-size: 2.5rem; } }

.list-unstyled {
  padding-left: 0;
  list-style: none; }

.list-inline {
  padding-left: 0;
  list-style: none; }

.list-inline-item {
  display: inline-block; }
  .list-inline-item:not(:last-child) {
    margin-right: 0.5rem; }

.initialism {
  font-size: 0.875em;
  text-transform: uppercase; }

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem; }
  .blockquote > :last-child {
    margin-bottom: 0; }

.blockquote-footer {
  margin-top: -1rem;
  margin-bottom: 1rem;
  font-size: 0.875em;
  color: #6c757d; }
  .blockquote-footer::before {
    content: "\2014\00A0"; }

.fade {
  transition: opacity 0.15s linear; }
  @media (prefers-reduced-motion: reduce) {
    .fade {
      transition: none; } }
  .fade:not(.show) {
    opacity: 0; }

.collapse:not(.show) {
  display: none; }

.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease; }
  @media (prefers-reduced-motion: reduce) {
    .collapsing {
      transition: none; } }
  .collapsing.collapse-horizontal {
    width: 0;
    height: auto;
    transition: width 0.35s ease; }
    @media (prefers-reduced-motion: reduce) {
      .collapsing.collapse-horizontal {
        transition: none; } }

.accordion-button {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 1rem 1.25rem;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  background-color: #fff;
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease; }
  @media (prefers-reduced-motion: reduce) {
    .accordion-button {
      transition: none; } }
  .accordion-button:not(.collapsed) {
    color: #0c63e4;
    background-color: #e7f1ff;
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.125); }
    .accordion-button:not(.collapsed)::after {
      background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 16 16%27 fill=%27%230c63e4%27%3e%3cpath fill-rule=%27evenodd%27 d=%27M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z%27/%3e%3c/svg%3e");
      transform: rotate(-180deg); }
  .accordion-button::after {
    flex-shrink: 0;
    width: 1.25rem;
    height: 1.25rem;
    margin-left: auto;
    content: "";
    background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 16 16%27 fill=%27%23212529%27%3e%3cpath fill-rule=%27evenodd%27 d=%27M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z%27/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-size: 1.25rem;
    transition: transform 0.2s ease-in-out; }
    @media (prefers-reduced-motion: reduce) {
      .accordion-button::after {
        transition: none; } }
  .accordion-button:hover {
    z-index: 2; }
  .accordion-button:focus {
    z-index: 3;
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25); }

.accordion-header {
  margin-bottom: 0; }

.accordion-item {
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.125); }
  .accordion-item:first-of-type {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem; }
    .accordion-item:first-of-type .accordion-button {
      border-top-left-radius: calc(0.25rem - 1px);
      border-top-right-radius: calc(0.25rem - 1px); }
  .accordion-item:not(:first-of-type) {
    border-top: 0; }
  .accordion-item:last-of-type {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem; }
    .accordion-item:last-of-type .accordion-button.collapsed {
      border-bottom-right-radius: calc(0.25rem - 1px);
      border-bottom-left-radius: calc(0.25rem - 1px); }
    .accordion-item:last-of-type .accordion-collapse {
      border-bottom-right-radius: 0.25rem;
      border-bottom-left-radius: 0.25rem; }

.accordion-body {
  padding: 1rem 1.25rem; }

.accordion-flush .accordion-collapse {
  border-width: 0; }

.accordion-flush .accordion-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0; }
  .accordion-flush .accordion-item:first-child {
    border-top: 0; }
  .accordion-flush .accordion-item:last-child {
    border-bottom: 0; }
  .accordion-flush .accordion-item .accordion-button {
    border-radius: 0; }

.pagination {
  display: flex;
  padding-left: 0;
  list-style: none; }

.page-link {
  position: relative;
  display: block;
  color: #0d6efd;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #dee2e6;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }
  @media (prefers-reduced-motion: reduce) {
    .page-link {
      transition: none; } }
  .page-link:hover {
    z-index: 2;
    color: #0a58ca;
    background-color: #e9ecef;
    border-color: #dee2e6; }
  .page-link:focus {
    z-index: 3;
    color: #0a58ca;
    background-color: #e9ecef;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25); }

.page-item:not(:first-child) .page-link {
  margin-left: -1px; }

.page-item.active .page-link {
  z-index: 3;
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd; }

.page-item.disabled .page-link {
  color: #6c757d;
  pointer-events: none;
  background-color: #fff;
  border-color: #dee2e6; }

.page-link {
  padding: 0.375rem 0.75rem; }

.page-item:first-child .page-link {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem; }

.page-item:last-child .page-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem; }

.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem; }

.pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem; }

.pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem; }

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem; }

.pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem; }

.pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem; }

.alert {
  position: relative;
  padding: 1rem 1rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem; }

.alert-heading {
  color: inherit; }

.alert-link {
  font-weight: 700; }

.alert-dismissible {
  padding-right: 3rem; }
  .alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 1.25rem 1rem; }

.alert-primary {
  color: #084298;
  background-color: #cfe2ff;
  border-color: #b6d4fe; }
  .alert-primary .alert-link {
    color: #06357a; }

.alert-secondary {
  color: #41464b;
  background-color: #e2e3e5;
  border-color: #d3d6d8; }
  .alert-secondary .alert-link {
    color: #34383c; }

.alert-success {
  color: #0f5132;
  background-color: #d1e7dd;
  border-color: #badbcc; }
  .alert-success .alert-link {
    color: #0c4128; }

.alert-info {
  color: #055160;
  background-color: #cff4fc;
  border-color: #b6effb; }
  .alert-info .alert-link {
    color: #04414d; }

.alert-warning {
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffecb5; }
  .alert-warning .alert-link {
    color: #523e02; }

.alert-danger {
  color: #842029;
  background-color: #f8d7da;
  border-color: #f5c2c7; }
  .alert-danger .alert-link {
    color: #6a1a21; }

.alert-light {
  color: #636464;
  background-color: #fefefe;
  border-color: #fdfdfe; }
  .alert-light .alert-link {
    color: #4f5050; }

.alert-dark {
  color: #141619;
  background-color: #d3d3d4;
  border-color: #bcbebf; }
  .alert-dark .alert-link {
    color: #101214; }

/*
=========================================================================
| Theme
=========================================================================
*/
/**
 * Lightcase - jQuery Plugin
 * The smart and flexible Lightbox Plugin.
 *
 * <AUTHOR> Boppart <<EMAIL>>
 * @copyright	Author
 *
 * @version		2.5.0 (11/03/2018)
 */
/**
 * Lightcase settings
 *
 * Note: Override default settings for your project without touching this source code by simply
 * defining those variables within a SASS map called '$lightcase-custom'.
 *
 * // Example usage
 * $lightcase-custom: (
 *   'breakpoint': 768px
 * );
 */
@font-face {
  font-family: "lightcase";
  src: url(d0216a52d13b07daa707.eot?55356177);
  src: url(d0216a52d13b07daa707.eot?55356177#iefix) format("embedded-opentype"), url(35778fdb807f3a89e3c3.woff?55356177) format("woff"), url(d41de76687bc4acfe4fa.ttf?55356177) format("truetype"), url(5493afb6ce1077500000.svg?55356177#lightcase) format("svg");
  font-weight: normal;
  font-style: normal; }

/* line 12, ../scss/components/dist/fonts/_font-lightcase.scss */
[class*="lightcase-icon-"]:before {
  font-family: "lightcase", sans-serif;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;
  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;
  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */ }

/* Codes */
/* line 35, ../scss/components/fonts/_font-lightcase.scss */
.lightcase-icon-play:before {
  content: "\e800"; }

/* line 36, ../scss/components/fonts/_font-lightcase.scss */
.lightcase-icon-pause:before {
  content: "\e801"; }

/* line 37, ../scss/components/fonts/_font-lightcase.scss */
.lightcase-icon-close:before {
  content: "";
  background-image: url("data:image/svg+xml, %3Csvg width=\"34\" height=\"34\" viewBox=\"0 0 34 34\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Crect y=\"32.3809\" width=\"45.7936\" height=\"2.28968\" rx=\"1\" transform=\"rotate%28-45 0 32.3809%29\" fill=\"%238C929E\"/%3E%3Crect width=\"45.7936\" height=\"2.28968\" rx=\"1\" transform=\"matrix%28-0.707107 -0.707107 -0.707107 0.707107 34 32.3809%29\" fill=\"%238C929E\"/%3E%3C/svg%3E");
  position: absolute;
  right: 0;
  z-index: 9;
  width: 40px;
  background-repeat: no-repeat;
  height: 40px; }

/* line 38, ../scss/components/fonts/_font-lightcase.scss */
.lightcase-icon-prev:before {
  content: "\e803"; }

/* line 39, ../scss/components/fonts/_font-lightcase.scss */
.lightcase-icon-next:before {
  content: "\e804"; }

/* line 40, ../scss/components/fonts/_font-lightcase.scss */
.lightcase-icon-spin:before {
  content: "\e805"; }

/**
 * Mixin providing icon defaults to be used on top of font-lightcase.
 *
 * Example usage:
 * @include icon(#e9e9e9)
 */
/**
 * Mixin providing icon defaults including a hover status to be used
 * on top of font-lightcase.
 *
 * Example usage:
 * @include icon-hover(#e9e9e9, #fff)
 */
/**
 * Provides natural content overflow behavior and scrolling support
 * even so for touch devices.
 *
 * Example usage:
 * @include overflow()
 */
/**
 * Neutralizes/resets dimensions including width, height, position as well as margins,
 * paddings and styles. Used to enforce a neutral and unstyled look and behavoir!
 *
 * Example usage:
 * @include clear(true)
 *
 * @param boolean $important
 */
@-webkit-keyframes lightcase-spin {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(359deg);
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    transform: rotate(359deg); } }

@-moz-keyframes lightcase-spin {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(359deg);
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    transform: rotate(359deg); } }

@-o-keyframes lightcase-spin {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(359deg);
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    transform: rotate(359deg); } }

@-ms-keyframes lightcase-spin {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(359deg);
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    transform: rotate(359deg); } }

@keyframes lightcase-spin {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(359deg);
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    transform: rotate(359deg); } }

/* line 1, ../scss/components/modules/_case.scss */
#lightcase-case {
  display: none;
  position: fixed;
  z-index: 2002;
  top: 50%;
  left: 50%;
  font-family: arial, sans-serif;
  font-size: 13px;
  line-height: 1.5;
  text-align: left;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5); }

@media screen and (max-width: 640px) {
  /* line 16, ../scss/components/modules/_case.scss */
  html[data-lc-type="inline"] #lightcase-case,
  html[data-lc-type="ajax"] #lightcase-case {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    margin: 0 !important;
    padding: 55px 0 70px 0;
    width: 100% !important;
    height: 100% !important;
    overflow: auto !important; } }

@media screen and (min-width: 641px) {
  /* line 4, ../scss/components/modules/_content.scss */
  html:not([data-lc-type="error"]) #lightcase-content {
    position: relative;
    z-index: 1;
    text-shadow: none;
    background-color: #fff;
    -webkit-box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    -o-box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    -webkit-backface-visibility: hidden; } }

@media screen and (min-width: 641px) {
  /* line 23, ../scss/components/modules/_content.scss */
  html[data-lc-type="image"] #lightcase-content,
  html[data-lc-type="video"] #lightcase-content {
    background-color: #333; } }

/* line 31, ../scss/components/modules/_content.scss */
html[data-lc-type="inline"] #lightcase-content,
html[data-lc-type="ajax"] #lightcase-content,
html[data-lc-type="error"] #lightcase-content {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  -o-box-shadow: none;
  box-shadow: none; }

@media screen and (max-width: 640px) {
  /* line 31, ../scss/components/modules/_content.scss */
  html[data-lc-type="inline"] #lightcase-content,
  html[data-lc-type="ajax"] #lightcase-content,
  html[data-lc-type="error"] #lightcase-content {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    width: auto !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: none !important; } }

/* line 43, ../scss/components/modules/_content.scss */
html[data-lc-type="inline"] #lightcase-content .lightcase-contentInner,
html[data-lc-type="ajax"] #lightcase-content .lightcase-contentInner,
html[data-lc-type="error"] #lightcase-content .lightcase-contentInner {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box; }

@media screen and (max-width: 640px) {
  /* line 43, ../scss/components/modules/_content.scss */
  html[data-lc-type="inline"] #lightcase-content .lightcase-contentInner,
  html[data-lc-type="ajax"] #lightcase-content .lightcase-contentInner,
  html[data-lc-type="error"] #lightcase-content .lightcase-contentInner {
    padding: 15px; }
  /* line 52, ../scss/components/modules/_content.scss */
  html[data-lc-type="inline"] #lightcase-content .lightcase-contentInner,
  html[data-lc-type="inline"] #lightcase-content .lightcase-contentInner > *,
  html[data-lc-type="ajax"] #lightcase-content .lightcase-contentInner,
  html[data-lc-type="ajax"] #lightcase-content .lightcase-contentInner > *,
  html[data-lc-type="error"] #lightcase-content .lightcase-contentInner,
  html[data-lc-type="error"] #lightcase-content .lightcase-contentInner > * {
    width: 100% !important;
    max-width: none !important; }
  /* line 59, ../scss/components/modules/_content.scss */
  html[data-lc-type="inline"] #lightcase-content .lightcase-contentInner > *:not(iframe),
  html[data-lc-type="ajax"] #lightcase-content .lightcase-contentInner > *:not(iframe),
  html[data-lc-type="error"] #lightcase-content .lightcase-contentInner > *:not(iframe) {
    height: auto !important;
    max-height: none !important; } }

@media screen and (max-width: 640px) {
  /* line 70, ../scss/components/modules/_content.scss */
  html.lightcase-isMobileDevice[data-lc-type="iframe"] #lightcase-content .lightcase-contentInner iframe {
    overflow: auto;
    -webkit-overflow-scrolling: touch; } }

@media screen and (max-width: 640px) and (min-width: 641px) {
  /* line 74, ../scss/components/modules/_content.scss */
  html[data-lc-type="image"] #lightcase-content .lightcase-contentInner,
  html[data-lc-type="video"] #lightcase-content .lightcase-contentInner {
    line-height: 0.75; } }

/* line 82, ../scss/components/modules/_content.scss */
html[data-lc-type="image"] #lightcase-content .lightcase-contentInner {
  position: relative;
  overflow: hidden !important; }

@media screen and (max-width: 640px) {
  /* line 91, ../scss/components/modules/_content.scss */
  html[data-lc-type="inline"] #lightcase-content .lightcase-contentInner .lightcase-inlineWrap,
  html[data-lc-type="ajax"] #lightcase-content .lightcase-contentInner .lightcase-inlineWrap,
  html[data-lc-type="error"] #lightcase-content .lightcase-contentInner .lightcase-inlineWrap {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    width: auto !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: none !important; } }

@media screen and (min-width: 641px) {
  /* line 100, ../scss/components/modules/_content.scss */
  html:not([data-lc-type="error"]) #lightcase-content .lightcase-contentInner .lightcase-inlineWrap {
    padding: 30px;
    overflow: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -o-box-sizing: border-box;
    box-sizing: border-box; } }

@media screen and (max-width: 640px) {
  /* line 117, ../scss/components/modules/_content.scss */
  #lightcase-content h1, #lightcase-content .h1,
  #lightcase-content h2,
  #lightcase-content .h2,
  #lightcase-content h3,
  #lightcase-content .h3,
  #lightcase-content h4,
  #lightcase-content .h4,
  #lightcase-content h5,
  #lightcase-content .h5,
  #lightcase-content h6,
  #lightcase-content .h6,
  #lightcase-content p {
    color: #aaa; } }

@media screen and (min-width: 641px) {
  /* line 117, ../scss/components/modules/_content.scss */
  #lightcase-content h1, #lightcase-content .h1,
  #lightcase-content h2,
  #lightcase-content .h2,
  #lightcase-content h3,
  #lightcase-content .h3,
  #lightcase-content h4,
  #lightcase-content .h4,
  #lightcase-content h5,
  #lightcase-content .h5,
  #lightcase-content h6,
  #lightcase-content .h6,
  #lightcase-content p {
    color: #333; } }

/* line 3, ../scss/components/modules/_error.scss */
#lightcase-case p.lightcase-error {
  margin: 0;
  font-size: 17px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #aaa; }

@media screen and (max-width: 640px) {
  /* line 3, ../scss/components/modules/_error.scss */
  #lightcase-case p.lightcase-error {
    padding: 30px 0; } }

@media screen and (min-width: 641px) {
  /* line 3, ../scss/components/modules/_error.scss */
  #lightcase-case p.lightcase-error {
    padding: 0; } }

/* line 4, ../scss/components/modules/_global.scss */
.lightcase-open body {
  overflow: hidden; }

/* line 8, ../scss/components/modules/_global.scss */
.lightcase-isMobileDevice .lightcase-open body {
  max-width: 100%;
  max-height: 100%; }

/* line 1, ../scss/components/modules/_info.scss */
#lightcase-info {
  position: absolute;
  padding-top: 15px; }

/* line 9, ../scss/components/modules/_info.scss */
#lightcase-info #lightcase-title,
#lightcase-info #lightcase-caption {
  margin: 0;
  padding: 0;
  line-height: 1.5;
  font-weight: normal;
  text-overflow: ellipsis; }

/* line 19, ../scss/components/modules/_info.scss */
#lightcase-info #lightcase-title {
  font-size: 17px;
  color: #aaa; }

@media screen and (max-width: 640px) {
  /* line 19, ../scss/components/modules/_info.scss */
  #lightcase-info #lightcase-title {
    position: fixed;
    top: 10px;
    left: 0;
    max-width: 87.5%;
    padding: 5px 15px;
    background: #333; } }

/* line 33, ../scss/components/modules/_info.scss */
#lightcase-info #lightcase-caption {
  clear: both;
  font-size: 13px;
  color: #aaa; }

/* line 39, ../scss/components/modules/_info.scss */
#lightcase-info #lightcase-sequenceInfo {
  font-size: 11px;
  color: #aaa; }

@media screen and (max-width: 640px) {
  /* line 45, ../scss/components/modules/_info.scss */
  .lightcase-fullScreenMode #lightcase-info {
    padding-left: 15px;
    padding-right: 15px; }
  /* line 51, ../scss/components/modules/_info.scss */
  html:not([data-lc-type="image"]):not([data-lc-type="video"]):not([data-lc-type="flash"]):not([data-lc-type="error"]) #lightcase-info {
    position: static; } }

/* line 1, ../scss/components/modules/_loading.scss */
#lightcase-loading {
  position: fixed;
  z-index: 9999;
  width: 1.123em;
  height: auto;
  font-size: 38px;
  line-height: 1;
  text-align: center;
  text-shadow: none;
  position: fixed;
  z-index: 2001;
  top: 50%;
  left: 50%;
  margin-top: -0.5em;
  margin-left: -0.5em;
  opacity: 1;
  font-size: 32px;
  text-shadow: 0 0 15px #fff;
  -moz-transform-origin: 50% 53%;
  -webkit-animation: lightcase-spin 0.5s infinite linear;
  -moz-animation: lightcase-spin 0.5s infinite linear;
  -o-animation: lightcase-spin 0.5s infinite linear;
  animation: lightcase-spin 0.5s infinite linear; }

/* line 20, ../scss/components/mixins/_presets.scss */
#lightcase-loading,
#lightcase-loading:focus {
  text-decoration: none;
  color: #fff;
  -webkit-tap-highlight-color: transparent;
  -webkit-transition: color, opacity, ease-in-out 0.25s;
  -moz-transition: color, opacity, ease-in-out 0.25s;
  -o-transition: color, opacity, ease-in-out 0.25s;
  transition: color, opacity, ease-in-out 0.25s; }

/* line 32, ../scss/components/mixins/_presets.scss */
#lightcase-loading > span {
  display: inline-block;
  text-indent: -9999px; }

/* line 2, ../scss/components/modules/_navigation.scss */
a[class*="lightcase-icon-"] {
  position: fixed;
  z-index: 9999;
  width: 1.123em;
  height: auto;
  font-size: 38px;
  line-height: 1;
  text-align: center;
  text-shadow: none;
  outline: none;
  cursor: pointer; }

/* line 20, ../scss/components/mixins/_presets.scss */
a[class*="lightcase-icon-"],
a[class*="lightcase-icon-"]:focus {
  text-decoration: none;
  color: rgba(255, 255, 255, 0.6);
  -webkit-tap-highlight-color: transparent;
  -webkit-transition: color, opacity, ease-in-out 0.25s;
  -moz-transition: color, opacity, ease-in-out 0.25s;
  -o-transition: color, opacity, ease-in-out 0.25s;
  transition: color, opacity, ease-in-out 0.25s; }

/* line 32, ../scss/components/mixins/_presets.scss */
a[class*="lightcase-icon-"] > span {
  display: inline-block;
  text-indent: -9999px; }

/* line 49, ../scss/components/mixins/_presets.scss */
a[class*="lightcase-icon-"]:hover {
  color: white;
  text-shadow: 0 0 15px white; }

/* line 10, ../scss/components/modules/_navigation.scss */
.lightcase-isMobileDevice a[class*="lightcase-icon-"]:hover {
  color: #aaa;
  text-shadow: none; }

/* line 17, ../scss/components/modules/_navigation.scss */
a[class*="lightcase-icon-"].lightcase-icon-close {
  position: fixed;
  top: 15px;
  right: 15px;
  bottom: auto;
  margin: 0;
  opacity: 0;
  outline: none; }

/* line 28, ../scss/components/modules/_navigation.scss */
a[class*="lightcase-icon-"].lightcase-icon-prev {
  left: 15px; }

/* line 33, ../scss/components/modules/_navigation.scss */
a[class*="lightcase-icon-"].lightcase-icon-next {
  right: 15px; }

/* line 38, ../scss/components/modules/_navigation.scss */
a[class*="lightcase-icon-"].lightcase-icon-pause,
a[class*="lightcase-icon-"].lightcase-icon-play {
  left: 50%;
  margin-left: -0.5em; }

@media screen and (min-width: 641px) {
  /* line 38, ../scss/components/modules/_navigation.scss */
  a[class*="lightcase-icon-"].lightcase-icon-pause,
  a[class*="lightcase-icon-"].lightcase-icon-play {
    opacity: 0; } }

@media screen and (max-width: 640px) {
  /* line 2, ../scss/components/modules/_navigation.scss */
  a[class*="lightcase-icon-"] {
    bottom: 15px;
    font-size: 24px; } }

@media screen and (min-width: 641px) {
  /* line 2, ../scss/components/modules/_navigation.scss */
  a[class*="lightcase-icon-"] {
    bottom: 50%;
    margin-bottom: -0.5em; }
  /* line 57, ../scss/components/modules/_navigation.scss */
  a[class*="lightcase-icon-"]:hover,
  #lightcase-case:hover ~ a[class*="lightcase-icon-"] {
    opacity: 1; } }

/* line 1, ../scss/components/modules/_overlay.scss */
#lightcase-overlay {
  display: none;
  width: 100%;
  min-height: 100%;
  position: fixed;
  z-index: 2000;
  top: -9999px;
  bottom: -9999px;
  left: 0;
  background: #333; }

@media screen and (max-width: 640px) {
  /* line 1, ../scss/components/modules/_overlay.scss */
  #lightcase-overlay {
    opacity: 1 !important; } }

/*
  Template Name: United Group
  Description: ;
  Version: 1.0.0;
  Text Domain United Group;
*/
/*
.........................................
        Table of Contents:

# Reseat Css
# Global CSS
# Heading CSS
# Banner CSS
# Features CSS
# Screenshot CSS
# Video CSS
# Interface Slider CSS
# Pricing CSS
# Testimonial CSS
# FAQ CSS
# Blog CSS
# Contact CSS
# Footer CSS
.........................................
*/
*,
*::before,
*::after {
  box-sizing: border-box; }

html {
  font-family: "Barlow", sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  scroll-behavior: smooth; }

@-ms-viewport {
  width: device-width; }

article,
aside,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
  display: block; }

body {
  margin: 0;
  font-family: "Barlow", sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.4;
  color: #000000;
  text-align: left; }

[tabindex='-1']:focus {
  outline: 0 !important; }

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible; }

h1, .h1 {
  font-size: 4em; }
  @media (max-width: 1199.98px) {
    h1, .h1 {
      font-size: 32px;
      line-height: 48px; } }

h2, .h2 {
  font-size: 40px;
  line-height: 60px; }
  @media (max-width: 1199.98px) {
    h2, .h2 {
      font-size: 32px;
      line-height: 48px; } }

h3, .h3 {
  font-size: 1.875em;
  font-weight: 400; }

h4, .h4 {
  font-size: 1.625em;
  line-height: 30px; }

h5, .h5 {
  font-size: 1.25em;
  margin: 5px 0; }

h6, .h6 {
  font-size: 1.125em;
  margin: 5px 0; }

h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
  color: #000000;
  font-weight: 400;
  font-family: "Barlow", sans-serif;
  line-height: 1.5;
  text-transform: uppercase; }

h1 > a, .h1 > a,
h2 > a,
.h2 > a,
h3 > a,
.h3 > a,
h4 > a,
.h4 > a,
h5 > a,
.h5 > a,
h6 > a,
.h6 > a {
  color: inherit;
  font-weight: 400;
  font-family: "Barlow", sans-serif; }

p {
  font-size: 15px;
  margin: 0 0 1.6em;
  color: #000000;
  line-height: 1.75;
  font-family: "Barlow", sans-serif;
  font-weight: 400; }
  @media (min-width: 1200px) {
    p {
      font-size: 16px; } }

abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0; }

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit; }

ol,
ul,
dl {
  margin-top: 0;
  margin-top: 0;
  padding: 0 20px;
  line-height: 30px;
  margin-bottom: 1rem;
  margin-bottom: 1rem; }

dt {
  font-weight: 700; }

dd {
  margin-bottom: 0.5rem;
  margin-left: 0; }

blockquote {
  margin: 0 0 1rem; }

dfn {
  font-style: italic; }

b,
strong {
  font-weight: 400; }

small, .small {
  font-size: 80%; }

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline; }

sub {
  bottom: -0.25em; }

sup {
  top: -0.5em; }

a {
  color: #000000;
  text-decoration: none;
  background-color: transparent;
  -webkit-text-decoration-skip: objects; }

a:hover {
  text-decoration: none;
  color: inherit; }

a:not([href]):not([tabindex]) {
  color: inherit;
  text-decoration: none; }

a:not([href]):not([tabindex]):hover,
a:not([href]):not([tabindex]):focus {
  color: inherit;
  text-decoration: none; }

a:not([href]):not([tabindex]):focus {
  outline: 0; }

pre,
code,
kbd,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
 'Courier New', monospace;
  font-size: 1em; }

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar; }

figure {
  margin: 0 0 1rem; }

img {
  vertical-align: middle;
  border-style: none;
  max-width: 100%; }

svg {
  overflow: hidden;
  vertical-align: middle; }

table {
  border-collapse: collapse; }

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom; }

th {
  text-align: inherit; }

label {
  display: inline-block;
  margin-bottom: 0.5rem; }

button {
  border-radius: 0; }

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color; }

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit; }

button,
input {
  overflow: visible; }

button,
select {
  text-transform: none; }

button,
html [type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button; }

button::-moz-focus-inner,
[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner {
  padding: 0;
  border-style: none; }

input[type='radio'],
input[type='checkbox'] {
  box-sizing: border-box;
  padding: 0; }

input[type='date'],
input[type='time'],
input[type='datetime-local'],
input[type='month'] {
  -webkit-appearance: listbox; }

textarea {
  overflow: auto;
  resize: vertical; }

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0; }

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal; }

progress {
  vertical-align: baseline; }

[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
  height: auto; }

[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none; }

[type='search']::-webkit-search-cancel-button,
[type='search']::-webkit-search-decoration {
  -webkit-appearance: none; }

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button; }

output {
  display: inline-block; }

summary {
  display: list-item;
  cursor: pointer; }

template {
  display: none; }

[hidden] {
  display: none !important; }

a.ghost-btn {
  background-color: #36469d;
  color: #ffffff; }

.pt--5 {
  padding-top: 5px; }

.pt--10 {
  padding-top: 10px; }

.pt--15 {
  padding-top: 15px; }

.pt--20 {
  padding-top: 20px; }

.pt--25 {
  padding-top: 25px; }

.pt--30 {
  padding-top: 30px; }

.pt--35 {
  padding-top: 35px; }

.pt--40 {
  padding-top: 40px; }

.pt--45 {
  padding-top: 45px; }

.pt--50 {
  padding-top: 50px; }

.pt--55 {
  padding-top: 55px; }

.pt--60 {
  padding-top: 60px; }

.pt--65 {
  padding-top: 65px; }

.pt--70 {
  padding-top: 70px; }

.pt--75 {
  padding-top: 75px; }

.pt--80 {
  padding-top: 80px; }

.pt--85 {
  padding-top: 85px; }

.pt--90 {
  padding-top: 90px; }

.pt--95 {
  padding-top: 95px; }

.pt--100 {
  padding-top: 100px; }

.pt--105 {
  padding-top: 105px; }

.pt--110 {
  padding-top: 110px; }

.pt--115 {
  padding-top: 115px; }

.pt--120 {
  padding-top: 120px; }

.pt--125 {
  padding-top: 125px; }

.pt--130 {
  padding-top: 130px; }

.pt--135 {
  padding-top: 135px; }

.pt--140 {
  padding-top: 140px; }

.pt--145 {
  padding-top: 145px; }

.pt--150 {
  padding-top: 150px; }

.pt--155 {
  padding-top: 155px; }

.pt--160 {
  padding-top: 160px; }

.pt--165 {
  padding-top: 165px; }

.pt--170 {
  padding-top: 170px; }

.pt--175 {
  padding-top: 175px; }

.pt--180 {
  padding-top: 180px; }

.pt--185 {
  padding-top: 185px; }

.pt--190 {
  padding-top: 190px; }

.pt--195 {
  padding-top: 195px; }

.pt--200 {
  padding-top: 200px; }

.pt--205 {
  padding-top: 205px; }

.pt--210 {
  padding-top: 210px; }

.pt--215 {
  padding-top: 215px; }

.pt--220 {
  padding-top: 220px; }

.pt--225 {
  padding-top: 225px; }

.pt--230 {
  padding-top: 230px; }

.pt--235 {
  padding-top: 235px; }

.pt--240 {
  padding-top: 240px; }

.pt--245 {
  padding-top: 245px; }

.pt--250 {
  padding-top: 250px; }

@media (min-width: 576px) {
  .pt_sm--5 {
    padding-top: 5px; }
  .pt_sm--10 {
    padding-top: 10px; }
  .pt_sm--15 {
    padding-top: 15px; }
  .pt_sm--20 {
    padding-top: 20px; }
  .pt_sm--25 {
    padding-top: 25px; }
  .pt_sm--30 {
    padding-top: 30px; }
  .pt_sm--35 {
    padding-top: 35px; }
  .pt_sm--40 {
    padding-top: 40px; }
  .pt_sm--45 {
    padding-top: 45px; }
  .pt_sm--50 {
    padding-top: 50px; }
  .pt_sm--55 {
    padding-top: 55px; }
  .pt_sm--60 {
    padding-top: 60px; }
  .pt_sm--65 {
    padding-top: 65px; }
  .pt_sm--70 {
    padding-top: 70px; }
  .pt_sm--75 {
    padding-top: 75px; }
  .pt_sm--80 {
    padding-top: 80px; }
  .pt_sm--85 {
    padding-top: 85px; }
  .pt_sm--90 {
    padding-top: 90px; }
  .pt_sm--95 {
    padding-top: 95px; }
  .pt_sm--100 {
    padding-top: 100px; }
  .pt_sm--105 {
    padding-top: 105px; }
  .pt_sm--110 {
    padding-top: 110px; }
  .pt_sm--115 {
    padding-top: 115px; }
  .pt_sm--120 {
    padding-top: 120px; }
  .pt_sm--125 {
    padding-top: 125px; }
  .pt_sm--130 {
    padding-top: 130px; }
  .pt_sm--135 {
    padding-top: 135px; }
  .pt_sm--140 {
    padding-top: 140px; }
  .pt_sm--145 {
    padding-top: 145px; }
  .pt_sm--150 {
    padding-top: 150px; }
  .pt_sm--155 {
    padding-top: 155px; }
  .pt_sm--160 {
    padding-top: 160px; }
  .pt_sm--165 {
    padding-top: 165px; }
  .pt_sm--170 {
    padding-top: 170px; }
  .pt_sm--175 {
    padding-top: 175px; }
  .pt_sm--180 {
    padding-top: 180px; }
  .pt_sm--185 {
    padding-top: 185px; }
  .pt_sm--190 {
    padding-top: 190px; }
  .pt_sm--195 {
    padding-top: 195px; }
  .pt_sm--200 {
    padding-top: 200px; }
  .pt_sm--205 {
    padding-top: 205px; }
  .pt_sm--210 {
    padding-top: 210px; }
  .pt_sm--215 {
    padding-top: 215px; }
  .pt_sm--220 {
    padding-top: 220px; }
  .pt_sm--225 {
    padding-top: 225px; }
  .pt_sm--230 {
    padding-top: 230px; }
  .pt_sm--235 {
    padding-top: 235px; }
  .pt_sm--240 {
    padding-top: 240px; }
  .pt_sm--245 {
    padding-top: 245px; }
  .pt_sm--250 {
    padding-top: 250px; } }

@media (min-width: 768px) {
  .pt_md--5 {
    padding-top: 5px; }
  .pt_md--10 {
    padding-top: 10px; }
  .pt_md--15 {
    padding-top: 15px; }
  .pt_md--20 {
    padding-top: 20px; }
  .pt_md--25 {
    padding-top: 25px; }
  .pt_md--30 {
    padding-top: 30px; }
  .pt_md--35 {
    padding-top: 35px; }
  .pt_md--40 {
    padding-top: 40px; }
  .pt_md--45 {
    padding-top: 45px; }
  .pt_md--50 {
    padding-top: 50px; }
  .pt_md--55 {
    padding-top: 55px; }
  .pt_md--60 {
    padding-top: 60px; }
  .pt_md--65 {
    padding-top: 65px; }
  .pt_md--70 {
    padding-top: 70px; }
  .pt_md--75 {
    padding-top: 75px; }
  .pt_md--80 {
    padding-top: 80px; }
  .pt_md--85 {
    padding-top: 85px; }
  .pt_md--90 {
    padding-top: 90px; }
  .pt_md--95 {
    padding-top: 95px; }
  .pt_md--100 {
    padding-top: 100px; }
  .pt_md--105 {
    padding-top: 105px; }
  .pt_md--110 {
    padding-top: 110px; }
  .pt_md--115 {
    padding-top: 115px; }
  .pt_md--120 {
    padding-top: 120px; }
  .pt_md--125 {
    padding-top: 125px; }
  .pt_md--130 {
    padding-top: 130px; }
  .pt_md--135 {
    padding-top: 135px; }
  .pt_md--140 {
    padding-top: 140px; }
  .pt_md--145 {
    padding-top: 145px; }
  .pt_md--150 {
    padding-top: 150px; }
  .pt_md--155 {
    padding-top: 155px; }
  .pt_md--160 {
    padding-top: 160px; }
  .pt_md--165 {
    padding-top: 165px; }
  .pt_md--170 {
    padding-top: 170px; }
  .pt_md--175 {
    padding-top: 175px; }
  .pt_md--180 {
    padding-top: 180px; }
  .pt_md--185 {
    padding-top: 185px; }
  .pt_md--190 {
    padding-top: 190px; }
  .pt_md--195 {
    padding-top: 195px; }
  .pt_md--200 {
    padding-top: 200px; }
  .pt_md--205 {
    padding-top: 205px; }
  .pt_md--210 {
    padding-top: 210px; }
  .pt_md--215 {
    padding-top: 215px; }
  .pt_md--220 {
    padding-top: 220px; }
  .pt_md--225 {
    padding-top: 225px; }
  .pt_md--230 {
    padding-top: 230px; }
  .pt_md--235 {
    padding-top: 235px; }
  .pt_md--240 {
    padding-top: 240px; }
  .pt_md--245 {
    padding-top: 245px; }
  .pt_md--250 {
    padding-top: 250px; } }

@media (min-width: 992px) {
  .pt_lg--5 {
    padding-top: 5px; }
  .pt_lg--10 {
    padding-top: 10px; }
  .pt_lg--15 {
    padding-top: 15px; }
  .pt_lg--20 {
    padding-top: 20px; }
  .pt_lg--25 {
    padding-top: 25px; }
  .pt_lg--30 {
    padding-top: 30px; }
  .pt_lg--35 {
    padding-top: 35px; }
  .pt_lg--40 {
    padding-top: 40px; }
  .pt_lg--45 {
    padding-top: 45px; }
  .pt_lg--50 {
    padding-top: 50px; }
  .pt_lg--55 {
    padding-top: 55px; }
  .pt_lg--60 {
    padding-top: 60px; }
  .pt_lg--65 {
    padding-top: 65px; }
  .pt_lg--70 {
    padding-top: 70px; }
  .pt_lg--75 {
    padding-top: 75px; }
  .pt_lg--80 {
    padding-top: 80px; }
  .pt_lg--85 {
    padding-top: 85px; }
  .pt_lg--90 {
    padding-top: 90px; }
  .pt_lg--95 {
    padding-top: 95px; }
  .pt_lg--100 {
    padding-top: 100px; }
  .pt_lg--105 {
    padding-top: 105px; }
  .pt_lg--110 {
    padding-top: 110px; }
  .pt_lg--115 {
    padding-top: 115px; }
  .pt_lg--120 {
    padding-top: 120px; }
  .pt_lg--125 {
    padding-top: 125px; }
  .pt_lg--130 {
    padding-top: 130px; }
  .pt_lg--135 {
    padding-top: 135px; }
  .pt_lg--140 {
    padding-top: 140px; }
  .pt_lg--145 {
    padding-top: 145px; }
  .pt_lg--150 {
    padding-top: 150px; }
  .pt_lg--155 {
    padding-top: 155px; }
  .pt_lg--160 {
    padding-top: 160px; }
  .pt_lg--165 {
    padding-top: 165px; }
  .pt_lg--170 {
    padding-top: 170px; }
  .pt_lg--175 {
    padding-top: 175px; }
  .pt_lg--180 {
    padding-top: 180px; }
  .pt_lg--185 {
    padding-top: 185px; }
  .pt_lg--190 {
    padding-top: 190px; }
  .pt_lg--195 {
    padding-top: 195px; }
  .pt_lg--200 {
    padding-top: 200px; }
  .pt_lg--205 {
    padding-top: 205px; }
  .pt_lg--210 {
    padding-top: 210px; }
  .pt_lg--215 {
    padding-top: 215px; }
  .pt_lg--220 {
    padding-top: 220px; }
  .pt_lg--225 {
    padding-top: 225px; }
  .pt_lg--230 {
    padding-top: 230px; }
  .pt_lg--235 {
    padding-top: 235px; }
  .pt_lg--240 {
    padding-top: 240px; }
  .pt_lg--245 {
    padding-top: 245px; }
  .pt_lg--250 {
    padding-top: 250px; } }

@media (min-width: 1200px) {
  .pt_xl--5 {
    padding-top: 5px; }
  .pt_xl--10 {
    padding-top: 10px; }
  .pt_xl--15 {
    padding-top: 15px; }
  .pt_xl--20 {
    padding-top: 20px; }
  .pt_xl--25 {
    padding-top: 25px; }
  .pt_xl--30 {
    padding-top: 30px; }
  .pt_xl--35 {
    padding-top: 35px; }
  .pt_xl--40 {
    padding-top: 40px; }
  .pt_xl--45 {
    padding-top: 45px; }
  .pt_xl--50 {
    padding-top: 50px; }
  .pt_xl--55 {
    padding-top: 55px; }
  .pt_xl--60 {
    padding-top: 60px; }
  .pt_xl--65 {
    padding-top: 65px; }
  .pt_xl--70 {
    padding-top: 70px; }
  .pt_xl--75 {
    padding-top: 75px; }
  .pt_xl--80 {
    padding-top: 80px; }
  .pt_xl--85 {
    padding-top: 85px; }
  .pt_xl--90 {
    padding-top: 90px; }
  .pt_xl--95 {
    padding-top: 95px; }
  .pt_xl--100 {
    padding-top: 100px; }
  .pt_xl--105 {
    padding-top: 105px; }
  .pt_xl--110 {
    padding-top: 110px; }
  .pt_xl--115 {
    padding-top: 115px; }
  .pt_xl--120 {
    padding-top: 120px; }
  .pt_xl--125 {
    padding-top: 125px; }
  .pt_xl--130 {
    padding-top: 130px; }
  .pt_xl--135 {
    padding-top: 135px; }
  .pt_xl--140 {
    padding-top: 140px; }
  .pt_xl--145 {
    padding-top: 145px; }
  .pt_xl--150 {
    padding-top: 150px; }
  .pt_xl--155 {
    padding-top: 155px; }
  .pt_xl--160 {
    padding-top: 160px; }
  .pt_xl--165 {
    padding-top: 165px; }
  .pt_xl--170 {
    padding-top: 170px; }
  .pt_xl--175 {
    padding-top: 175px; }
  .pt_xl--180 {
    padding-top: 180px; }
  .pt_xl--185 {
    padding-top: 185px; }
  .pt_xl--190 {
    padding-top: 190px; }
  .pt_xl--195 {
    padding-top: 195px; }
  .pt_xl--200 {
    padding-top: 200px; }
  .pt_xl--205 {
    padding-top: 205px; }
  .pt_xl--210 {
    padding-top: 210px; }
  .pt_xl--215 {
    padding-top: 215px; }
  .pt_xl--220 {
    padding-top: 220px; }
  .pt_xl--225 {
    padding-top: 225px; }
  .pt_xl--230 {
    padding-top: 230px; }
  .pt_xl--235 {
    padding-top: 235px; }
  .pt_xl--240 {
    padding-top: 240px; }
  .pt_xl--245 {
    padding-top: 245px; }
  .pt_xl--250 {
    padding-top: 250px; } }

.pb--5 {
  padding-bottom: 5px; }

.pb--10 {
  padding-bottom: 10px; }

.pb--15 {
  padding-bottom: 15px; }

.pb--20 {
  padding-bottom: 20px; }

.pb--25 {
  padding-bottom: 25px; }

.pb--30 {
  padding-bottom: 30px; }

.pb--35 {
  padding-bottom: 35px; }

.pb--40 {
  padding-bottom: 40px; }

.pb--45 {
  padding-bottom: 45px; }

.pb--50 {
  padding-bottom: 50px; }

.pb--55 {
  padding-bottom: 55px; }

.pb--60 {
  padding-bottom: 60px; }

.pb--65 {
  padding-bottom: 65px; }

.pb--70 {
  padding-bottom: 70px; }

.pb--75 {
  padding-bottom: 75px; }

.pb--80 {
  padding-bottom: 80px; }

.pb--85 {
  padding-bottom: 85px; }

.pb--90 {
  padding-bottom: 90px; }

.pb--95 {
  padding-bottom: 95px; }

.pb--100 {
  padding-bottom: 100px; }

.pb--105 {
  padding-bottom: 105px; }

.pb--110 {
  padding-bottom: 110px; }

.pb--115 {
  padding-bottom: 115px; }

.pb--120 {
  padding-bottom: 120px; }

.pb--125 {
  padding-bottom: 125px; }

.pb--130 {
  padding-bottom: 130px; }

.pb--135 {
  padding-bottom: 135px; }

.pb--140 {
  padding-bottom: 140px; }

.pb--145 {
  padding-bottom: 145px; }

.pb--150 {
  padding-bottom: 150px; }

.pb--155 {
  padding-bottom: 155px; }

.pb--160 {
  padding-bottom: 160px; }

.pb--165 {
  padding-bottom: 165px; }

.pb--170 {
  padding-bottom: 170px; }

.pb--175 {
  padding-bottom: 175px; }

.pb--180 {
  padding-bottom: 180px; }

.pb--185 {
  padding-bottom: 185px; }

.pb--190 {
  padding-bottom: 190px; }

.pb--195 {
  padding-bottom: 195px; }

.pb--200 {
  padding-bottom: 200px; }

.pb--205 {
  padding-bottom: 205px; }

.pb--210 {
  padding-bottom: 210px; }

.pb--215 {
  padding-bottom: 215px; }

.pb--220 {
  padding-bottom: 220px; }

.pb--225 {
  padding-bottom: 225px; }

.pb--230 {
  padding-bottom: 230px; }

.pb--235 {
  padding-bottom: 235px; }

.pb--240 {
  padding-bottom: 240px; }

.pb--245 {
  padding-bottom: 245px; }

.pb--250 {
  padding-bottom: 250px; }

@media (min-width: 576px) {
  .pb_sm--5 {
    padding-bottom: 5px; }
  .pb_sm--10 {
    padding-bottom: 10px; }
  .pb_sm--15 {
    padding-bottom: 15px; }
  .pb_sm--20 {
    padding-bottom: 20px; }
  .pb_sm--25 {
    padding-bottom: 25px; }
  .pb_sm--30 {
    padding-bottom: 30px; }
  .pb_sm--35 {
    padding-bottom: 35px; }
  .pb_sm--40 {
    padding-bottom: 40px; }
  .pb_sm--45 {
    padding-bottom: 45px; }
  .pb_sm--50 {
    padding-bottom: 50px; }
  .pb_sm--55 {
    padding-bottom: 55px; }
  .pb_sm--60 {
    padding-bottom: 60px; }
  .pb_sm--65 {
    padding-bottom: 65px; }
  .pb_sm--70 {
    padding-bottom: 70px; }
  .pb_sm--75 {
    padding-bottom: 75px; }
  .pb_sm--80 {
    padding-bottom: 80px; }
  .pb_sm--85 {
    padding-bottom: 85px; }
  .pb_sm--90 {
    padding-bottom: 90px; }
  .pb_sm--95 {
    padding-bottom: 95px; }
  .pb_sm--100 {
    padding-bottom: 100px; }
  .pb_sm--105 {
    padding-bottom: 105px; }
  .pb_sm--110 {
    padding-bottom: 110px; }
  .pb_sm--115 {
    padding-bottom: 115px; }
  .pb_sm--120 {
    padding-bottom: 120px; }
  .pb_sm--125 {
    padding-bottom: 125px; }
  .pb_sm--130 {
    padding-bottom: 130px; }
  .pb_sm--135 {
    padding-bottom: 135px; }
  .pb_sm--140 {
    padding-bottom: 140px; }
  .pb_sm--145 {
    padding-bottom: 145px; }
  .pb_sm--150 {
    padding-bottom: 150px; }
  .pb_sm--155 {
    padding-bottom: 155px; }
  .pb_sm--160 {
    padding-bottom: 160px; }
  .pb_sm--165 {
    padding-bottom: 165px; }
  .pb_sm--170 {
    padding-bottom: 170px; }
  .pb_sm--175 {
    padding-bottom: 175px; }
  .pb_sm--180 {
    padding-bottom: 180px; }
  .pb_sm--185 {
    padding-bottom: 185px; }
  .pb_sm--190 {
    padding-bottom: 190px; }
  .pb_sm--195 {
    padding-bottom: 195px; }
  .pb_sm--200 {
    padding-bottom: 200px; }
  .pb_sm--205 {
    padding-bottom: 205px; }
  .pb_sm--210 {
    padding-bottom: 210px; }
  .pb_sm--215 {
    padding-bottom: 215px; }
  .pb_sm--220 {
    padding-bottom: 220px; }
  .pb_sm--225 {
    padding-bottom: 225px; }
  .pb_sm--230 {
    padding-bottom: 230px; }
  .pb_sm--235 {
    padding-bottom: 235px; }
  .pb_sm--240 {
    padding-bottom: 240px; }
  .pb_sm--245 {
    padding-bottom: 245px; }
  .pb_sm--250 {
    padding-bottom: 250px; } }

@media (min-width: 768px) {
  .pb_md--5 {
    padding-bottom: 5px; }
  .pb_md--10 {
    padding-bottom: 10px; }
  .pb_md--15 {
    padding-bottom: 15px; }
  .pb_md--20 {
    padding-bottom: 20px; }
  .pb_md--25 {
    padding-bottom: 25px; }
  .pb_md--30 {
    padding-bottom: 30px; }
  .pb_md--35 {
    padding-bottom: 35px; }
  .pb_md--40 {
    padding-bottom: 40px; }
  .pb_md--45 {
    padding-bottom: 45px; }
  .pb_md--50 {
    padding-bottom: 50px; }
  .pb_md--55 {
    padding-bottom: 55px; }
  .pb_md--60 {
    padding-bottom: 60px; }
  .pb_md--65 {
    padding-bottom: 65px; }
  .pb_md--70 {
    padding-bottom: 70px; }
  .pb_md--75 {
    padding-bottom: 75px; }
  .pb_md--80 {
    padding-bottom: 80px; }
  .pb_md--85 {
    padding-bottom: 85px; }
  .pb_md--90 {
    padding-bottom: 90px; }
  .pb_md--95 {
    padding-bottom: 95px; }
  .pb_md--100 {
    padding-bottom: 100px; }
  .pb_md--105 {
    padding-bottom: 105px; }
  .pb_md--110 {
    padding-bottom: 110px; }
  .pb_md--115 {
    padding-bottom: 115px; }
  .pb_md--120 {
    padding-bottom: 120px; }
  .pb_md--125 {
    padding-bottom: 125px; }
  .pb_md--130 {
    padding-bottom: 130px; }
  .pb_md--135 {
    padding-bottom: 135px; }
  .pb_md--140 {
    padding-bottom: 140px; }
  .pb_md--145 {
    padding-bottom: 145px; }
  .pb_md--150 {
    padding-bottom: 150px; }
  .pb_md--155 {
    padding-bottom: 155px; }
  .pb_md--160 {
    padding-bottom: 160px; }
  .pb_md--165 {
    padding-bottom: 165px; }
  .pb_md--170 {
    padding-bottom: 170px; }
  .pb_md--175 {
    padding-bottom: 175px; }
  .pb_md--180 {
    padding-bottom: 180px; }
  .pb_md--185 {
    padding-bottom: 185px; }
  .pb_md--190 {
    padding-bottom: 190px; }
  .pb_md--195 {
    padding-bottom: 195px; }
  .pb_md--200 {
    padding-bottom: 200px; }
  .pb_md--205 {
    padding-bottom: 205px; }
  .pb_md--210 {
    padding-bottom: 210px; }
  .pb_md--215 {
    padding-bottom: 215px; }
  .pb_md--220 {
    padding-bottom: 220px; }
  .pb_md--225 {
    padding-bottom: 225px; }
  .pb_md--230 {
    padding-bottom: 230px; }
  .pb_md--235 {
    padding-bottom: 235px; }
  .pb_md--240 {
    padding-bottom: 240px; }
  .pb_md--245 {
    padding-bottom: 245px; }
  .pb_md--250 {
    padding-bottom: 250px; } }

@media (min-width: 992px) {
  .pb_lg--5 {
    padding-bottom: 5px; }
  .pb_lg--10 {
    padding-bottom: 10px; }
  .pb_lg--15 {
    padding-bottom: 15px; }
  .pb_lg--20 {
    padding-bottom: 20px; }
  .pb_lg--25 {
    padding-bottom: 25px; }
  .pb_lg--30 {
    padding-bottom: 30px; }
  .pb_lg--35 {
    padding-bottom: 35px; }
  .pb_lg--40 {
    padding-bottom: 40px; }
  .pb_lg--45 {
    padding-bottom: 45px; }
  .pb_lg--50 {
    padding-bottom: 50px; }
  .pb_lg--55 {
    padding-bottom: 55px; }
  .pb_lg--60 {
    padding-bottom: 60px; }
  .pb_lg--65 {
    padding-bottom: 65px; }
  .pb_lg--70 {
    padding-bottom: 70px; }
  .pb_lg--75 {
    padding-bottom: 75px; }
  .pb_lg--80 {
    padding-bottom: 80px; }
  .pb_lg--85 {
    padding-bottom: 85px; }
  .pb_lg--90 {
    padding-bottom: 90px; }
  .pb_lg--95 {
    padding-bottom: 95px; }
  .pb_lg--100 {
    padding-bottom: 100px; }
  .pb_lg--105 {
    padding-bottom: 105px; }
  .pb_lg--110 {
    padding-bottom: 110px; }
  .pb_lg--115 {
    padding-bottom: 115px; }
  .pb_lg--120 {
    padding-bottom: 120px; }
  .pb_lg--125 {
    padding-bottom: 125px; }
  .pb_lg--130 {
    padding-bottom: 130px; }
  .pb_lg--135 {
    padding-bottom: 135px; }
  .pb_lg--140 {
    padding-bottom: 140px; }
  .pb_lg--145 {
    padding-bottom: 145px; }
  .pb_lg--150 {
    padding-bottom: 150px; }
  .pb_lg--155 {
    padding-bottom: 155px; }
  .pb_lg--160 {
    padding-bottom: 160px; }
  .pb_lg--165 {
    padding-bottom: 165px; }
  .pb_lg--170 {
    padding-bottom: 170px; }
  .pb_lg--175 {
    padding-bottom: 175px; }
  .pb_lg--180 {
    padding-bottom: 180px; }
  .pb_lg--185 {
    padding-bottom: 185px; }
  .pb_lg--190 {
    padding-bottom: 190px; }
  .pb_lg--195 {
    padding-bottom: 195px; }
  .pb_lg--200 {
    padding-bottom: 200px; }
  .pb_lg--205 {
    padding-bottom: 205px; }
  .pb_lg--210 {
    padding-bottom: 210px; }
  .pb_lg--215 {
    padding-bottom: 215px; }
  .pb_lg--220 {
    padding-bottom: 220px; }
  .pb_lg--225 {
    padding-bottom: 225px; }
  .pb_lg--230 {
    padding-bottom: 230px; }
  .pb_lg--235 {
    padding-bottom: 235px; }
  .pb_lg--240 {
    padding-bottom: 240px; }
  .pb_lg--245 {
    padding-bottom: 245px; }
  .pb_lg--250 {
    padding-bottom: 250px; } }

@media (min-width: 1200px) {
  .pb_xl--5 {
    padding-bottom: 5px; }
  .pb_xl--10 {
    padding-bottom: 10px; }
  .pb_xl--15 {
    padding-bottom: 15px; }
  .pb_xl--20 {
    padding-bottom: 20px; }
  .pb_xl--25 {
    padding-bottom: 25px; }
  .pb_xl--30 {
    padding-bottom: 30px; }
  .pb_xl--35 {
    padding-bottom: 35px; }
  .pb_xl--40 {
    padding-bottom: 40px; }
  .pb_xl--45 {
    padding-bottom: 45px; }
  .pb_xl--50 {
    padding-bottom: 50px; }
  .pb_xl--55 {
    padding-bottom: 55px; }
  .pb_xl--60 {
    padding-bottom: 60px; }
  .pb_xl--65 {
    padding-bottom: 65px; }
  .pb_xl--70 {
    padding-bottom: 70px; }
  .pb_xl--75 {
    padding-bottom: 75px; }
  .pb_xl--80 {
    padding-bottom: 80px; }
  .pb_xl--85 {
    padding-bottom: 85px; }
  .pb_xl--90 {
    padding-bottom: 90px; }
  .pb_xl--95 {
    padding-bottom: 95px; }
  .pb_xl--100 {
    padding-bottom: 100px; }
  .pb_xl--105 {
    padding-bottom: 105px; }
  .pb_xl--110 {
    padding-bottom: 110px; }
  .pb_xl--115 {
    padding-bottom: 115px; }
  .pb_xl--120 {
    padding-bottom: 120px; }
  .pb_xl--125 {
    padding-bottom: 125px; }
  .pb_xl--130 {
    padding-bottom: 130px; }
  .pb_xl--135 {
    padding-bottom: 135px; }
  .pb_xl--140 {
    padding-bottom: 140px; }
  .pb_xl--145 {
    padding-bottom: 145px; }
  .pb_xl--150 {
    padding-bottom: 150px; }
  .pb_xl--155 {
    padding-bottom: 155px; }
  .pb_xl--160 {
    padding-bottom: 160px; }
  .pb_xl--165 {
    padding-bottom: 165px; }
  .pb_xl--170 {
    padding-bottom: 170px; }
  .pb_xl--175 {
    padding-bottom: 175px; }
  .pb_xl--180 {
    padding-bottom: 180px; }
  .pb_xl--185 {
    padding-bottom: 185px; }
  .pb_xl--190 {
    padding-bottom: 190px; }
  .pb_xl--195 {
    padding-bottom: 195px; }
  .pb_xl--200 {
    padding-bottom: 200px; }
  .pb_xl--205 {
    padding-bottom: 205px; }
  .pb_xl--210 {
    padding-bottom: 210px; }
  .pb_xl--215 {
    padding-bottom: 215px; }
  .pb_xl--220 {
    padding-bottom: 220px; }
  .pb_xl--225 {
    padding-bottom: 225px; }
  .pb_xl--230 {
    padding-bottom: 230px; }
  .pb_xl--235 {
    padding-bottom: 235px; }
  .pb_xl--240 {
    padding-bottom: 240px; }
  .pb_xl--245 {
    padding-bottom: 245px; }
  .pb_xl--250 {
    padding-bottom: 250px; } }

.pl--5 {
  padding-left: 5px; }

.pl--10 {
  padding-left: 10px; }

.pl--15 {
  padding-left: 15px; }

.pl--20 {
  padding-left: 20px; }

.pl--25 {
  padding-left: 25px; }

.pl--30 {
  padding-left: 30px; }

.pl--35 {
  padding-left: 35px; }

.pl--40 {
  padding-left: 40px; }

.pl--45 {
  padding-left: 45px; }

.pl--50 {
  padding-left: 50px; }

.pl--55 {
  padding-left: 55px; }

.pl--60 {
  padding-left: 60px; }

.pl--65 {
  padding-left: 65px; }

.pl--70 {
  padding-left: 70px; }

.pl--75 {
  padding-left: 75px; }

.pl--80 {
  padding-left: 80px; }

.pl--85 {
  padding-left: 85px; }

.pl--90 {
  padding-left: 90px; }

.pl--95 {
  padding-left: 95px; }

.pl--100 {
  padding-left: 100px; }

.pl--105 {
  padding-left: 105px; }

.pl--110 {
  padding-left: 110px; }

.pl--115 {
  padding-left: 115px; }

.pl--120 {
  padding-left: 120px; }

.pl--125 {
  padding-left: 125px; }

.pl--130 {
  padding-left: 130px; }

.pl--135 {
  padding-left: 135px; }

.pl--140 {
  padding-left: 140px; }

.pl--145 {
  padding-left: 145px; }

.pl--150 {
  padding-left: 150px; }

.pl--155 {
  padding-left: 155px; }

.pl--160 {
  padding-left: 160px; }

.pl--165 {
  padding-left: 165px; }

.pl--170 {
  padding-left: 170px; }

.pl--175 {
  padding-left: 175px; }

.pl--180 {
  padding-left: 180px; }

.pl--185 {
  padding-left: 185px; }

.pl--190 {
  padding-left: 190px; }

.pl--195 {
  padding-left: 195px; }

.pl--200 {
  padding-left: 200px; }

.pl--205 {
  padding-left: 205px; }

.pl--210 {
  padding-left: 210px; }

.pl--215 {
  padding-left: 215px; }

.pl--220 {
  padding-left: 220px; }

.pl--225 {
  padding-left: 225px; }

.pl--230 {
  padding-left: 230px; }

.pl--235 {
  padding-left: 235px; }

.pl--240 {
  padding-left: 240px; }

.pl--245 {
  padding-left: 245px; }

.pl--250 {
  padding-left: 250px; }

@media (min-width: 576px) {
  .pl_sm--5 {
    padding-left: 5px; }
  .pl_sm--10 {
    padding-left: 10px; }
  .pl_sm--15 {
    padding-left: 15px; }
  .pl_sm--20 {
    padding-left: 20px; }
  .pl_sm--25 {
    padding-left: 25px; }
  .pl_sm--30 {
    padding-left: 30px; }
  .pl_sm--35 {
    padding-left: 35px; }
  .pl_sm--40 {
    padding-left: 40px; }
  .pl_sm--45 {
    padding-left: 45px; }
  .pl_sm--50 {
    padding-left: 50px; }
  .pl_sm--55 {
    padding-left: 55px; }
  .pl_sm--60 {
    padding-left: 60px; }
  .pl_sm--65 {
    padding-left: 65px; }
  .pl_sm--70 {
    padding-left: 70px; }
  .pl_sm--75 {
    padding-left: 75px; }
  .pl_sm--80 {
    padding-left: 80px; }
  .pl_sm--85 {
    padding-left: 85px; }
  .pl_sm--90 {
    padding-left: 90px; }
  .pl_sm--95 {
    padding-left: 95px; }
  .pl_sm--100 {
    padding-left: 100px; }
  .pl_sm--105 {
    padding-left: 105px; }
  .pl_sm--110 {
    padding-left: 110px; }
  .pl_sm--115 {
    padding-left: 115px; }
  .pl_sm--120 {
    padding-left: 120px; }
  .pl_sm--125 {
    padding-left: 125px; }
  .pl_sm--130 {
    padding-left: 130px; }
  .pl_sm--135 {
    padding-left: 135px; }
  .pl_sm--140 {
    padding-left: 140px; }
  .pl_sm--145 {
    padding-left: 145px; }
  .pl_sm--150 {
    padding-left: 150px; }
  .pl_sm--155 {
    padding-left: 155px; }
  .pl_sm--160 {
    padding-left: 160px; }
  .pl_sm--165 {
    padding-left: 165px; }
  .pl_sm--170 {
    padding-left: 170px; }
  .pl_sm--175 {
    padding-left: 175px; }
  .pl_sm--180 {
    padding-left: 180px; }
  .pl_sm--185 {
    padding-left: 185px; }
  .pl_sm--190 {
    padding-left: 190px; }
  .pl_sm--195 {
    padding-left: 195px; }
  .pl_sm--200 {
    padding-left: 200px; }
  .pl_sm--205 {
    padding-left: 205px; }
  .pl_sm--210 {
    padding-left: 210px; }
  .pl_sm--215 {
    padding-left: 215px; }
  .pl_sm--220 {
    padding-left: 220px; }
  .pl_sm--225 {
    padding-left: 225px; }
  .pl_sm--230 {
    padding-left: 230px; }
  .pl_sm--235 {
    padding-left: 235px; }
  .pl_sm--240 {
    padding-left: 240px; }
  .pl_sm--245 {
    padding-left: 245px; }
  .pl_sm--250 {
    padding-left: 250px; } }

@media (min-width: 768px) {
  .pl_md--5 {
    padding-left: 5px; }
  .pl_md--10 {
    padding-left: 10px; }
  .pl_md--15 {
    padding-left: 15px; }
  .pl_md--20 {
    padding-left: 20px; }
  .pl_md--25 {
    padding-left: 25px; }
  .pl_md--30 {
    padding-left: 30px; }
  .pl_md--35 {
    padding-left: 35px; }
  .pl_md--40 {
    padding-left: 40px; }
  .pl_md--45 {
    padding-left: 45px; }
  .pl_md--50 {
    padding-left: 50px; }
  .pl_md--55 {
    padding-left: 55px; }
  .pl_md--60 {
    padding-left: 60px; }
  .pl_md--65 {
    padding-left: 65px; }
  .pl_md--70 {
    padding-left: 70px; }
  .pl_md--75 {
    padding-left: 75px; }
  .pl_md--80 {
    padding-left: 80px; }
  .pl_md--85 {
    padding-left: 85px; }
  .pl_md--90 {
    padding-left: 90px; }
  .pl_md--95 {
    padding-left: 95px; }
  .pl_md--100 {
    padding-left: 100px; }
  .pl_md--105 {
    padding-left: 105px; }
  .pl_md--110 {
    padding-left: 110px; }
  .pl_md--115 {
    padding-left: 115px; }
  .pl_md--120 {
    padding-left: 120px; }
  .pl_md--125 {
    padding-left: 125px; }
  .pl_md--130 {
    padding-left: 130px; }
  .pl_md--135 {
    padding-left: 135px; }
  .pl_md--140 {
    padding-left: 140px; }
  .pl_md--145 {
    padding-left: 145px; }
  .pl_md--150 {
    padding-left: 150px; }
  .pl_md--155 {
    padding-left: 155px; }
  .pl_md--160 {
    padding-left: 160px; }
  .pl_md--165 {
    padding-left: 165px; }
  .pl_md--170 {
    padding-left: 170px; }
  .pl_md--175 {
    padding-left: 175px; }
  .pl_md--180 {
    padding-left: 180px; }
  .pl_md--185 {
    padding-left: 185px; }
  .pl_md--190 {
    padding-left: 190px; }
  .pl_md--195 {
    padding-left: 195px; }
  .pl_md--200 {
    padding-left: 200px; }
  .pl_md--205 {
    padding-left: 205px; }
  .pl_md--210 {
    padding-left: 210px; }
  .pl_md--215 {
    padding-left: 215px; }
  .pl_md--220 {
    padding-left: 220px; }
  .pl_md--225 {
    padding-left: 225px; }
  .pl_md--230 {
    padding-left: 230px; }
  .pl_md--235 {
    padding-left: 235px; }
  .pl_md--240 {
    padding-left: 240px; }
  .pl_md--245 {
    padding-left: 245px; }
  .pl_md--250 {
    padding-left: 250px; } }

@media (min-width: 992px) {
  .pl_lg--5 {
    padding-left: 5px; }
  .pl_lg--10 {
    padding-left: 10px; }
  .pl_lg--15 {
    padding-left: 15px; }
  .pl_lg--20 {
    padding-left: 20px; }
  .pl_lg--25 {
    padding-left: 25px; }
  .pl_lg--30 {
    padding-left: 30px; }
  .pl_lg--35 {
    padding-left: 35px; }
  .pl_lg--40 {
    padding-left: 40px; }
  .pl_lg--45 {
    padding-left: 45px; }
  .pl_lg--50 {
    padding-left: 50px; }
  .pl_lg--55 {
    padding-left: 55px; }
  .pl_lg--60 {
    padding-left: 60px; }
  .pl_lg--65 {
    padding-left: 65px; }
  .pl_lg--70 {
    padding-left: 70px; }
  .pl_lg--75 {
    padding-left: 75px; }
  .pl_lg--80 {
    padding-left: 80px; }
  .pl_lg--85 {
    padding-left: 85px; }
  .pl_lg--90 {
    padding-left: 90px; }
  .pl_lg--95 {
    padding-left: 95px; }
  .pl_lg--100 {
    padding-left: 100px; }
  .pl_lg--105 {
    padding-left: 105px; }
  .pl_lg--110 {
    padding-left: 110px; }
  .pl_lg--115 {
    padding-left: 115px; }
  .pl_lg--120 {
    padding-left: 120px; }
  .pl_lg--125 {
    padding-left: 125px; }
  .pl_lg--130 {
    padding-left: 130px; }
  .pl_lg--135 {
    padding-left: 135px; }
  .pl_lg--140 {
    padding-left: 140px; }
  .pl_lg--145 {
    padding-left: 145px; }
  .pl_lg--150 {
    padding-left: 150px; }
  .pl_lg--155 {
    padding-left: 155px; }
  .pl_lg--160 {
    padding-left: 160px; }
  .pl_lg--165 {
    padding-left: 165px; }
  .pl_lg--170 {
    padding-left: 170px; }
  .pl_lg--175 {
    padding-left: 175px; }
  .pl_lg--180 {
    padding-left: 180px; }
  .pl_lg--185 {
    padding-left: 185px; }
  .pl_lg--190 {
    padding-left: 190px; }
  .pl_lg--195 {
    padding-left: 195px; }
  .pl_lg--200 {
    padding-left: 200px; }
  .pl_lg--205 {
    padding-left: 205px; }
  .pl_lg--210 {
    padding-left: 210px; }
  .pl_lg--215 {
    padding-left: 215px; }
  .pl_lg--220 {
    padding-left: 220px; }
  .pl_lg--225 {
    padding-left: 225px; }
  .pl_lg--230 {
    padding-left: 230px; }
  .pl_lg--235 {
    padding-left: 235px; }
  .pl_lg--240 {
    padding-left: 240px; }
  .pl_lg--245 {
    padding-left: 245px; }
  .pl_lg--250 {
    padding-left: 250px; } }

@media (min-width: 1200px) {
  .pl_xl--5 {
    padding-left: 5px; }
  .pl_xl--10 {
    padding-left: 10px; }
  .pl_xl--15 {
    padding-left: 15px; }
  .pl_xl--20 {
    padding-left: 20px; }
  .pl_xl--25 {
    padding-left: 25px; }
  .pl_xl--30 {
    padding-left: 30px; }
  .pl_xl--35 {
    padding-left: 35px; }
  .pl_xl--40 {
    padding-left: 40px; }
  .pl_xl--45 {
    padding-left: 45px; }
  .pl_xl--50 {
    padding-left: 50px; }
  .pl_xl--55 {
    padding-left: 55px; }
  .pl_xl--60 {
    padding-left: 60px; }
  .pl_xl--65 {
    padding-left: 65px; }
  .pl_xl--70 {
    padding-left: 70px; }
  .pl_xl--75 {
    padding-left: 75px; }
  .pl_xl--80 {
    padding-left: 80px; }
  .pl_xl--85 {
    padding-left: 85px; }
  .pl_xl--90 {
    padding-left: 90px; }
  .pl_xl--95 {
    padding-left: 95px; }
  .pl_xl--100 {
    padding-left: 100px; }
  .pl_xl--105 {
    padding-left: 105px; }
  .pl_xl--110 {
    padding-left: 110px; }
  .pl_xl--115 {
    padding-left: 115px; }
  .pl_xl--120 {
    padding-left: 120px; }
  .pl_xl--125 {
    padding-left: 125px; }
  .pl_xl--130 {
    padding-left: 130px; }
  .pl_xl--135 {
    padding-left: 135px; }
  .pl_xl--140 {
    padding-left: 140px; }
  .pl_xl--145 {
    padding-left: 145px; }
  .pl_xl--150 {
    padding-left: 150px; }
  .pl_xl--155 {
    padding-left: 155px; }
  .pl_xl--160 {
    padding-left: 160px; }
  .pl_xl--165 {
    padding-left: 165px; }
  .pl_xl--170 {
    padding-left: 170px; }
  .pl_xl--175 {
    padding-left: 175px; }
  .pl_xl--180 {
    padding-left: 180px; }
  .pl_xl--185 {
    padding-left: 185px; }
  .pl_xl--190 {
    padding-left: 190px; }
  .pl_xl--195 {
    padding-left: 195px; }
  .pl_xl--200 {
    padding-left: 200px; }
  .pl_xl--205 {
    padding-left: 205px; }
  .pl_xl--210 {
    padding-left: 210px; }
  .pl_xl--215 {
    padding-left: 215px; }
  .pl_xl--220 {
    padding-left: 220px; }
  .pl_xl--225 {
    padding-left: 225px; }
  .pl_xl--230 {
    padding-left: 230px; }
  .pl_xl--235 {
    padding-left: 235px; }
  .pl_xl--240 {
    padding-left: 240px; }
  .pl_xl--245 {
    padding-left: 245px; }
  .pl_xl--250 {
    padding-left: 250px; } }

.pr--5 {
  padding-right: 5px; }

.pr--10 {
  padding-right: 10px; }

.pr--15 {
  padding-right: 15px; }

.pr--20 {
  padding-right: 20px; }

.pr--25 {
  padding-right: 25px; }

.pr--30 {
  padding-right: 30px; }

.pr--35 {
  padding-right: 35px; }

.pr--40 {
  padding-right: 40px; }

.pr--45 {
  padding-right: 45px; }

.pr--50 {
  padding-right: 50px; }

.pr--55 {
  padding-right: 55px; }

.pr--60 {
  padding-right: 60px; }

.pr--65 {
  padding-right: 65px; }

.pr--70 {
  padding-right: 70px; }

.pr--75 {
  padding-right: 75px; }

.pr--80 {
  padding-right: 80px; }

.pr--85 {
  padding-right: 85px; }

.pr--90 {
  padding-right: 90px; }

.pr--95 {
  padding-right: 95px; }

.pr--100 {
  padding-right: 100px; }

.pr--105 {
  padding-right: 105px; }

.pr--110 {
  padding-right: 110px; }

.pr--115 {
  padding-right: 115px; }

.pr--120 {
  padding-right: 120px; }

.pr--125 {
  padding-right: 125px; }

.pr--130 {
  padding-right: 130px; }

.pr--135 {
  padding-right: 135px; }

.pr--140 {
  padding-right: 140px; }

.pr--145 {
  padding-right: 145px; }

.pr--150 {
  padding-right: 150px; }

.pr--155 {
  padding-right: 155px; }

.pr--160 {
  padding-right: 160px; }

.pr--165 {
  padding-right: 165px; }

.pr--170 {
  padding-right: 170px; }

.pr--175 {
  padding-right: 175px; }

.pr--180 {
  padding-right: 180px; }

.pr--185 {
  padding-right: 185px; }

.pr--190 {
  padding-right: 190px; }

.pr--195 {
  padding-right: 195px; }

.pr--200 {
  padding-right: 200px; }

.pr--205 {
  padding-right: 205px; }

.pr--210 {
  padding-right: 210px; }

.pr--215 {
  padding-right: 215px; }

.pr--220 {
  padding-right: 220px; }

.pr--225 {
  padding-right: 225px; }

.pr--230 {
  padding-right: 230px; }

.pr--235 {
  padding-right: 235px; }

.pr--240 {
  padding-right: 240px; }

.pr--245 {
  padding-right: 245px; }

.pr--250 {
  padding-right: 250px; }

@media (min-width: 576px) {
  .pr_sm--5 {
    padding-right: 5px; }
  .pr_sm--10 {
    padding-right: 10px; }
  .pr_sm--15 {
    padding-right: 15px; }
  .pr_sm--20 {
    padding-right: 20px; }
  .pr_sm--25 {
    padding-right: 25px; }
  .pr_sm--30 {
    padding-right: 30px; }
  .pr_sm--35 {
    padding-right: 35px; }
  .pr_sm--40 {
    padding-right: 40px; }
  .pr_sm--45 {
    padding-right: 45px; }
  .pr_sm--50 {
    padding-right: 50px; }
  .pr_sm--55 {
    padding-right: 55px; }
  .pr_sm--60 {
    padding-right: 60px; }
  .pr_sm--65 {
    padding-right: 65px; }
  .pr_sm--70 {
    padding-right: 70px; }
  .pr_sm--75 {
    padding-right: 75px; }
  .pr_sm--80 {
    padding-right: 80px; }
  .pr_sm--85 {
    padding-right: 85px; }
  .pr_sm--90 {
    padding-right: 90px; }
  .pr_sm--95 {
    padding-right: 95px; }
  .pr_sm--100 {
    padding-right: 100px; }
  .pr_sm--105 {
    padding-right: 105px; }
  .pr_sm--110 {
    padding-right: 110px; }
  .pr_sm--115 {
    padding-right: 115px; }
  .pr_sm--120 {
    padding-right: 120px; }
  .pr_sm--125 {
    padding-right: 125px; }
  .pr_sm--130 {
    padding-right: 130px; }
  .pr_sm--135 {
    padding-right: 135px; }
  .pr_sm--140 {
    padding-right: 140px; }
  .pr_sm--145 {
    padding-right: 145px; }
  .pr_sm--150 {
    padding-right: 150px; }
  .pr_sm--155 {
    padding-right: 155px; }
  .pr_sm--160 {
    padding-right: 160px; }
  .pr_sm--165 {
    padding-right: 165px; }
  .pr_sm--170 {
    padding-right: 170px; }
  .pr_sm--175 {
    padding-right: 175px; }
  .pr_sm--180 {
    padding-right: 180px; }
  .pr_sm--185 {
    padding-right: 185px; }
  .pr_sm--190 {
    padding-right: 190px; }
  .pr_sm--195 {
    padding-right: 195px; }
  .pr_sm--200 {
    padding-right: 200px; }
  .pr_sm--205 {
    padding-right: 205px; }
  .pr_sm--210 {
    padding-right: 210px; }
  .pr_sm--215 {
    padding-right: 215px; }
  .pr_sm--220 {
    padding-right: 220px; }
  .pr_sm--225 {
    padding-right: 225px; }
  .pr_sm--230 {
    padding-right: 230px; }
  .pr_sm--235 {
    padding-right: 235px; }
  .pr_sm--240 {
    padding-right: 240px; }
  .pr_sm--245 {
    padding-right: 245px; }
  .pr_sm--250 {
    padding-right: 250px; } }

@media (min-width: 768px) {
  .pr_md--5 {
    padding-right: 5px; }
  .pr_md--10 {
    padding-right: 10px; }
  .pr_md--15 {
    padding-right: 15px; }
  .pr_md--20 {
    padding-right: 20px; }
  .pr_md--25 {
    padding-right: 25px; }
  .pr_md--30 {
    padding-right: 30px; }
  .pr_md--35 {
    padding-right: 35px; }
  .pr_md--40 {
    padding-right: 40px; }
  .pr_md--45 {
    padding-right: 45px; }
  .pr_md--50 {
    padding-right: 50px; }
  .pr_md--55 {
    padding-right: 55px; }
  .pr_md--60 {
    padding-right: 60px; }
  .pr_md--65 {
    padding-right: 65px; }
  .pr_md--70 {
    padding-right: 70px; }
  .pr_md--75 {
    padding-right: 75px; }
  .pr_md--80 {
    padding-right: 80px; }
  .pr_md--85 {
    padding-right: 85px; }
  .pr_md--90 {
    padding-right: 90px; }
  .pr_md--95 {
    padding-right: 95px; }
  .pr_md--100 {
    padding-right: 100px; }
  .pr_md--105 {
    padding-right: 105px; }
  .pr_md--110 {
    padding-right: 110px; }
  .pr_md--115 {
    padding-right: 115px; }
  .pr_md--120 {
    padding-right: 120px; }
  .pr_md--125 {
    padding-right: 125px; }
  .pr_md--130 {
    padding-right: 130px; }
  .pr_md--135 {
    padding-right: 135px; }
  .pr_md--140 {
    padding-right: 140px; }
  .pr_md--145 {
    padding-right: 145px; }
  .pr_md--150 {
    padding-right: 150px; }
  .pr_md--155 {
    padding-right: 155px; }
  .pr_md--160 {
    padding-right: 160px; }
  .pr_md--165 {
    padding-right: 165px; }
  .pr_md--170 {
    padding-right: 170px; }
  .pr_md--175 {
    padding-right: 175px; }
  .pr_md--180 {
    padding-right: 180px; }
  .pr_md--185 {
    padding-right: 185px; }
  .pr_md--190 {
    padding-right: 190px; }
  .pr_md--195 {
    padding-right: 195px; }
  .pr_md--200 {
    padding-right: 200px; }
  .pr_md--205 {
    padding-right: 205px; }
  .pr_md--210 {
    padding-right: 210px; }
  .pr_md--215 {
    padding-right: 215px; }
  .pr_md--220 {
    padding-right: 220px; }
  .pr_md--225 {
    padding-right: 225px; }
  .pr_md--230 {
    padding-right: 230px; }
  .pr_md--235 {
    padding-right: 235px; }
  .pr_md--240 {
    padding-right: 240px; }
  .pr_md--245 {
    padding-right: 245px; }
  .pr_md--250 {
    padding-right: 250px; } }

@media (min-width: 992px) {
  .pr_lg--5 {
    padding-right: 5px; }
  .pr_lg--10 {
    padding-right: 10px; }
  .pr_lg--15 {
    padding-right: 15px; }
  .pr_lg--20 {
    padding-right: 20px; }
  .pr_lg--25 {
    padding-right: 25px; }
  .pr_lg--30 {
    padding-right: 30px; }
  .pr_lg--35 {
    padding-right: 35px; }
  .pr_lg--40 {
    padding-right: 40px; }
  .pr_lg--45 {
    padding-right: 45px; }
  .pr_lg--50 {
    padding-right: 50px; }
  .pr_lg--55 {
    padding-right: 55px; }
  .pr_lg--60 {
    padding-right: 60px; }
  .pr_lg--65 {
    padding-right: 65px; }
  .pr_lg--70 {
    padding-right: 70px; }
  .pr_lg--75 {
    padding-right: 75px; }
  .pr_lg--80 {
    padding-right: 80px; }
  .pr_lg--85 {
    padding-right: 85px; }
  .pr_lg--90 {
    padding-right: 90px; }
  .pr_lg--95 {
    padding-right: 95px; }
  .pr_lg--100 {
    padding-right: 100px; }
  .pr_lg--105 {
    padding-right: 105px; }
  .pr_lg--110 {
    padding-right: 110px; }
  .pr_lg--115 {
    padding-right: 115px; }
  .pr_lg--120 {
    padding-right: 120px; }
  .pr_lg--125 {
    padding-right: 125px; }
  .pr_lg--130 {
    padding-right: 130px; }
  .pr_lg--135 {
    padding-right: 135px; }
  .pr_lg--140 {
    padding-right: 140px; }
  .pr_lg--145 {
    padding-right: 145px; }
  .pr_lg--150 {
    padding-right: 150px; }
  .pr_lg--155 {
    padding-right: 155px; }
  .pr_lg--160 {
    padding-right: 160px; }
  .pr_lg--165 {
    padding-right: 165px; }
  .pr_lg--170 {
    padding-right: 170px; }
  .pr_lg--175 {
    padding-right: 175px; }
  .pr_lg--180 {
    padding-right: 180px; }
  .pr_lg--185 {
    padding-right: 185px; }
  .pr_lg--190 {
    padding-right: 190px; }
  .pr_lg--195 {
    padding-right: 195px; }
  .pr_lg--200 {
    padding-right: 200px; }
  .pr_lg--205 {
    padding-right: 205px; }
  .pr_lg--210 {
    padding-right: 210px; }
  .pr_lg--215 {
    padding-right: 215px; }
  .pr_lg--220 {
    padding-right: 220px; }
  .pr_lg--225 {
    padding-right: 225px; }
  .pr_lg--230 {
    padding-right: 230px; }
  .pr_lg--235 {
    padding-right: 235px; }
  .pr_lg--240 {
    padding-right: 240px; }
  .pr_lg--245 {
    padding-right: 245px; }
  .pr_lg--250 {
    padding-right: 250px; } }

@media (min-width: 1200px) {
  .pr_xl--5 {
    padding-right: 5px; }
  .pr_xl--10 {
    padding-right: 10px; }
  .pr_xl--15 {
    padding-right: 15px; }
  .pr_xl--20 {
    padding-right: 20px; }
  .pr_xl--25 {
    padding-right: 25px; }
  .pr_xl--30 {
    padding-right: 30px; }
  .pr_xl--35 {
    padding-right: 35px; }
  .pr_xl--40 {
    padding-right: 40px; }
  .pr_xl--45 {
    padding-right: 45px; }
  .pr_xl--50 {
    padding-right: 50px; }
  .pr_xl--55 {
    padding-right: 55px; }
  .pr_xl--60 {
    padding-right: 60px; }
  .pr_xl--65 {
    padding-right: 65px; }
  .pr_xl--70 {
    padding-right: 70px; }
  .pr_xl--75 {
    padding-right: 75px; }
  .pr_xl--80 {
    padding-right: 80px; }
  .pr_xl--85 {
    padding-right: 85px; }
  .pr_xl--90 {
    padding-right: 90px; }
  .pr_xl--95 {
    padding-right: 95px; }
  .pr_xl--100 {
    padding-right: 100px; }
  .pr_xl--105 {
    padding-right: 105px; }
  .pr_xl--110 {
    padding-right: 110px; }
  .pr_xl--115 {
    padding-right: 115px; }
  .pr_xl--120 {
    padding-right: 120px; }
  .pr_xl--125 {
    padding-right: 125px; }
  .pr_xl--130 {
    padding-right: 130px; }
  .pr_xl--135 {
    padding-right: 135px; }
  .pr_xl--140 {
    padding-right: 140px; }
  .pr_xl--145 {
    padding-right: 145px; }
  .pr_xl--150 {
    padding-right: 150px; }
  .pr_xl--155 {
    padding-right: 155px; }
  .pr_xl--160 {
    padding-right: 160px; }
  .pr_xl--165 {
    padding-right: 165px; }
  .pr_xl--170 {
    padding-right: 170px; }
  .pr_xl--175 {
    padding-right: 175px; }
  .pr_xl--180 {
    padding-right: 180px; }
  .pr_xl--185 {
    padding-right: 185px; }
  .pr_xl--190 {
    padding-right: 190px; }
  .pr_xl--195 {
    padding-right: 195px; }
  .pr_xl--200 {
    padding-right: 200px; }
  .pr_xl--205 {
    padding-right: 205px; }
  .pr_xl--210 {
    padding-right: 210px; }
  .pr_xl--215 {
    padding-right: 215px; }
  .pr_xl--220 {
    padding-right: 220px; }
  .pr_xl--225 {
    padding-right: 225px; }
  .pr_xl--230 {
    padding-right: 230px; }
  .pr_xl--235 {
    padding-right: 235px; }
  .pr_xl--240 {
    padding-right: 240px; }
  .pr_xl--245 {
    padding-right: 245px; }
  .pr_xl--250 {
    padding-right: 250px; } }

.mt--5 {
  margin-top: 5px; }

.mt--10 {
  margin-top: 10px; }

.mt--15 {
  margin-top: 15px; }

.mt--20 {
  margin-top: 20px; }

.mt--25 {
  margin-top: 25px; }

.mt--30 {
  margin-top: 30px; }

.mt--35 {
  margin-top: 35px; }

.mt--40 {
  margin-top: 40px; }

.mt--45 {
  margin-top: 45px; }

.mt--50 {
  margin-top: 50px; }

.mt--55 {
  margin-top: 55px; }

.mt--60 {
  margin-top: 60px; }

.mt--65 {
  margin-top: 65px; }

.mt--70 {
  margin-top: 70px; }

.mt--75 {
  margin-top: 75px; }

.mt--80 {
  margin-top: 80px; }

.mt--85 {
  margin-top: 85px; }

.mt--90 {
  margin-top: 90px; }

.mt--95 {
  margin-top: 95px; }

.mt--100 {
  margin-top: 100px; }

.mt--105 {
  margin-top: 105px; }

.mt--110 {
  margin-top: 110px; }

.mt--115 {
  margin-top: 115px; }

.mt--120 {
  margin-top: 120px; }

.mt--125 {
  margin-top: 125px; }

.mt--130 {
  margin-top: 130px; }

.mt--135 {
  margin-top: 135px; }

.mt--140 {
  margin-top: 140px; }

.mt--145 {
  margin-top: 145px; }

.mt--150 {
  margin-top: 150px; }

.mt--155 {
  margin-top: 155px; }

.mt--160 {
  margin-top: 160px; }

.mt--165 {
  margin-top: 165px; }

.mt--170 {
  margin-top: 170px; }

.mt--175 {
  margin-top: 175px; }

.mt--180 {
  margin-top: 180px; }

.mt--185 {
  margin-top: 185px; }

.mt--190 {
  margin-top: 190px; }

.mt--195 {
  margin-top: 195px; }

.mt--200 {
  margin-top: 200px; }

.mt--205 {
  margin-top: 205px; }

.mt--210 {
  margin-top: 210px; }

.mt--215 {
  margin-top: 215px; }

.mt--220 {
  margin-top: 220px; }

.mt--225 {
  margin-top: 225px; }

.mt--230 {
  margin-top: 230px; }

.mt--235 {
  margin-top: 235px; }

.mt--240 {
  margin-top: 240px; }

.mt--245 {
  margin-top: 245px; }

.mt--250 {
  margin-top: 250px; }

@media (min-width: 576px) {
  .mt_sm--5 {
    margin-top: 5px; }
  .mt_sm--10 {
    margin-top: 10px; }
  .mt_sm--15 {
    margin-top: 15px; }
  .mt_sm--20 {
    margin-top: 20px; }
  .mt_sm--25 {
    margin-top: 25px; }
  .mt_sm--30 {
    margin-top: 30px; }
  .mt_sm--35 {
    margin-top: 35px; }
  .mt_sm--40 {
    margin-top: 40px; }
  .mt_sm--45 {
    margin-top: 45px; }
  .mt_sm--50 {
    margin-top: 50px; }
  .mt_sm--55 {
    margin-top: 55px; }
  .mt_sm--60 {
    margin-top: 60px; }
  .mt_sm--65 {
    margin-top: 65px; }
  .mt_sm--70 {
    margin-top: 70px; }
  .mt_sm--75 {
    margin-top: 75px; }
  .mt_sm--80 {
    margin-top: 80px; }
  .mt_sm--85 {
    margin-top: 85px; }
  .mt_sm--90 {
    margin-top: 90px; }
  .mt_sm--95 {
    margin-top: 95px; }
  .mt_sm--100 {
    margin-top: 100px; }
  .mt_sm--105 {
    margin-top: 105px; }
  .mt_sm--110 {
    margin-top: 110px; }
  .mt_sm--115 {
    margin-top: 115px; }
  .mt_sm--120 {
    margin-top: 120px; }
  .mt_sm--125 {
    margin-top: 125px; }
  .mt_sm--130 {
    margin-top: 130px; }
  .mt_sm--135 {
    margin-top: 135px; }
  .mt_sm--140 {
    margin-top: 140px; }
  .mt_sm--145 {
    margin-top: 145px; }
  .mt_sm--150 {
    margin-top: 150px; }
  .mt_sm--155 {
    margin-top: 155px; }
  .mt_sm--160 {
    margin-top: 160px; }
  .mt_sm--165 {
    margin-top: 165px; }
  .mt_sm--170 {
    margin-top: 170px; }
  .mt_sm--175 {
    margin-top: 175px; }
  .mt_sm--180 {
    margin-top: 180px; }
  .mt_sm--185 {
    margin-top: 185px; }
  .mt_sm--190 {
    margin-top: 190px; }
  .mt_sm--195 {
    margin-top: 195px; }
  .mt_sm--200 {
    margin-top: 200px; }
  .mt_sm--205 {
    margin-top: 205px; }
  .mt_sm--210 {
    margin-top: 210px; }
  .mt_sm--215 {
    margin-top: 215px; }
  .mt_sm--220 {
    margin-top: 220px; }
  .mt_sm--225 {
    margin-top: 225px; }
  .mt_sm--230 {
    margin-top: 230px; }
  .mt_sm--235 {
    margin-top: 235px; }
  .mt_sm--240 {
    margin-top: 240px; }
  .mt_sm--245 {
    margin-top: 245px; }
  .mt_sm--250 {
    margin-top: 250px; } }

@media (min-width: 768px) {
  .mt_md--5 {
    margin-top: 5px; }
  .mt_md--10 {
    margin-top: 10px; }
  .mt_md--15 {
    margin-top: 15px; }
  .mt_md--20 {
    margin-top: 20px; }
  .mt_md--25 {
    margin-top: 25px; }
  .mt_md--30 {
    margin-top: 30px; }
  .mt_md--35 {
    margin-top: 35px; }
  .mt_md--40 {
    margin-top: 40px; }
  .mt_md--45 {
    margin-top: 45px; }
  .mt_md--50 {
    margin-top: 50px; }
  .mt_md--55 {
    margin-top: 55px; }
  .mt_md--60 {
    margin-top: 60px; }
  .mt_md--65 {
    margin-top: 65px; }
  .mt_md--70 {
    margin-top: 70px; }
  .mt_md--75 {
    margin-top: 75px; }
  .mt_md--80 {
    margin-top: 80px; }
  .mt_md--85 {
    margin-top: 85px; }
  .mt_md--90 {
    margin-top: 90px; }
  .mt_md--95 {
    margin-top: 95px; }
  .mt_md--100 {
    margin-top: 100px; }
  .mt_md--105 {
    margin-top: 105px; }
  .mt_md--110 {
    margin-top: 110px; }
  .mt_md--115 {
    margin-top: 115px; }
  .mt_md--120 {
    margin-top: 120px; }
  .mt_md--125 {
    margin-top: 125px; }
  .mt_md--130 {
    margin-top: 130px; }
  .mt_md--135 {
    margin-top: 135px; }
  .mt_md--140 {
    margin-top: 140px; }
  .mt_md--145 {
    margin-top: 145px; }
  .mt_md--150 {
    margin-top: 150px; }
  .mt_md--155 {
    margin-top: 155px; }
  .mt_md--160 {
    margin-top: 160px; }
  .mt_md--165 {
    margin-top: 165px; }
  .mt_md--170 {
    margin-top: 170px; }
  .mt_md--175 {
    margin-top: 175px; }
  .mt_md--180 {
    margin-top: 180px; }
  .mt_md--185 {
    margin-top: 185px; }
  .mt_md--190 {
    margin-top: 190px; }
  .mt_md--195 {
    margin-top: 195px; }
  .mt_md--200 {
    margin-top: 200px; }
  .mt_md--205 {
    margin-top: 205px; }
  .mt_md--210 {
    margin-top: 210px; }
  .mt_md--215 {
    margin-top: 215px; }
  .mt_md--220 {
    margin-top: 220px; }
  .mt_md--225 {
    margin-top: 225px; }
  .mt_md--230 {
    margin-top: 230px; }
  .mt_md--235 {
    margin-top: 235px; }
  .mt_md--240 {
    margin-top: 240px; }
  .mt_md--245 {
    margin-top: 245px; }
  .mt_md--250 {
    margin-top: 250px; } }

@media (min-width: 992px) {
  .mt_lg--5 {
    margin-top: 5px; }
  .mt_lg--10 {
    margin-top: 10px; }
  .mt_lg--15 {
    margin-top: 15px; }
  .mt_lg--20 {
    margin-top: 20px; }
  .mt_lg--25 {
    margin-top: 25px; }
  .mt_lg--30 {
    margin-top: 30px; }
  .mt_lg--35 {
    margin-top: 35px; }
  .mt_lg--40 {
    margin-top: 40px; }
  .mt_lg--45 {
    margin-top: 45px; }
  .mt_lg--50 {
    margin-top: 50px; }
  .mt_lg--55 {
    margin-top: 55px; }
  .mt_lg--60 {
    margin-top: 60px; }
  .mt_lg--65 {
    margin-top: 65px; }
  .mt_lg--70 {
    margin-top: 70px; }
  .mt_lg--75 {
    margin-top: 75px; }
  .mt_lg--80 {
    margin-top: 80px; }
  .mt_lg--85 {
    margin-top: 85px; }
  .mt_lg--90 {
    margin-top: 90px; }
  .mt_lg--95 {
    margin-top: 95px; }
  .mt_lg--100 {
    margin-top: 100px; }
  .mt_lg--105 {
    margin-top: 105px; }
  .mt_lg--110 {
    margin-top: 110px; }
  .mt_lg--115 {
    margin-top: 115px; }
  .mt_lg--120 {
    margin-top: 120px; }
  .mt_lg--125 {
    margin-top: 125px; }
  .mt_lg--130 {
    margin-top: 130px; }
  .mt_lg--135 {
    margin-top: 135px; }
  .mt_lg--140 {
    margin-top: 140px; }
  .mt_lg--145 {
    margin-top: 145px; }
  .mt_lg--150 {
    margin-top: 150px; }
  .mt_lg--155 {
    margin-top: 155px; }
  .mt_lg--160 {
    margin-top: 160px; }
  .mt_lg--165 {
    margin-top: 165px; }
  .mt_lg--170 {
    margin-top: 170px; }
  .mt_lg--175 {
    margin-top: 175px; }
  .mt_lg--180 {
    margin-top: 180px; }
  .mt_lg--185 {
    margin-top: 185px; }
  .mt_lg--190 {
    margin-top: 190px; }
  .mt_lg--195 {
    margin-top: 195px; }
  .mt_lg--200 {
    margin-top: 200px; }
  .mt_lg--205 {
    margin-top: 205px; }
  .mt_lg--210 {
    margin-top: 210px; }
  .mt_lg--215 {
    margin-top: 215px; }
  .mt_lg--220 {
    margin-top: 220px; }
  .mt_lg--225 {
    margin-top: 225px; }
  .mt_lg--230 {
    margin-top: 230px; }
  .mt_lg--235 {
    margin-top: 235px; }
  .mt_lg--240 {
    margin-top: 240px; }
  .mt_lg--245 {
    margin-top: 245px; }
  .mt_lg--250 {
    margin-top: 250px; } }

@media (min-width: 1200px) {
  .mt_xl--5 {
    margin-top: 5px; }
  .mt_xl--10 {
    margin-top: 10px; }
  .mt_xl--15 {
    margin-top: 15px; }
  .mt_xl--20 {
    margin-top: 20px; }
  .mt_xl--25 {
    margin-top: 25px; }
  .mt_xl--30 {
    margin-top: 30px; }
  .mt_xl--35 {
    margin-top: 35px; }
  .mt_xl--40 {
    margin-top: 40px; }
  .mt_xl--45 {
    margin-top: 45px; }
  .mt_xl--50 {
    margin-top: 50px; }
  .mt_xl--55 {
    margin-top: 55px; }
  .mt_xl--60 {
    margin-top: 60px; }
  .mt_xl--65 {
    margin-top: 65px; }
  .mt_xl--70 {
    margin-top: 70px; }
  .mt_xl--75 {
    margin-top: 75px; }
  .mt_xl--80 {
    margin-top: 80px; }
  .mt_xl--85 {
    margin-top: 85px; }
  .mt_xl--90 {
    margin-top: 90px; }
  .mt_xl--95 {
    margin-top: 95px; }
  .mt_xl--100 {
    margin-top: 100px; }
  .mt_xl--105 {
    margin-top: 105px; }
  .mt_xl--110 {
    margin-top: 110px; }
  .mt_xl--115 {
    margin-top: 115px; }
  .mt_xl--120 {
    margin-top: 120px; }
  .mt_xl--125 {
    margin-top: 125px; }
  .mt_xl--130 {
    margin-top: 130px; }
  .mt_xl--135 {
    margin-top: 135px; }
  .mt_xl--140 {
    margin-top: 140px; }
  .mt_xl--145 {
    margin-top: 145px; }
  .mt_xl--150 {
    margin-top: 150px; }
  .mt_xl--155 {
    margin-top: 155px; }
  .mt_xl--160 {
    margin-top: 160px; }
  .mt_xl--165 {
    margin-top: 165px; }
  .mt_xl--170 {
    margin-top: 170px; }
  .mt_xl--175 {
    margin-top: 175px; }
  .mt_xl--180 {
    margin-top: 180px; }
  .mt_xl--185 {
    margin-top: 185px; }
  .mt_xl--190 {
    margin-top: 190px; }
  .mt_xl--195 {
    margin-top: 195px; }
  .mt_xl--200 {
    margin-top: 200px; }
  .mt_xl--205 {
    margin-top: 205px; }
  .mt_xl--210 {
    margin-top: 210px; }
  .mt_xl--215 {
    margin-top: 215px; }
  .mt_xl--220 {
    margin-top: 220px; }
  .mt_xl--225 {
    margin-top: 225px; }
  .mt_xl--230 {
    margin-top: 230px; }
  .mt_xl--235 {
    margin-top: 235px; }
  .mt_xl--240 {
    margin-top: 240px; }
  .mt_xl--245 {
    margin-top: 245px; }
  .mt_xl--250 {
    margin-top: 250px; } }

.mb--5 {
  margin-bottom: 5px; }

.mb--10 {
  margin-bottom: 10px; }

.mb--15 {
  margin-bottom: 15px; }

.mb--20 {
  margin-bottom: 20px; }

.mb--25 {
  margin-bottom: 25px; }

.mb--30 {
  margin-bottom: 30px; }

.mb--35 {
  margin-bottom: 35px; }

.mb--40 {
  margin-bottom: 40px; }

.mb--45 {
  margin-bottom: 45px; }

.mb--50 {
  margin-bottom: 50px; }

.mb--55 {
  margin-bottom: 55px; }

.mb--60 {
  margin-bottom: 60px; }

.mb--65 {
  margin-bottom: 65px; }

.mb--70 {
  margin-bottom: 70px; }

.mb--75 {
  margin-bottom: 75px; }

.mb--80 {
  margin-bottom: 80px; }

.mb--85 {
  margin-bottom: 85px; }

.mb--90 {
  margin-bottom: 90px; }

.mb--95 {
  margin-bottom: 95px; }

.mb--100 {
  margin-bottom: 100px; }

.mb--105 {
  margin-bottom: 105px; }

.mb--110 {
  margin-bottom: 110px; }

.mb--115 {
  margin-bottom: 115px; }

.mb--120 {
  margin-bottom: 120px; }

.mb--125 {
  margin-bottom: 125px; }

.mb--130 {
  margin-bottom: 130px; }

.mb--135 {
  margin-bottom: 135px; }

.mb--140 {
  margin-bottom: 140px; }

.mb--145 {
  margin-bottom: 145px; }

.mb--150 {
  margin-bottom: 150px; }

.mb--155 {
  margin-bottom: 155px; }

.mb--160 {
  margin-bottom: 160px; }

.mb--165 {
  margin-bottom: 165px; }

.mb--170 {
  margin-bottom: 170px; }

.mb--175 {
  margin-bottom: 175px; }

.mb--180 {
  margin-bottom: 180px; }

.mb--185 {
  margin-bottom: 185px; }

.mb--190 {
  margin-bottom: 190px; }

.mb--195 {
  margin-bottom: 195px; }

.mb--200 {
  margin-bottom: 200px; }

.mb--205 {
  margin-bottom: 205px; }

.mb--210 {
  margin-bottom: 210px; }

.mb--215 {
  margin-bottom: 215px; }

.mb--220 {
  margin-bottom: 220px; }

.mb--225 {
  margin-bottom: 225px; }

.mb--230 {
  margin-bottom: 230px; }

.mb--235 {
  margin-bottom: 235px; }

.mb--240 {
  margin-bottom: 240px; }

.mb--245 {
  margin-bottom: 245px; }

.mb--250 {
  margin-bottom: 250px; }

@media (min-width: 576px) {
  .mb_sm--5 {
    margin-bottom: 5px; }
  .mb_sm--10 {
    margin-bottom: 10px; }
  .mb_sm--15 {
    margin-bottom: 15px; }
  .mb_sm--20 {
    margin-bottom: 20px; }
  .mb_sm--25 {
    margin-bottom: 25px; }
  .mb_sm--30 {
    margin-bottom: 30px; }
  .mb_sm--35 {
    margin-bottom: 35px; }
  .mb_sm--40 {
    margin-bottom: 40px; }
  .mb_sm--45 {
    margin-bottom: 45px; }
  .mb_sm--50 {
    margin-bottom: 50px; }
  .mb_sm--55 {
    margin-bottom: 55px; }
  .mb_sm--60 {
    margin-bottom: 60px; }
  .mb_sm--65 {
    margin-bottom: 65px; }
  .mb_sm--70 {
    margin-bottom: 70px; }
  .mb_sm--75 {
    margin-bottom: 75px; }
  .mb_sm--80 {
    margin-bottom: 80px; }
  .mb_sm--85 {
    margin-bottom: 85px; }
  .mb_sm--90 {
    margin-bottom: 90px; }
  .mb_sm--95 {
    margin-bottom: 95px; }
  .mb_sm--100 {
    margin-bottom: 100px; }
  .mb_sm--105 {
    margin-bottom: 105px; }
  .mb_sm--110 {
    margin-bottom: 110px; }
  .mb_sm--115 {
    margin-bottom: 115px; }
  .mb_sm--120 {
    margin-bottom: 120px; }
  .mb_sm--125 {
    margin-bottom: 125px; }
  .mb_sm--130 {
    margin-bottom: 130px; }
  .mb_sm--135 {
    margin-bottom: 135px; }
  .mb_sm--140 {
    margin-bottom: 140px; }
  .mb_sm--145 {
    margin-bottom: 145px; }
  .mb_sm--150 {
    margin-bottom: 150px; }
  .mb_sm--155 {
    margin-bottom: 155px; }
  .mb_sm--160 {
    margin-bottom: 160px; }
  .mb_sm--165 {
    margin-bottom: 165px; }
  .mb_sm--170 {
    margin-bottom: 170px; }
  .mb_sm--175 {
    margin-bottom: 175px; }
  .mb_sm--180 {
    margin-bottom: 180px; }
  .mb_sm--185 {
    margin-bottom: 185px; }
  .mb_sm--190 {
    margin-bottom: 190px; }
  .mb_sm--195 {
    margin-bottom: 195px; }
  .mb_sm--200 {
    margin-bottom: 200px; }
  .mb_sm--205 {
    margin-bottom: 205px; }
  .mb_sm--210 {
    margin-bottom: 210px; }
  .mb_sm--215 {
    margin-bottom: 215px; }
  .mb_sm--220 {
    margin-bottom: 220px; }
  .mb_sm--225 {
    margin-bottom: 225px; }
  .mb_sm--230 {
    margin-bottom: 230px; }
  .mb_sm--235 {
    margin-bottom: 235px; }
  .mb_sm--240 {
    margin-bottom: 240px; }
  .mb_sm--245 {
    margin-bottom: 245px; }
  .mb_sm--250 {
    margin-bottom: 250px; } }

@media (min-width: 768px) {
  .mb_md--5 {
    margin-bottom: 5px; }
  .mb_md--10 {
    margin-bottom: 10px; }
  .mb_md--15 {
    margin-bottom: 15px; }
  .mb_md--20 {
    margin-bottom: 20px; }
  .mb_md--25 {
    margin-bottom: 25px; }
  .mb_md--30 {
    margin-bottom: 30px; }
  .mb_md--35 {
    margin-bottom: 35px; }
  .mb_md--40 {
    margin-bottom: 40px; }
  .mb_md--45 {
    margin-bottom: 45px; }
  .mb_md--50 {
    margin-bottom: 50px; }
  .mb_md--55 {
    margin-bottom: 55px; }
  .mb_md--60 {
    margin-bottom: 60px; }
  .mb_md--65 {
    margin-bottom: 65px; }
  .mb_md--70 {
    margin-bottom: 70px; }
  .mb_md--75 {
    margin-bottom: 75px; }
  .mb_md--80 {
    margin-bottom: 80px; }
  .mb_md--85 {
    margin-bottom: 85px; }
  .mb_md--90 {
    margin-bottom: 90px; }
  .mb_md--95 {
    margin-bottom: 95px; }
  .mb_md--100 {
    margin-bottom: 100px; }
  .mb_md--105 {
    margin-bottom: 105px; }
  .mb_md--110 {
    margin-bottom: 110px; }
  .mb_md--115 {
    margin-bottom: 115px; }
  .mb_md--120 {
    margin-bottom: 120px; }
  .mb_md--125 {
    margin-bottom: 125px; }
  .mb_md--130 {
    margin-bottom: 130px; }
  .mb_md--135 {
    margin-bottom: 135px; }
  .mb_md--140 {
    margin-bottom: 140px; }
  .mb_md--145 {
    margin-bottom: 145px; }
  .mb_md--150 {
    margin-bottom: 150px; }
  .mb_md--155 {
    margin-bottom: 155px; }
  .mb_md--160 {
    margin-bottom: 160px; }
  .mb_md--165 {
    margin-bottom: 165px; }
  .mb_md--170 {
    margin-bottom: 170px; }
  .mb_md--175 {
    margin-bottom: 175px; }
  .mb_md--180 {
    margin-bottom: 180px; }
  .mb_md--185 {
    margin-bottom: 185px; }
  .mb_md--190 {
    margin-bottom: 190px; }
  .mb_md--195 {
    margin-bottom: 195px; }
  .mb_md--200 {
    margin-bottom: 200px; }
  .mb_md--205 {
    margin-bottom: 205px; }
  .mb_md--210 {
    margin-bottom: 210px; }
  .mb_md--215 {
    margin-bottom: 215px; }
  .mb_md--220 {
    margin-bottom: 220px; }
  .mb_md--225 {
    margin-bottom: 225px; }
  .mb_md--230 {
    margin-bottom: 230px; }
  .mb_md--235 {
    margin-bottom: 235px; }
  .mb_md--240 {
    margin-bottom: 240px; }
  .mb_md--245 {
    margin-bottom: 245px; }
  .mb_md--250 {
    margin-bottom: 250px; } }

@media (min-width: 992px) {
  .mb_lg--5 {
    margin-bottom: 5px; }
  .mb_lg--10 {
    margin-bottom: 10px; }
  .mb_lg--15 {
    margin-bottom: 15px; }
  .mb_lg--20 {
    margin-bottom: 20px; }
  .mb_lg--25 {
    margin-bottom: 25px; }
  .mb_lg--30 {
    margin-bottom: 30px; }
  .mb_lg--35 {
    margin-bottom: 35px; }
  .mb_lg--40 {
    margin-bottom: 40px; }
  .mb_lg--45 {
    margin-bottom: 45px; }
  .mb_lg--50 {
    margin-bottom: 50px; }
  .mb_lg--55 {
    margin-bottom: 55px; }
  .mb_lg--60 {
    margin-bottom: 60px; }
  .mb_lg--65 {
    margin-bottom: 65px; }
  .mb_lg--70 {
    margin-bottom: 70px; }
  .mb_lg--75 {
    margin-bottom: 75px; }
  .mb_lg--80 {
    margin-bottom: 80px; }
  .mb_lg--85 {
    margin-bottom: 85px; }
  .mb_lg--90 {
    margin-bottom: 90px; }
  .mb_lg--95 {
    margin-bottom: 95px; }
  .mb_lg--100 {
    margin-bottom: 100px; }
  .mb_lg--105 {
    margin-bottom: 105px; }
  .mb_lg--110 {
    margin-bottom: 110px; }
  .mb_lg--115 {
    margin-bottom: 115px; }
  .mb_lg--120 {
    margin-bottom: 120px; }
  .mb_lg--125 {
    margin-bottom: 125px; }
  .mb_lg--130 {
    margin-bottom: 130px; }
  .mb_lg--135 {
    margin-bottom: 135px; }
  .mb_lg--140 {
    margin-bottom: 140px; }
  .mb_lg--145 {
    margin-bottom: 145px; }
  .mb_lg--150 {
    margin-bottom: 150px; }
  .mb_lg--155 {
    margin-bottom: 155px; }
  .mb_lg--160 {
    margin-bottom: 160px; }
  .mb_lg--165 {
    margin-bottom: 165px; }
  .mb_lg--170 {
    margin-bottom: 170px; }
  .mb_lg--175 {
    margin-bottom: 175px; }
  .mb_lg--180 {
    margin-bottom: 180px; }
  .mb_lg--185 {
    margin-bottom: 185px; }
  .mb_lg--190 {
    margin-bottom: 190px; }
  .mb_lg--195 {
    margin-bottom: 195px; }
  .mb_lg--200 {
    margin-bottom: 200px; }
  .mb_lg--205 {
    margin-bottom: 205px; }
  .mb_lg--210 {
    margin-bottom: 210px; }
  .mb_lg--215 {
    margin-bottom: 215px; }
  .mb_lg--220 {
    margin-bottom: 220px; }
  .mb_lg--225 {
    margin-bottom: 225px; }
  .mb_lg--230 {
    margin-bottom: 230px; }
  .mb_lg--235 {
    margin-bottom: 235px; }
  .mb_lg--240 {
    margin-bottom: 240px; }
  .mb_lg--245 {
    margin-bottom: 245px; }
  .mb_lg--250 {
    margin-bottom: 250px; } }

@media (min-width: 1200px) {
  .mb_xl--5 {
    margin-bottom: 5px; }
  .mb_xl--10 {
    margin-bottom: 10px; }
  .mb_xl--15 {
    margin-bottom: 15px; }
  .mb_xl--20 {
    margin-bottom: 20px; }
  .mb_xl--25 {
    margin-bottom: 25px; }
  .mb_xl--30 {
    margin-bottom: 30px; }
  .mb_xl--35 {
    margin-bottom: 35px; }
  .mb_xl--40 {
    margin-bottom: 40px; }
  .mb_xl--45 {
    margin-bottom: 45px; }
  .mb_xl--50 {
    margin-bottom: 50px; }
  .mb_xl--55 {
    margin-bottom: 55px; }
  .mb_xl--60 {
    margin-bottom: 60px; }
  .mb_xl--65 {
    margin-bottom: 65px; }
  .mb_xl--70 {
    margin-bottom: 70px; }
  .mb_xl--75 {
    margin-bottom: 75px; }
  .mb_xl--80 {
    margin-bottom: 80px; }
  .mb_xl--85 {
    margin-bottom: 85px; }
  .mb_xl--90 {
    margin-bottom: 90px; }
  .mb_xl--95 {
    margin-bottom: 95px; }
  .mb_xl--100 {
    margin-bottom: 100px; }
  .mb_xl--105 {
    margin-bottom: 105px; }
  .mb_xl--110 {
    margin-bottom: 110px; }
  .mb_xl--115 {
    margin-bottom: 115px; }
  .mb_xl--120 {
    margin-bottom: 120px; }
  .mb_xl--125 {
    margin-bottom: 125px; }
  .mb_xl--130 {
    margin-bottom: 130px; }
  .mb_xl--135 {
    margin-bottom: 135px; }
  .mb_xl--140 {
    margin-bottom: 140px; }
  .mb_xl--145 {
    margin-bottom: 145px; }
  .mb_xl--150 {
    margin-bottom: 150px; }
  .mb_xl--155 {
    margin-bottom: 155px; }
  .mb_xl--160 {
    margin-bottom: 160px; }
  .mb_xl--165 {
    margin-bottom: 165px; }
  .mb_xl--170 {
    margin-bottom: 170px; }
  .mb_xl--175 {
    margin-bottom: 175px; }
  .mb_xl--180 {
    margin-bottom: 180px; }
  .mb_xl--185 {
    margin-bottom: 185px; }
  .mb_xl--190 {
    margin-bottom: 190px; }
  .mb_xl--195 {
    margin-bottom: 195px; }
  .mb_xl--200 {
    margin-bottom: 200px; }
  .mb_xl--205 {
    margin-bottom: 205px; }
  .mb_xl--210 {
    margin-bottom: 210px; }
  .mb_xl--215 {
    margin-bottom: 215px; }
  .mb_xl--220 {
    margin-bottom: 220px; }
  .mb_xl--225 {
    margin-bottom: 225px; }
  .mb_xl--230 {
    margin-bottom: 230px; }
  .mb_xl--235 {
    margin-bottom: 235px; }
  .mb_xl--240 {
    margin-bottom: 240px; }
  .mb_xl--245 {
    margin-bottom: 245px; }
  .mb_xl--250 {
    margin-bottom: 250px; } }

.mr--5 {
  margin-right: 5px; }

.mr--10 {
  margin-right: 10px; }

.mr--15 {
  margin-right: 15px; }

.mr--20 {
  margin-right: 20px; }

.mr--25 {
  margin-right: 25px; }

.mr--30 {
  margin-right: 30px; }

.mr--35 {
  margin-right: 35px; }

.mr--40 {
  margin-right: 40px; }

.mr--45 {
  margin-right: 45px; }

.mr--50 {
  margin-right: 50px; }

.mr--55 {
  margin-right: 55px; }

.mr--60 {
  margin-right: 60px; }

.mr--65 {
  margin-right: 65px; }

.mr--70 {
  margin-right: 70px; }

.mr--75 {
  margin-right: 75px; }

.mr--80 {
  margin-right: 80px; }

.mr--85 {
  margin-right: 85px; }

.mr--90 {
  margin-right: 90px; }

.mr--95 {
  margin-right: 95px; }

.mr--100 {
  margin-right: 100px; }

.mr--105 {
  margin-right: 105px; }

.mr--110 {
  margin-right: 110px; }

.mr--115 {
  margin-right: 115px; }

.mr--120 {
  margin-right: 120px; }

.mr--125 {
  margin-right: 125px; }

.mr--130 {
  margin-right: 130px; }

.mr--135 {
  margin-right: 135px; }

.mr--140 {
  margin-right: 140px; }

.mr--145 {
  margin-right: 145px; }

.mr--150 {
  margin-right: 150px; }

.mr--155 {
  margin-right: 155px; }

.mr--160 {
  margin-right: 160px; }

.mr--165 {
  margin-right: 165px; }

.mr--170 {
  margin-right: 170px; }

.mr--175 {
  margin-right: 175px; }

.mr--180 {
  margin-right: 180px; }

.mr--185 {
  margin-right: 185px; }

.mr--190 {
  margin-right: 190px; }

.mr--195 {
  margin-right: 195px; }

.mr--200 {
  margin-right: 200px; }

.mr--205 {
  margin-right: 205px; }

.mr--210 {
  margin-right: 210px; }

.mr--215 {
  margin-right: 215px; }

.mr--220 {
  margin-right: 220px; }

.mr--225 {
  margin-right: 225px; }

.mr--230 {
  margin-right: 230px; }

.mr--235 {
  margin-right: 235px; }

.mr--240 {
  margin-right: 240px; }

.mr--245 {
  margin-right: 245px; }

.mr--250 {
  margin-right: 250px; }

@media (min-width: 576px) {
  .mr_sm--5 {
    margin-right: 5px; }
  .mr_sm--10 {
    margin-right: 10px; }
  .mr_sm--15 {
    margin-right: 15px; }
  .mr_sm--20 {
    margin-right: 20px; }
  .mr_sm--25 {
    margin-right: 25px; }
  .mr_sm--30 {
    margin-right: 30px; }
  .mr_sm--35 {
    margin-right: 35px; }
  .mr_sm--40 {
    margin-right: 40px; }
  .mr_sm--45 {
    margin-right: 45px; }
  .mr_sm--50 {
    margin-right: 50px; }
  .mr_sm--55 {
    margin-right: 55px; }
  .mr_sm--60 {
    margin-right: 60px; }
  .mr_sm--65 {
    margin-right: 65px; }
  .mr_sm--70 {
    margin-right: 70px; }
  .mr_sm--75 {
    margin-right: 75px; }
  .mr_sm--80 {
    margin-right: 80px; }
  .mr_sm--85 {
    margin-right: 85px; }
  .mr_sm--90 {
    margin-right: 90px; }
  .mr_sm--95 {
    margin-right: 95px; }
  .mr_sm--100 {
    margin-right: 100px; }
  .mr_sm--105 {
    margin-right: 105px; }
  .mr_sm--110 {
    margin-right: 110px; }
  .mr_sm--115 {
    margin-right: 115px; }
  .mr_sm--120 {
    margin-right: 120px; }
  .mr_sm--125 {
    margin-right: 125px; }
  .mr_sm--130 {
    margin-right: 130px; }
  .mr_sm--135 {
    margin-right: 135px; }
  .mr_sm--140 {
    margin-right: 140px; }
  .mr_sm--145 {
    margin-right: 145px; }
  .mr_sm--150 {
    margin-right: 150px; }
  .mr_sm--155 {
    margin-right: 155px; }
  .mr_sm--160 {
    margin-right: 160px; }
  .mr_sm--165 {
    margin-right: 165px; }
  .mr_sm--170 {
    margin-right: 170px; }
  .mr_sm--175 {
    margin-right: 175px; }
  .mr_sm--180 {
    margin-right: 180px; }
  .mr_sm--185 {
    margin-right: 185px; }
  .mr_sm--190 {
    margin-right: 190px; }
  .mr_sm--195 {
    margin-right: 195px; }
  .mr_sm--200 {
    margin-right: 200px; }
  .mr_sm--205 {
    margin-right: 205px; }
  .mr_sm--210 {
    margin-right: 210px; }
  .mr_sm--215 {
    margin-right: 215px; }
  .mr_sm--220 {
    margin-right: 220px; }
  .mr_sm--225 {
    margin-right: 225px; }
  .mr_sm--230 {
    margin-right: 230px; }
  .mr_sm--235 {
    margin-right: 235px; }
  .mr_sm--240 {
    margin-right: 240px; }
  .mr_sm--245 {
    margin-right: 245px; }
  .mr_sm--250 {
    margin-right: 250px; } }

@media (min-width: 768px) {
  .mr_md--5 {
    margin-right: 5px; }
  .mr_md--10 {
    margin-right: 10px; }
  .mr_md--15 {
    margin-right: 15px; }
  .mr_md--20 {
    margin-right: 20px; }
  .mr_md--25 {
    margin-right: 25px; }
  .mr_md--30 {
    margin-right: 30px; }
  .mr_md--35 {
    margin-right: 35px; }
  .mr_md--40 {
    margin-right: 40px; }
  .mr_md--45 {
    margin-right: 45px; }
  .mr_md--50 {
    margin-right: 50px; }
  .mr_md--55 {
    margin-right: 55px; }
  .mr_md--60 {
    margin-right: 60px; }
  .mr_md--65 {
    margin-right: 65px; }
  .mr_md--70 {
    margin-right: 70px; }
  .mr_md--75 {
    margin-right: 75px; }
  .mr_md--80 {
    margin-right: 80px; }
  .mr_md--85 {
    margin-right: 85px; }
  .mr_md--90 {
    margin-right: 90px; }
  .mr_md--95 {
    margin-right: 95px; }
  .mr_md--100 {
    margin-right: 100px; }
  .mr_md--105 {
    margin-right: 105px; }
  .mr_md--110 {
    margin-right: 110px; }
  .mr_md--115 {
    margin-right: 115px; }
  .mr_md--120 {
    margin-right: 120px; }
  .mr_md--125 {
    margin-right: 125px; }
  .mr_md--130 {
    margin-right: 130px; }
  .mr_md--135 {
    margin-right: 135px; }
  .mr_md--140 {
    margin-right: 140px; }
  .mr_md--145 {
    margin-right: 145px; }
  .mr_md--150 {
    margin-right: 150px; }
  .mr_md--155 {
    margin-right: 155px; }
  .mr_md--160 {
    margin-right: 160px; }
  .mr_md--165 {
    margin-right: 165px; }
  .mr_md--170 {
    margin-right: 170px; }
  .mr_md--175 {
    margin-right: 175px; }
  .mr_md--180 {
    margin-right: 180px; }
  .mr_md--185 {
    margin-right: 185px; }
  .mr_md--190 {
    margin-right: 190px; }
  .mr_md--195 {
    margin-right: 195px; }
  .mr_md--200 {
    margin-right: 200px; }
  .mr_md--205 {
    margin-right: 205px; }
  .mr_md--210 {
    margin-right: 210px; }
  .mr_md--215 {
    margin-right: 215px; }
  .mr_md--220 {
    margin-right: 220px; }
  .mr_md--225 {
    margin-right: 225px; }
  .mr_md--230 {
    margin-right: 230px; }
  .mr_md--235 {
    margin-right: 235px; }
  .mr_md--240 {
    margin-right: 240px; }
  .mr_md--245 {
    margin-right: 245px; }
  .mr_md--250 {
    margin-right: 250px; } }

@media (min-width: 992px) {
  .mr_lg--5 {
    margin-right: 5px; }
  .mr_lg--10 {
    margin-right: 10px; }
  .mr_lg--15 {
    margin-right: 15px; }
  .mr_lg--20 {
    margin-right: 20px; }
  .mr_lg--25 {
    margin-right: 25px; }
  .mr_lg--30 {
    margin-right: 30px; }
  .mr_lg--35 {
    margin-right: 35px; }
  .mr_lg--40 {
    margin-right: 40px; }
  .mr_lg--45 {
    margin-right: 45px; }
  .mr_lg--50 {
    margin-right: 50px; }
  .mr_lg--55 {
    margin-right: 55px; }
  .mr_lg--60 {
    margin-right: 60px; }
  .mr_lg--65 {
    margin-right: 65px; }
  .mr_lg--70 {
    margin-right: 70px; }
  .mr_lg--75 {
    margin-right: 75px; }
  .mr_lg--80 {
    margin-right: 80px; }
  .mr_lg--85 {
    margin-right: 85px; }
  .mr_lg--90 {
    margin-right: 90px; }
  .mr_lg--95 {
    margin-right: 95px; }
  .mr_lg--100 {
    margin-right: 100px; }
  .mr_lg--105 {
    margin-right: 105px; }
  .mr_lg--110 {
    margin-right: 110px; }
  .mr_lg--115 {
    margin-right: 115px; }
  .mr_lg--120 {
    margin-right: 120px; }
  .mr_lg--125 {
    margin-right: 125px; }
  .mr_lg--130 {
    margin-right: 130px; }
  .mr_lg--135 {
    margin-right: 135px; }
  .mr_lg--140 {
    margin-right: 140px; }
  .mr_lg--145 {
    margin-right: 145px; }
  .mr_lg--150 {
    margin-right: 150px; }
  .mr_lg--155 {
    margin-right: 155px; }
  .mr_lg--160 {
    margin-right: 160px; }
  .mr_lg--165 {
    margin-right: 165px; }
  .mr_lg--170 {
    margin-right: 170px; }
  .mr_lg--175 {
    margin-right: 175px; }
  .mr_lg--180 {
    margin-right: 180px; }
  .mr_lg--185 {
    margin-right: 185px; }
  .mr_lg--190 {
    margin-right: 190px; }
  .mr_lg--195 {
    margin-right: 195px; }
  .mr_lg--200 {
    margin-right: 200px; }
  .mr_lg--205 {
    margin-right: 205px; }
  .mr_lg--210 {
    margin-right: 210px; }
  .mr_lg--215 {
    margin-right: 215px; }
  .mr_lg--220 {
    margin-right: 220px; }
  .mr_lg--225 {
    margin-right: 225px; }
  .mr_lg--230 {
    margin-right: 230px; }
  .mr_lg--235 {
    margin-right: 235px; }
  .mr_lg--240 {
    margin-right: 240px; }
  .mr_lg--245 {
    margin-right: 245px; }
  .mr_lg--250 {
    margin-right: 250px; } }

@media (min-width: 1200px) {
  .mr_xl--5 {
    margin-right: 5px; }
  .mr_xl--10 {
    margin-right: 10px; }
  .mr_xl--15 {
    margin-right: 15px; }
  .mr_xl--20 {
    margin-right: 20px; }
  .mr_xl--25 {
    margin-right: 25px; }
  .mr_xl--30 {
    margin-right: 30px; }
  .mr_xl--35 {
    margin-right: 35px; }
  .mr_xl--40 {
    margin-right: 40px; }
  .mr_xl--45 {
    margin-right: 45px; }
  .mr_xl--50 {
    margin-right: 50px; }
  .mr_xl--55 {
    margin-right: 55px; }
  .mr_xl--60 {
    margin-right: 60px; }
  .mr_xl--65 {
    margin-right: 65px; }
  .mr_xl--70 {
    margin-right: 70px; }
  .mr_xl--75 {
    margin-right: 75px; }
  .mr_xl--80 {
    margin-right: 80px; }
  .mr_xl--85 {
    margin-right: 85px; }
  .mr_xl--90 {
    margin-right: 90px; }
  .mr_xl--95 {
    margin-right: 95px; }
  .mr_xl--100 {
    margin-right: 100px; }
  .mr_xl--105 {
    margin-right: 105px; }
  .mr_xl--110 {
    margin-right: 110px; }
  .mr_xl--115 {
    margin-right: 115px; }
  .mr_xl--120 {
    margin-right: 120px; }
  .mr_xl--125 {
    margin-right: 125px; }
  .mr_xl--130 {
    margin-right: 130px; }
  .mr_xl--135 {
    margin-right: 135px; }
  .mr_xl--140 {
    margin-right: 140px; }
  .mr_xl--145 {
    margin-right: 145px; }
  .mr_xl--150 {
    margin-right: 150px; }
  .mr_xl--155 {
    margin-right: 155px; }
  .mr_xl--160 {
    margin-right: 160px; }
  .mr_xl--165 {
    margin-right: 165px; }
  .mr_xl--170 {
    margin-right: 170px; }
  .mr_xl--175 {
    margin-right: 175px; }
  .mr_xl--180 {
    margin-right: 180px; }
  .mr_xl--185 {
    margin-right: 185px; }
  .mr_xl--190 {
    margin-right: 190px; }
  .mr_xl--195 {
    margin-right: 195px; }
  .mr_xl--200 {
    margin-right: 200px; }
  .mr_xl--205 {
    margin-right: 205px; }
  .mr_xl--210 {
    margin-right: 210px; }
  .mr_xl--215 {
    margin-right: 215px; }
  .mr_xl--220 {
    margin-right: 220px; }
  .mr_xl--225 {
    margin-right: 225px; }
  .mr_xl--230 {
    margin-right: 230px; }
  .mr_xl--235 {
    margin-right: 235px; }
  .mr_xl--240 {
    margin-right: 240px; }
  .mr_xl--245 {
    margin-right: 245px; }
  .mr_xl--250 {
    margin-right: 250px; } }

.ml--5 {
  margin-left: 5px; }

.ml--10 {
  margin-left: 10px; }

.ml--15 {
  margin-left: 15px; }

.ml--20 {
  margin-left: 20px; }

.ml--25 {
  margin-left: 25px; }

.ml--30 {
  margin-left: 30px; }

.ml--35 {
  margin-left: 35px; }

.ml--40 {
  margin-left: 40px; }

.ml--45 {
  margin-left: 45px; }

.ml--50 {
  margin-left: 50px; }

.ml--55 {
  margin-left: 55px; }

.ml--60 {
  margin-left: 60px; }

.ml--65 {
  margin-left: 65px; }

.ml--70 {
  margin-left: 70px; }

.ml--75 {
  margin-left: 75px; }

.ml--80 {
  margin-left: 80px; }

.ml--85 {
  margin-left: 85px; }

.ml--90 {
  margin-left: 90px; }

.ml--95 {
  margin-left: 95px; }

.ml--100 {
  margin-left: 100px; }

.ml--105 {
  margin-left: 105px; }

.ml--110 {
  margin-left: 110px; }

.ml--115 {
  margin-left: 115px; }

.ml--120 {
  margin-left: 120px; }

.ml--125 {
  margin-left: 125px; }

.ml--130 {
  margin-left: 130px; }

.ml--135 {
  margin-left: 135px; }

.ml--140 {
  margin-left: 140px; }

.ml--145 {
  margin-left: 145px; }

.ml--150 {
  margin-left: 150px; }

.ml--155 {
  margin-left: 155px; }

.ml--160 {
  margin-left: 160px; }

.ml--165 {
  margin-left: 165px; }

.ml--170 {
  margin-left: 170px; }

.ml--175 {
  margin-left: 175px; }

.ml--180 {
  margin-left: 180px; }

.ml--185 {
  margin-left: 185px; }

.ml--190 {
  margin-left: 190px; }

.ml--195 {
  margin-left: 195px; }

.ml--200 {
  margin-left: 200px; }

.ml--205 {
  margin-left: 205px; }

.ml--210 {
  margin-left: 210px; }

.ml--215 {
  margin-left: 215px; }

.ml--220 {
  margin-left: 220px; }

.ml--225 {
  margin-left: 225px; }

.ml--230 {
  margin-left: 230px; }

.ml--235 {
  margin-left: 235px; }

.ml--240 {
  margin-left: 240px; }

.ml--245 {
  margin-left: 245px; }

.ml--250 {
  margin-left: 250px; }

@media (min-width: 576px) {
  .ml_sm--5 {
    margin-left: 5px; }
  .ml_sm--10 {
    margin-left: 10px; }
  .ml_sm--15 {
    margin-left: 15px; }
  .ml_sm--20 {
    margin-left: 20px; }
  .ml_sm--25 {
    margin-left: 25px; }
  .ml_sm--30 {
    margin-left: 30px; }
  .ml_sm--35 {
    margin-left: 35px; }
  .ml_sm--40 {
    margin-left: 40px; }
  .ml_sm--45 {
    margin-left: 45px; }
  .ml_sm--50 {
    margin-left: 50px; }
  .ml_sm--55 {
    margin-left: 55px; }
  .ml_sm--60 {
    margin-left: 60px; }
  .ml_sm--65 {
    margin-left: 65px; }
  .ml_sm--70 {
    margin-left: 70px; }
  .ml_sm--75 {
    margin-left: 75px; }
  .ml_sm--80 {
    margin-left: 80px; }
  .ml_sm--85 {
    margin-left: 85px; }
  .ml_sm--90 {
    margin-left: 90px; }
  .ml_sm--95 {
    margin-left: 95px; }
  .ml_sm--100 {
    margin-left: 100px; }
  .ml_sm--105 {
    margin-left: 105px; }
  .ml_sm--110 {
    margin-left: 110px; }
  .ml_sm--115 {
    margin-left: 115px; }
  .ml_sm--120 {
    margin-left: 120px; }
  .ml_sm--125 {
    margin-left: 125px; }
  .ml_sm--130 {
    margin-left: 130px; }
  .ml_sm--135 {
    margin-left: 135px; }
  .ml_sm--140 {
    margin-left: 140px; }
  .ml_sm--145 {
    margin-left: 145px; }
  .ml_sm--150 {
    margin-left: 150px; }
  .ml_sm--155 {
    margin-left: 155px; }
  .ml_sm--160 {
    margin-left: 160px; }
  .ml_sm--165 {
    margin-left: 165px; }
  .ml_sm--170 {
    margin-left: 170px; }
  .ml_sm--175 {
    margin-left: 175px; }
  .ml_sm--180 {
    margin-left: 180px; }
  .ml_sm--185 {
    margin-left: 185px; }
  .ml_sm--190 {
    margin-left: 190px; }
  .ml_sm--195 {
    margin-left: 195px; }
  .ml_sm--200 {
    margin-left: 200px; }
  .ml_sm--205 {
    margin-left: 205px; }
  .ml_sm--210 {
    margin-left: 210px; }
  .ml_sm--215 {
    margin-left: 215px; }
  .ml_sm--220 {
    margin-left: 220px; }
  .ml_sm--225 {
    margin-left: 225px; }
  .ml_sm--230 {
    margin-left: 230px; }
  .ml_sm--235 {
    margin-left: 235px; }
  .ml_sm--240 {
    margin-left: 240px; }
  .ml_sm--245 {
    margin-left: 245px; }
  .ml_sm--250 {
    margin-left: 250px; } }

@media (min-width: 768px) {
  .ml_md--5 {
    margin-left: 5px; }
  .ml_md--10 {
    margin-left: 10px; }
  .ml_md--15 {
    margin-left: 15px; }
  .ml_md--20 {
    margin-left: 20px; }
  .ml_md--25 {
    margin-left: 25px; }
  .ml_md--30 {
    margin-left: 30px; }
  .ml_md--35 {
    margin-left: 35px; }
  .ml_md--40 {
    margin-left: 40px; }
  .ml_md--45 {
    margin-left: 45px; }
  .ml_md--50 {
    margin-left: 50px; }
  .ml_md--55 {
    margin-left: 55px; }
  .ml_md--60 {
    margin-left: 60px; }
  .ml_md--65 {
    margin-left: 65px; }
  .ml_md--70 {
    margin-left: 70px; }
  .ml_md--75 {
    margin-left: 75px; }
  .ml_md--80 {
    margin-left: 80px; }
  .ml_md--85 {
    margin-left: 85px; }
  .ml_md--90 {
    margin-left: 90px; }
  .ml_md--95 {
    margin-left: 95px; }
  .ml_md--100 {
    margin-left: 100px; }
  .ml_md--105 {
    margin-left: 105px; }
  .ml_md--110 {
    margin-left: 110px; }
  .ml_md--115 {
    margin-left: 115px; }
  .ml_md--120 {
    margin-left: 120px; }
  .ml_md--125 {
    margin-left: 125px; }
  .ml_md--130 {
    margin-left: 130px; }
  .ml_md--135 {
    margin-left: 135px; }
  .ml_md--140 {
    margin-left: 140px; }
  .ml_md--145 {
    margin-left: 145px; }
  .ml_md--150 {
    margin-left: 150px; }
  .ml_md--155 {
    margin-left: 155px; }
  .ml_md--160 {
    margin-left: 160px; }
  .ml_md--165 {
    margin-left: 165px; }
  .ml_md--170 {
    margin-left: 170px; }
  .ml_md--175 {
    margin-left: 175px; }
  .ml_md--180 {
    margin-left: 180px; }
  .ml_md--185 {
    margin-left: 185px; }
  .ml_md--190 {
    margin-left: 190px; }
  .ml_md--195 {
    margin-left: 195px; }
  .ml_md--200 {
    margin-left: 200px; }
  .ml_md--205 {
    margin-left: 205px; }
  .ml_md--210 {
    margin-left: 210px; }
  .ml_md--215 {
    margin-left: 215px; }
  .ml_md--220 {
    margin-left: 220px; }
  .ml_md--225 {
    margin-left: 225px; }
  .ml_md--230 {
    margin-left: 230px; }
  .ml_md--235 {
    margin-left: 235px; }
  .ml_md--240 {
    margin-left: 240px; }
  .ml_md--245 {
    margin-left: 245px; }
  .ml_md--250 {
    margin-left: 250px; } }

@media (min-width: 992px) {
  .ml_lg--5 {
    margin-left: 5px; }
  .ml_lg--10 {
    margin-left: 10px; }
  .ml_lg--15 {
    margin-left: 15px; }
  .ml_lg--20 {
    margin-left: 20px; }
  .ml_lg--25 {
    margin-left: 25px; }
  .ml_lg--30 {
    margin-left: 30px; }
  .ml_lg--35 {
    margin-left: 35px; }
  .ml_lg--40 {
    margin-left: 40px; }
  .ml_lg--45 {
    margin-left: 45px; }
  .ml_lg--50 {
    margin-left: 50px; }
  .ml_lg--55 {
    margin-left: 55px; }
  .ml_lg--60 {
    margin-left: 60px; }
  .ml_lg--65 {
    margin-left: 65px; }
  .ml_lg--70 {
    margin-left: 70px; }
  .ml_lg--75 {
    margin-left: 75px; }
  .ml_lg--80 {
    margin-left: 80px; }
  .ml_lg--85 {
    margin-left: 85px; }
  .ml_lg--90 {
    margin-left: 90px; }
  .ml_lg--95 {
    margin-left: 95px; }
  .ml_lg--100 {
    margin-left: 100px; }
  .ml_lg--105 {
    margin-left: 105px; }
  .ml_lg--110 {
    margin-left: 110px; }
  .ml_lg--115 {
    margin-left: 115px; }
  .ml_lg--120 {
    margin-left: 120px; }
  .ml_lg--125 {
    margin-left: 125px; }
  .ml_lg--130 {
    margin-left: 130px; }
  .ml_lg--135 {
    margin-left: 135px; }
  .ml_lg--140 {
    margin-left: 140px; }
  .ml_lg--145 {
    margin-left: 145px; }
  .ml_lg--150 {
    margin-left: 150px; }
  .ml_lg--155 {
    margin-left: 155px; }
  .ml_lg--160 {
    margin-left: 160px; }
  .ml_lg--165 {
    margin-left: 165px; }
  .ml_lg--170 {
    margin-left: 170px; }
  .ml_lg--175 {
    margin-left: 175px; }
  .ml_lg--180 {
    margin-left: 180px; }
  .ml_lg--185 {
    margin-left: 185px; }
  .ml_lg--190 {
    margin-left: 190px; }
  .ml_lg--195 {
    margin-left: 195px; }
  .ml_lg--200 {
    margin-left: 200px; }
  .ml_lg--205 {
    margin-left: 205px; }
  .ml_lg--210 {
    margin-left: 210px; }
  .ml_lg--215 {
    margin-left: 215px; }
  .ml_lg--220 {
    margin-left: 220px; }
  .ml_lg--225 {
    margin-left: 225px; }
  .ml_lg--230 {
    margin-left: 230px; }
  .ml_lg--235 {
    margin-left: 235px; }
  .ml_lg--240 {
    margin-left: 240px; }
  .ml_lg--245 {
    margin-left: 245px; }
  .ml_lg--250 {
    margin-left: 250px; } }

@media (min-width: 1200px) {
  .ml_xl--5 {
    margin-left: 5px; }
  .ml_xl--10 {
    margin-left: 10px; }
  .ml_xl--15 {
    margin-left: 15px; }
  .ml_xl--20 {
    margin-left: 20px; }
  .ml_xl--25 {
    margin-left: 25px; }
  .ml_xl--30 {
    margin-left: 30px; }
  .ml_xl--35 {
    margin-left: 35px; }
  .ml_xl--40 {
    margin-left: 40px; }
  .ml_xl--45 {
    margin-left: 45px; }
  .ml_xl--50 {
    margin-left: 50px; }
  .ml_xl--55 {
    margin-left: 55px; }
  .ml_xl--60 {
    margin-left: 60px; }
  .ml_xl--65 {
    margin-left: 65px; }
  .ml_xl--70 {
    margin-left: 70px; }
  .ml_xl--75 {
    margin-left: 75px; }
  .ml_xl--80 {
    margin-left: 80px; }
  .ml_xl--85 {
    margin-left: 85px; }
  .ml_xl--90 {
    margin-left: 90px; }
  .ml_xl--95 {
    margin-left: 95px; }
  .ml_xl--100 {
    margin-left: 100px; }
  .ml_xl--105 {
    margin-left: 105px; }
  .ml_xl--110 {
    margin-left: 110px; }
  .ml_xl--115 {
    margin-left: 115px; }
  .ml_xl--120 {
    margin-left: 120px; }
  .ml_xl--125 {
    margin-left: 125px; }
  .ml_xl--130 {
    margin-left: 130px; }
  .ml_xl--135 {
    margin-left: 135px; }
  .ml_xl--140 {
    margin-left: 140px; }
  .ml_xl--145 {
    margin-left: 145px; }
  .ml_xl--150 {
    margin-left: 150px; }
  .ml_xl--155 {
    margin-left: 155px; }
  .ml_xl--160 {
    margin-left: 160px; }
  .ml_xl--165 {
    margin-left: 165px; }
  .ml_xl--170 {
    margin-left: 170px; }
  .ml_xl--175 {
    margin-left: 175px; }
  .ml_xl--180 {
    margin-left: 180px; }
  .ml_xl--185 {
    margin-left: 185px; }
  .ml_xl--190 {
    margin-left: 190px; }
  .ml_xl--195 {
    margin-left: 195px; }
  .ml_xl--200 {
    margin-left: 200px; }
  .ml_xl--205 {
    margin-left: 205px; }
  .ml_xl--210 {
    margin-left: 210px; }
  .ml_xl--215 {
    margin-left: 215px; }
  .ml_xl--220 {
    margin-left: 220px; }
  .ml_xl--225 {
    margin-left: 225px; }
  .ml_xl--230 {
    margin-left: 230px; }
  .ml_xl--235 {
    margin-left: 235px; }
  .ml_xl--240 {
    margin-left: 240px; }
  .ml_xl--245 {
    margin-left: 245px; }
  .ml_xl--250 {
    margin-left: 250px; } }

.w-100 {
  width: 100%; }

.w-75 {
  width: 75%; }

.w-50 {
  width: 50%; }

.w-33 {
  width: 33.33%; }

.w-25 {
  width: 25%; }

.w-20 {
  width: 20%; }

.w-10 {
  width: 10%; }

.max-width--600 {
  max-width: 600px;
  width: 100%; }

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .max-width--600 {
    max-width: 100%; } }

@media only screen and (max-width: 767px) {
  .max-width--600 {
    max-width: 100%; } }

.h-100 {
  height: 100%; }

.h-75 {
  height: 75%; }

.h-50 {
  height: 50%; }

.h-25 {
  height: 25%; }

.h-33 {
  height: 33.33%; }

.h-10 {
  height: 10%; }

a {
  color: #f05a23; }

.position-relative {
  position: relative; }

.text-center {
  text-align: center; }

.for-desktop {
  display: none; }
  @media (min-width: 1200px) {
    .for-desktop {
      display: block; } }

.for-mobile {
  display: none; }
  @media (max-width: 1199.98px) {
    .for-mobile {
      display: block; } }

.for-md-mobile {
  display: none; }
  @media (max-width: 1199.98px) {
    .for-md-mobile {
      display: block; } }

.for-md-desktop {
  display: none; }
  @media (min-width: 1200px) {
    .for-md-desktop {
      display: block; } }

.font-weight-bold {
  font-weight: 400; }

.page-header h2, .page-header .h2 {
  text-align: center;
  font-size: 32px;
  line-height: 48px; }
  @media (min-width: 1200px) {
    .page-header h2, .page-header .h2 {
      font-size: 40px;
      line-height: 60px; } }

.page-header.black h2, .page-header.black .h2 {
  color: #000000; }

.page-header.white h2, .page-header.white .h2 {
  color: #ffffff; }

.add-bg {
  background-color: #fafafa;
  padding-bottom: 120px !important; }
  @media (max-width: 1199.98px) {
    .add-bg {
      padding-bottom: 60px !important; } }

.section-spacing {
  padding-top: 60px;
  padding-bottom: 60px; }
  @media (min-width: 1300px) {
    .section-spacing {
      padding-top: 80px;
      padding-bottom: 80px; } }
  @media (min-width: 1500px) {
    .section-spacing {
      padding-top: 120px;
      padding-bottom: 120px; } }

.spacing-bottom-none {
  padding-bottom: 0 !important; }

.spacing-top-none {
  padding-top: 0 !important; }

.half-top-space {
  padding-top: 40px !important; }

.margin-top {
  margin-top: 120px; }
  @media (max-width: 1199.98px) {
    .margin-top {
      margin-top: 60px; } }

.list-underlist-none {
  list-style: none; }

.custom-width {
  max-width: 730px;
  margin: 0 auto; }

#loading {
  background-color: #f05a23;
  height: 100%;
  width: 100%;
  position: fixed;
  z-index: 9999;
  margin-top: 0px;
  top: 0px; }

#loading-center {
  width: 100%;
  height: 100%;
  position: relative; }

#loading-center-absolute {
  position: absolute;
  left: 50%;
  top: 50%;
  height: 60px;
  width: 60px;
  margin-top: -30px;
  margin-left: -30px;
  -webkit-animation: loading-center-absolute 1s infinite;
  animation: loading-center-absolute 1s infinite; }

.object {
  width: 20px;
  height: 20px;
  background-color: #fff;
  float: left;
  -moz-border-radius: 50% 50% 50% 50%;
  -webkit-border-radius: 50% 50% 50% 50%;
  border-radius: 50% 50% 50% 50%;
  margin-right: 20px;
  margin-bottom: 20px; }

.object:nth-child(2n + 0) {
  margin-right: 0px; }

#object_one {
  -webkit-animation: object_one 1s infinite;
  animation: object_one 1s infinite; }

#object_two {
  -webkit-animation: object_two 1s infinite;
  animation: object_two 1s infinite; }

#object_three {
  -webkit-animation: object_three 1s infinite;
  animation: object_three 1s infinite; }

#object_four {
  -webkit-animation: object_four 1s infinite;
  animation: object_four 1s infinite; }

@-webkit-keyframes loading-center-absolute {
  100% {
    -ms-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg); } }

@keyframes loading-center-absolute {
  100% {
    -ms-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg); } }

@-webkit-keyframes object_one {
  50% {
    -ms-transform: translate(20px, 20px);
    -webkit-transform: translate(20px, 20px);
    transform: translate(20px, 20px); } }

@keyframes object_one {
  50% {
    -ms-transform: translate(20px, 20px);
    -webkit-transform: translate(20px, 20px);
    transform: translate(20px, 20px); } }

@-webkit-keyframes object_two {
  50% {
    -ms-transform: translate(-20px, 20px);
    -webkit-transform: translate(-20px, 20px);
    transform: translate(-20px, 20px); } }

@keyframes object_two {
  50% {
    -ms-transform: translate(-20px, 20px);
    -webkit-transform: translate(-20px, 20px);
    transform: translate(-20px, 20px); } }

@-webkit-keyframes object_three {
  50% {
    -ms-transform: translate(20px, -20px);
    -webkit-transform: translate(20px, -20px);
    transform: translate(20px, -20px); } }

@keyframes object_three {
  50% {
    -ms-transform: translate(20px, -20px);
    -webkit-transform: translate(20px, -20px);
    transform: translate(20px, -20px); } }

@-webkit-keyframes object_four {
  50% {
    -ms-transform: translate(-20px, -20px);
    -webkit-transform: translate(-20px, -20px);
    transform: translate(-20px, -20px); } }

@keyframes object_four {
  50% {
    -ms-transform: translate(-20px, -20px);
    -webkit-transform: translate(-20px, -20px);
    transform: translate(-20px, -20px); } }

.to-top {
  width: 40px;
  height: 40px;
  display: block;
  background-color: #f05a23;
  color: #ffffff;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 99;
  text-align: center;
  line-height: 40px;
  font-size: 20px;
  cursor: pointer;
  transform: translateY(100px);
  opacity: 0;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out; }
  .to-top svg {
    transform: rotate(90deg);
    position: relative;
    top: -4px; }
  .to-top:hover {
    color: #ffffff;
    background-color: #424143; }
  .to-top.fixed-totop {
    transform: translateY(0px);
    opacity: 1; }

.white-bg {
  background-color: #ffffff !important; }

main.wp-block-group {
  padding-left: 15px;
  padding-right: 15px; }

.section-page-header h2, .section-page-header .h2 {
  font-size: 32px;
  line-height: 48px; }
  @media (min-width: 1200px) {
    .section-page-header h2, .section-page-header .h2 {
      font-size: 40px;
      line-height: 60px; } }

.section-page-header div {
  max-width: 530px;
  margin: 0 auto; }
  @media (max-width: 1199.98px) {
    .section-page-header div {
      max-width: 100%; } }

.primary-btn {
  width: 186px;
  height: 50px;
  background: #f05a23;
  border: none;
  border-radius: 5px 0px 0px 0px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 13px 50px;
  text-transform: uppercase;
  color: #000000;
  font-weight: bold;
  font-size: 16px;
  line-height: 24px;
  transition: all 1ms ease-in-out; }
  .primary-btn:hover {
    background-color: #424143;
    color: #ffffff;
    transition: all 1ms ease-in; }

.primary-btn-anim {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none; }
  .primary-btn-anim span {
    position: relative;
    transition: all 0.3s ease; }
    .primary-btn-anim span::after {
      content: '\00bb';
      position: absolute;
      opacity: 0;
      top: 0;
      right: -10px;
      transition: all 0.3s ease; }
  .primary-btn-anim:hover span::after {
    right: -20px;
    opacity: 1; }

#application-form form .ug-application-submit input {
  display: none !important; }

.modal-open-window {
  position: relative; }
  .modal-open-window::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    content: '';
    pointer-events: none;
    z-index: 100;
    opacity: 0.5; }

.white-circle {
  background: #ffffff; }

.circle {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  box-shadow: 0px 0px 1px 1px #118dff; }

.pulse {
  animation: pulse-animation 1s infinite alternate; }
  .pulse.effect1 {
    animation-delay: 1s; }
  .pulse.effect2 {
    animation-delay: 1.2s; }
  .pulse.effect3 {
    animation-delay: 1.4s; }
  .pulse.effect4 {
    animation-delay: 1.6s; }
  .pulse.effect5 {
    animation-delay: 1.8s; }
  .pulse.effect6 {
    animation-delay: 2s; }
  .pulse.effect7 {
    animation-delay: 1s; }
  .pulse.effect8 {
    animation-delay: 1.2s; }
  .pulse.effect9 {
    animation-delay: 1.4s; }
  .pulse.effect10 {
    animation-delay: 1.6s; }
  .pulse.effect11 {
    animation-delay: 1.8s; }
  .pulse.effect12 {
    animation-delay: 2s; }
  .pulse.effect13 {
    animation-delay: 1.5s; }
  .pulse.effect14 {
    animation-delay: 2s; }

@keyframes pulse-animation {
  0% {
    box-shadow: 0 0 0 0px #118cff70; }
  100% {
    box-shadow: 0 0 0 20px #118cff70; } }

.master {
  color: black;
  float: right; }

@media (max-width: 900px) {
  .master {
    display: none; } }

/*======================
    404 page
=======================*/
.page_404 {
  padding: 40px 0;
  background: #fff; }

.page_404 img {
  width: 100%; }

.four_zero_four_bg {
  background-image: url(https://cdn.dribbble.com/users/285475/screenshots/2083086/dribbble_1.gif);
  height: 400px;
  background-position: center;
  background-repeat: no-repeat; }

.four_zero_four_bg h1, .four_zero_four_bg .h1 {
  font-size: 80px; }

.four_zero_four_bg h3, .four_zero_four_bg .h3 {
  font-size: 80px; }

.link_404 {
  color: #fff !important;
  padding: 10px 20px;
  background: #39ac31;
  margin: 20px 0;
  display: inline-block; }

.contant_box_404 {
  margin-top: -50px;
  display: flex;
  flex-direction: column;
  align-items: center; }

p.paragraph-heading {
  font-weight: 400 !important;
  font-size: 20px !important;
  line-height: 32px !important;
  color: #000000 !important; }

.parallax-window {
  min-height: 500px;
  background: transparent; }

.custom-parallax {
  margin-bottom: 30px;
  width: 100%;
  min-height: 300px;
  background-size: cover;
  background-position: 60%; }
  @media (min-width: 992px) {
    .custom-parallax {
      min-height: 500px; } }

.banner-parallax {
  min-height: 700px; }

.paragraph {
  max-width: 960px; }

.banner-parallax {
  min-height: 600px;
  background-size: cover;
  margin-bottom: 0; }
  @media (max-width: 991.98px) {
    .banner-parallax {
      min-height: 300px; } }

.single-image img {
  max-height: 500px;
  width: 100%;
  object-fit: cover; }

.alignright {
  float: right; }

.partner-logo {
  text-align: center;
  background: #f7f7f7; }
  @media (max-width: 767.98px) {
    .partner-logo .row {
      justify-content: center;
      align-content: center; } }
  .partner-logo h2, .partner-logo .h2 {
    margin-bottom: 30px; }
  .partner-logo .partner-items {
    margin-bottom: 20px;
    max-width: 280px; }
    @media (max-width: 767.98px) {
      .partner-logo .partner-items {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center; } }
  .partner-logo.large-logo .partner-items {
    max-width: 350px !important; }

.lg-backdrop {
  background-color: #0009; }

.headroom--pinned {
  display: block; }

.headroom--unpinned {
  display: none; }

/**
 * Note: I have omitted any vendor-prefixes for clarity.
 * Adding them is left as an exercise for the reader.
 */
.headroom {
  will-change: transform;
  transition: transform 200ms linear; }

.headroom--pinned {
  transform: translateY(0%); }

.headroom--unpinned {
  transform: translateY(-100%); }

.lg-sub-html h3, .lg-sub-html .h3 {
  margin: 0;
  font-size: 20px;
  font-weight: bold;
  color: #fff; }

.lg-sub-html p {
  font-size: 12px;
  margin: 5px 0 0;
  color: #fff; }

.image-group-grid {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30px; }
  .image-group-grid img {
    width: 100%;
    height: auto;
    margin-bottom: 15px; }
    @media (min-width: 1200px) {
      .image-group-grid img {
        width: 50%;
        margin-bottom: 0;
        object-fit: cover; } }

.our-concerns-template-default.single.single-our-concerns.postid-1795 #image-slider .swiper .swiper-slide img,
.our-concerns-template-default.single.single-our-concerns.postid-1179 #image-slider .swiper .swiper-slide img {
  display: block;
  width: 100%;
  height: auto;
  object-fit: fill; }

.page-id-240 #message-from-ceo {
  margin-bottom: 0; }

@media (min-width: 1200px) {
  .d-flex-dekstop {
    display: flex; } }

span.filename {
  font-weight: 500;
  margin-top: 10px;
  display: block;
  color: #424143; }

.postid-1102 #image-slider img {
  object-fit: contain !important; }

@media (min-width: 576px) {
  .for-sm-desktop {
    display: block; }
  .for-sm-mobile {
    display: none; } }

@media (max-width: 575.98px) {
  .for-sm-desktop {
    display: none; }
  .for-sm-mobile {
    display: block; } }

.postid-1102 #full-single-image .for-sm-desktop img {
  height: auto;
  object-fit: contain; }

.postid-3632 #image-slider img {
  object-fit: contain !important;
  height: auto !important; }

#cheif-message .cheif-message-content p {
  text-align: justify; }

.wpcf7-submit {
  border-radius: 10px 0px 0px 0px;
  background-color: #f05a23;
  border: none;
  color: #000000;
  text-align: center;
  font-size: 16px;
  padding: 9px;
  width: 200px;
  transition: all 0.5s;
  cursor: pointer;
  margin: 10px;
  font-weight: 500; }

.wpcf7-submit span {
  cursor: pointer;
  display: inline-block;
  position: relative;
  transition: 0.5s; }

.wpcf7-submit span:after {
  content: '\00bb';
  position: absolute;
  opacity: 0;
  top: 0;
  right: -20px;
  transition: 0.5s; }

.wpcf7-submit:hover span {
  padding-right: 25px; }

.wpcf7-submit:hover span:after {
  opacity: 1;
  right: 0; }

#drop-resume .primay-button {
  width: 260px !important; }

#drop-resume a span {
  cursor: pointer;
  display: inline-block;
  position: relative;
  transition: 0.5s; }

#drop-resume a span {
  cursor: pointer;
  display: inline-block;
  position: relative;
  transition: 0.5s; }

#drop-resume a span:after {
  content: '\00bb';
  position: absolute;
  opacity: 0;
  top: 0;
  right: -40px;
  transition: 0.5s; }

#drop-resume a:hover span {
  padding-right: 5px; }

#drop-resume a:hover span:after {
  opacity: 1;
  right: -20px; }

#verify-email-btn {
  background-color: #f05a23;
  border: none;
  color: #000000;
  text-align: center;
  font-size: 16px;
  padding: 12px;
  width: 200px;
  transition: all 0.5s;
  cursor: pointer;
  font-weight: 500;
  transition: all 1ms ease-in-out; }
  #verify-email-btn:hover {
    transition: all 1ms ease-in;
    background-color: #424143; }
    #verify-email-btn:hover span {
      color: #ffffff !important; }

#verify-email-btn span {
  cursor: pointer;
  display: inline-block;
  position: relative;
  transition: 0.5s;
  color: #000000 !important;
  margin-bottom: 0 !important;
  font-weight: 500 !important; }

#verify-email-btn span:after {
  content: '\00bb';
  position: absolute;
  opacity: 0;
  top: 0;
  right: -40px;
  transition: 0.5s; }

#verify-email-btn:hover span {
  padding-right: 5px; }

#verify-email-btn:hover span:after {
  opacity: 1;
  right: -20px; }

.home .transparent-header,
.home .headroom--top {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  z-index: 99;
  background-color: #00000030; }
  .home .transparent-header .main-menu-area .nav-menu li a,
  .home .headroom--top .main-menu-area .nav-menu li a {
    color: #ffffff; }
  .home .transparent-header .nav-menu li.menu-item-has-children::after,
  .home .headroom--top .nav-menu li.menu-item-has-children::after {
    transition: all 1ms ease-in-out;
    background-image: url("data:image/svg+xml, %3Csvg width=\"12\" height=\"6\" viewBox=\"0 0 12 6\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M5.58578 5.74761C5.69507 5.85625 5.84292 5.91724 5.99703 5.91724C6.15114 5.91724 6.29898 5.85625 6.40828 5.74761L11.0749 1.08094C11.1705 0.969349 11.2204 0.825805 11.2148 0.678994C11.2091 0.532183 11.1482 0.392918 11.0444 0.28903C10.9405 0.185141 10.8012 0.124281 10.6544 0.11861C10.5076 0.112939 10.364 0.162876 10.2524 0.258442L5.99995 4.51094L1.74745 0.252608C1.6376 0.142764 1.48862 0.0810547 1.33328 0.0810547C1.17794 0.0810547 1.02896 0.142764 0.919112 0.252608C0.809268 0.362452 0.747559 0.511432 0.747559 0.666775C0.747559 0.822118 0.809268 0.971098 0.919112 1.08094L5.58578 5.74761Z\" fill=\"white\"/%3E%3C/svg%3E"); }
  .home .transparent-header .menu-bar svg path,
  .home .headroom--top .menu-bar svg path {
    fill: white; }

.transparent-header {
  position: relative;
  left: 0;
  top: 0;
  right: 0;
  z-index: 99;
  background-color: #fff;
  border-bottom: 1px solid #4e4e4e12; }
  .transparent-header .main-menu-area .nav-menu li a {
    color: #000000;
    transition: all 1ms ease-in; }
  .transparent-header .menu-bar svg path {
    fill: black; }
  .transparent-header .mobile-menu .nav-menu li.menu-item-has-children::after {
    background-image: url("data:image/svg+xml, %3Csvg width=\"12\" height=\"6\" viewBox=\"0 0 12 6\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M5.58578 5.74761C5.69507 5.85625 5.84292 5.91724 5.99703 5.91724C6.15114 5.91724 6.29898 5.85625 6.40828 5.74761L11.0749 1.08094C11.1705 0.969349 11.2204 0.825805 11.2148 0.678994C11.2091 0.532183 11.1482 0.392918 11.0444 0.28903C10.9405 0.185141 10.8012 0.124281 10.6544 0.11861C10.5076 0.112939 10.364 0.162876 10.2524 0.258442L5.99995 4.51094L1.74745 0.252608C1.6376 0.142764 1.48862 0.0810547 1.33328 0.0810547C1.17794 0.0810547 1.02896 0.142764 0.919112 0.252608C0.809268 0.362452 0.747559 0.511432 0.747559 0.666775C0.747559 0.822118 0.809268 0.971098 0.919112 1.08094L5.58578 5.74761Z\" fill=\"white\"/%3E%3C/svg%3E"); }

.main-menu-area {
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 15px;
  padding-bottom: 15px; }
  .main-menu-area .sticky-logo {
    display: none; }
  @media (min-width: 992px) {
    .main-menu-area {
      padding-left: 80px;
      padding-right: 80px; } }
  @media (min-width: 1200px) {
    .main-menu-area {
      padding-left: 60px;
      padding-right: 60px; } }
  .main-menu-area .nav-menu {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    list-style: none;
    margin: 0;
    padding: 0; }
    .main-menu-area .nav-menu li {
      position: relative; }
      .main-menu-area .nav-menu li.current-menu-item::after {
        background-image: url("data:image/svg+xml, %3Csvg width=\"12\" height=\"6\" viewBox=\"0 0 12 6\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M5.58578 5.74761C5.69507 5.85625 5.84292 5.91724 5.99703 5.91724C6.15114 5.91724 6.29898 5.85625 6.40828 5.74761L11.0749 1.08094C11.1705 0.969349 11.2204 0.825805 11.2148 0.678994C11.2091 0.532183 11.1482 0.392918 11.0444 0.28903C10.9405 0.185141 10.8012 0.124281 10.6544 0.11861C10.5076 0.112939 10.364 0.162876 10.2524 0.258442L5.99995 4.51094L1.74745 0.252608C1.6376 0.142764 1.48862 0.0810547 1.33328 0.0810547C1.17794 0.0810547 1.02896 0.142764 0.919112 0.252608C0.809268 0.362452 0.747559 0.511432 0.747559 0.666775C0.747559 0.822118 0.809268 0.971098 0.919112 1.08094L5.58578 5.74761Z\" fill=\"orange\"/%3E%3C/svg%3E") !important; }
      .main-menu-area .nav-menu li.current-menu-item a {
        color: #f05a23 !important; }
      .main-menu-area .nav-menu li:hover::after {
        background-image: url("data:image/svg+xml, %3Csvg width=\"12\" height=\"6\" viewBox=\"0 0 12 6\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M5.58578 5.74761C5.69507 5.85625 5.84292 5.91724 5.99703 5.91724C6.15114 5.91724 6.29898 5.85625 6.40828 5.74761L11.0749 1.08094C11.1705 0.969349 11.2204 0.825805 11.2148 0.678994C11.2091 0.532183 11.1482 0.392918 11.0444 0.28903C10.9405 0.185141 10.8012 0.124281 10.6544 0.11861C10.5076 0.112939 10.364 0.162876 10.2524 0.258442L5.99995 4.51094L1.74745 0.252608C1.6376 0.142764 1.48862 0.0810547 1.33328 0.0810547C1.17794 0.0810547 1.02896 0.142764 0.919112 0.252608C0.809268 0.362452 0.747559 0.511432 0.747559 0.666775C0.747559 0.822118 0.809268 0.971098 0.919112 1.08094L5.58578 5.74761Z\" fill=\"orange\"/%3E%3C/svg%3E") !important;
        transform: rotate(180deg);
        top: 6px !important; }
      .main-menu-area .nav-menu li a {
        padding: 1.4em 1.21em;
        line-height: 26px;
        transition: all 0.25s ease-in-out;
        color: #ffffff; }
        .main-menu-area .nav-menu li a:hover {
          transition: all 0.25s ease-in;
          color: #f05a23 !important; }
      .main-menu-area .nav-menu li .sub-menu {
        min-width: 230px;
        background-color: #fff;
        position: absolute;
        left: 0;
        top: 57px;
        padding: 15px 0;
        text-align: center;
        list-style-type: none;
        opacity: 0;
        transition: all 0.25s ease;
        visibility: hidden;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px;
        z-index: 2;
        transform-origin: left top;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px; }
        .main-menu-area .nav-menu li .sub-menu li a {
          padding: 10px 25px;
          display: block;
          font-size: 95%;
          position: relative;
          color: #000000 !important;
          text-align: left;
          -webkit-transition: all 0.3s ease-in-out;
          -moz-transition: all 0.3s ease-in-out;
          transition: all 0.3s ease-in-out; }
          .main-menu-area .nav-menu li .sub-menu li a:hover {
            padding-left: 35px;
            background-color: #f9f9f9;
            color: #f05a23 !important; }
          .main-menu-area .nav-menu li .sub-menu li a i {
            position: absolute;
            top: 50%;
            right: 20px;
            margin-top: -8px;
            opacity: 0.6; }
      .main-menu-area .nav-menu li:hover > .sub-menu {
        opacity: 1;
        transition: all 0.25s ease;
        visibility: visible; }
      .main-menu-area .nav-menu li.menu-item-has-children {
        position: relative; }
        .main-menu-area .nav-menu li.menu-item-has-children::after {
          content: '';
          position: absolute;
          width: 15px;
          height: 15px;
          background-image: url("data:image/svg+xml, %3Csvg width=\"12\" height=\"6\" viewBox=\"0 0 12 6\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M5.58578 5.74761C5.69507 5.85625 5.84292 5.91724 5.99703 5.91724C6.15114 5.91724 6.29898 5.85625 6.40828 5.74761L11.0749 1.08094C11.1705 0.969349 11.2204 0.825805 11.2148 0.678994C11.2091 0.532183 11.1482 0.392918 11.0444 0.28903C10.9405 0.185141 10.8012 0.124281 10.6544 0.11861C10.5076 0.112939 10.364 0.162876 10.2524 0.258442L5.99995 4.51094L1.74745 0.252608C1.6376 0.142764 1.48862 0.0810547 1.33328 0.0810547C1.17794 0.0810547 1.02896 0.142764 0.919112 0.252608C0.809268 0.362452 0.747559 0.511432 0.747559 0.666775C0.747559 0.822118 0.809268 0.971098 0.919112 1.08094L5.58578 5.74761Z\" fill=\"black\"/%3E%3C/svg%3E");
          background-repeat: no-repeat;
          top: 12px;
          right: -8px; }

.open-mobile-menu {
  overflow: hidden; }
  .open-mobile-menu .fixed-totop {
    z-index: -1;
    opacity: 0; }
  .open-mobile-menu .menu-bar {
    display: none !important; }
  .open-mobile-menu .mobile-menu {
    transform: translate3d(0%, 0, 0) scaleX(1);
    animation: scale-easeOutElastic;
    z-index: 9999999;
    height: 100%;
    overflow-y: auto; }

.mobile-menu {
  max-width: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #2b2b2bf5;
  height: 100%;
  z-index: -1;
  transform: translate3d(-100%, 0, 0) scaleX(0.5);
  transform-origin: left;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  padding: 20px; }
  .mobile-menu .close-menu {
    text-align: right; }
  .mobile-menu .nav-menu {
    padding-top: 50px;
    list-style: none;
    padding-left: 0;
    padding-right: 0; }
    .mobile-menu .nav-menu li {
      position: relative;
      transition: all 0.25s ease; }
      .mobile-menu .nav-menu li.menu-item-has-children::after {
        content: '';
        position: absolute;
        width: 15px;
        height: 15px;
        background-image: url("data:image/svg+xml, %3Csvg width=\"12\" height=\"6\" viewBox=\"0 0 12 6\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M5.58578 5.74761C5.69507 5.85625 5.84292 5.91724 5.99703 5.91724C6.15114 5.91724 6.29898 5.85625 6.40828 5.74761L11.0749 1.08094C11.1705 0.969349 11.2204 0.825805 11.2148 0.678994C11.2091 0.532183 11.1482 0.392918 11.0444 0.28903C10.9405 0.185141 10.8012 0.124281 10.6544 0.11861C10.5076 0.112939 10.364 0.162876 10.2524 0.258442L5.99995 4.51094L1.74745 0.252608C1.6376 0.142764 1.48862 0.0810547 1.33328 0.0810547C1.17794 0.0810547 1.02896 0.142764 0.919112 0.252608C0.809268 0.362452 0.747559 0.511432 0.747559 0.666775C0.747559 0.822118 0.809268 0.971098 0.919112 1.08094L5.58578 5.74761Z\" fill=\"black\"/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        top: 25px;
        right: 0; }
      .mobile-menu .nav-menu li a {
        font-weight: 400;
        font-size: 20px;
        line-height: 32px;
        color: #ffffff;
        display: block;
        padding: 10px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2); }
      .mobile-menu .nav-menu li .sub-menu {
        display: none;
        box-shadow: none;
        list-style: none; }
      .mobile-menu .nav-menu li.toogle-open::after {
        transform: rotate(180deg);
        transition: all 0.25s ease;
        top: 15px; }

.main-menu-area.fixed-menu {
  animation-name: slideInDown;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999999;
  background-color: #fff; }
  .main-menu-area.fixed-menu .default-logo {
    display: none; }
  .main-menu-area.fixed-menu .sticky-logo {
    display: block; }
  .main-menu-area.fixed-menu .nav-menu li.menu-item-has-children::after {
    transition: all 1ms ease-in-out;
    background-image: url("data:image/svg+xml, %3Csvg width=\"12\" height=\"6\" viewBox=\"0 0 12 6\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M5.58578 5.74761C5.69507 5.85625 5.84292 5.91724 5.99703 5.91724C6.15114 5.91724 6.29898 5.85625 6.40828 5.74761L11.0749 1.08094C11.1705 0.969349 11.2204 0.825805 11.2148 0.678994C11.2091 0.532183 11.1482 0.392918 11.0444 0.28903C10.9405 0.185141 10.8012 0.124281 10.6544 0.11861C10.5076 0.112939 10.364 0.162876 10.2524 0.258442L5.99995 4.51094L1.74745 0.252608C1.6376 0.142764 1.48862 0.0810547 1.33328 0.0810547C1.17794 0.0810547 1.02896 0.142764 0.919112 0.252608C0.809268 0.362452 0.747559 0.511432 0.747559 0.666775C0.747559 0.822118 0.809268 0.971098 0.919112 1.08094L5.58578 5.74761Z\" fill=\"black\"/%3E%3C/svg%3E"); }
  .main-menu-area.fixed-menu .nav-menu li a {
    color: #000000 !important; }
  .main-menu-area.fixed-menu .menu-bar svg path {
    fill: black; }

.cta {
  padding-top: 30px;
  height: 65px;
  position: relative;
  max-width: 145px;
  cursor: pointer; }
  .cta::after {
    z-index: 0;
    position: absolute;
    left: -20px;
    top: 25px;
    content: '';
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: 1px solid #f05a23;
    background-color: #f05a23;
    animation-name: mymove2;
    animation-duration: 0.25s;
    animation-fill-mode: forwards; }
  .cta:hover a {
    padding-left: 0px !important;
    text-indent: 0px; }
    .cta:hover a::before {
      display: none; }
    .cta:hover a::after {
      opacity: 1; }
  .cta:hover::after {
    animation-name: mymove;
    animation-duration: 0.25s;
    animation-fill-mode: forwards; }
  .cta a {
    font-weight: bold;
    font-size: 16px;
    line-height: 20px;
    text-transform: uppercase;
    color: #000000;
    position: relative;
    z-index: 9;
    font-size: 14px;
    animation-name: paddinganimation;
    animation-duration: 0.25s;
    animation-fill-mode: forwards;
    max-width: 145px;
    display: inline-block;
    text-indent: 5px; }
    .cta a svg line {
      transition: all 1s ease-in; }
    .cta a::after {
      content: '';
      background-image: url("data:image/svg+xml, %3Csvg width=\"17\" height=\"11\" viewBox=\"0 0 17 11\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M13.324 6.40909L1.43816 6.40909C0.950653 6.40909 0.555447 6.00208 0.555447 5.5C0.555447 4.99792 0.950653 4.59091 1.43816 4.59091L13.324 4.59091L10.4485 1.62951C10.0866 1.25674 10.0866 0.652353 10.4485 0.27958C10.8105 -0.0931935 11.3973 -0.0931935 11.7593 0.27958L16.1729 4.82503C16.5348 5.19781 16.5348 5.80219 16.1729 6.17497L11.7593 10.7204C11.3973 11.0932 10.8105 11.0932 10.4485 10.7204C10.0866 10.3476 10.0866 9.74326 10.4485 9.37049L13.324 6.40909Z\" fill=\"black\"/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      width: 35px;
      height: 35px;
      position: absolute;
      top: -15px;
      left: 74px;
      transform: translate(20px, 20px);
      opacity: 0; }
    .cta a::before {
      content: '';
      background-image: url("data:image/svg+xml, %3Csvg width=\"14\" height=\"2\" viewBox=\"0 0 14 2\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cline x1=\"8.74228e-08\" y1=\"1\" x2=\"14\" y2=\"1\" stroke=\"black\" stroke-width=\"2\"/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      width: 20px;
      height: 2px;
      position: absolute;
      left: 0;
      top: 0px;
      transform: translate(11px, 10px); }
  .cta.website-link {
    padding-top: 30px;
    height: 65px;
    position: relative;
    max-width: 145px;
    cursor: pointer; }
    .cta.website-link::after {
      z-index: 0;
      position: absolute;
      left: -20px;
      top: 25px;
      content: '';
      width: 35px;
      height: 35px;
      border-radius: 50%;
      border: 1px solid #f05a23;
      background-color: #f05a23;
      animation-name: mymove2;
      animation-duration: 0.25s;
      animation-fill-mode: forwards; }
    .cta.website-link:hover a {
      padding-left: 0px !important;
      text-indent: 0px; }
      .cta.website-link:hover a::before {
        display: none; }
      .cta.website-link:hover a::after {
        opacity: 1;
        left: 90px; }
    .cta.website-link:hover::after {
      animation-name: mymove;
      animation-duration: 0.25s;
      animation-fill-mode: forwards;
      transform: translate(15px); }

#hero {
  height: 100vh;
  overflow: hidden;
  position: relative; }
  #hero.half-sm-height {
    height: 350px; }
    @media (min-width: 768px) {
      #hero.half-sm-height {
        height: 100vh; } }
    #hero.half-sm-height .swiper-wrapper {
      height: 350px; }
      @media (min-width: 768px) {
        #hero.half-sm-height .swiper-wrapper {
          height: 100%; } }
      #hero.half-sm-height .swiper-wrapper .slider-item .slider-content {
        position: absolute; }
        @media (min-width: 768px) {
          #hero.half-sm-height .swiper-wrapper .slider-item .slider-content {
            position: relative; } }
  @media (min-width: 992px) {
    #hero {
      height: 50vh; } }
  @media (min-width: 1200px) {
    #hero {
      height: 100vh; } }
  #hero .scroll-logo {
    position: absolute;
    left: 20px;
    z-index: 9;
    top: 30%;
    bottom: 30%; }
    @media (max-width: 1199.98px) {
      #hero .scroll-logo {
        display: none; } }
  #hero .mySwiper {
    overflow: hidden; }
  #hero .swiper-slide .slider-item {
    height: 100vh;
    overflow: hidden;
    z-index: 2; }
    @media (min-width: 992px) {
      #hero .swiper-slide .slider-item {
        height: 50vh; } }
    @media (min-width: 1200px) {
      #hero .swiper-slide .slider-item {
        height: 100vh; } }
    #hero .swiper-slide .slider-item .slider-image {
      background-size: cover;
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1;
      -webkit-transition: all 20s ease-in-out;
      -moz-transition: all 20s ease-in-out;
      transition: all 20s ease-in-out; }
      #hero .swiper-slide .slider-item .slider-image::after {
        content: "";
        position: absolute;
        z-index: 9;
        width: 100%;
        height: 100%;
        left: 0;
        right: 0; }
    #hero .swiper-slide .slider-item .slider-content {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative; }
      @media (min-width: 1200px) {
        #hero .swiper-slide .slider-item .slider-content {
          left: 150px;
          justify-content: flex-start; } }
      #hero .swiper-slide .slider-item .slider-content h1, #hero .swiper-slide .slider-item .slider-content .h1 {
        -webkit-transition: all 0.6s ease-in-out;
        -moz-transition: all 0.6s ease-in-out;
        transition: all 0.6s ease-in-out;
        opacity: 0;
        -webkit-transform: translateY(40px);
        -moz-transform: translateY(40px);
        -ms-transform: translateY(40px);
        -o-transform: translateY(40px);
        transform: translateY(40px);
        font-size: 38px;
        font-weight: 300;
        line-height: 48px;
        text-align: center;
        width: 330px;
        color: #ffffff;
        position: relative; }
        @media (min-width: 1200px) {
          #hero .swiper-slide .slider-item .slider-content h1, #hero .swiper-slide .slider-item .slider-content .h1 {
            font-size: 40px;
            line-height: 60px;
            text-align: left;
            width: calc(100% + 40px); } }
        #hero .swiper-slide .slider-item .slider-content h1::after, #hero .swiper-slide .slider-item .slider-content .h1::after {
          content: "";
          background-image: url("data:image/svg+xml, %3Csvg viewBox=\"0 0 25 25\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E %3Cpath d=\"M0.672549 12.8829C0.818954 13.039 0.97451 13.039 1.13922 12.8829L6.87647 6.73423C7.04118 6.57808 7.04118 6.42192 6.87647 6.26577L1.13922 0.117117C0.97451 -0.039039 0.818954 -0.039039 0.672549 0.117117L0.123529 0.702703C-0.0411763 0.858859 -0.0411763 1.02477 0.123529 1.20045L5.09216 6.5L0.123529 11.7996C-0.0411763 11.9752 -0.0411763 12.1411 0.123529 12.2973L0.672549 12.8829Z\" fill=\"%23ffffff\"/%3E %3C/svg%3E");
          position: absolute;
          width: 50px;
          height: 50px;
          opacity: 0;
          bottom: 20px; }
        #hero .swiper-slide .slider-item .slider-content h1:hover::after, #hero .swiper-slide .slider-item .slider-content .h1:hover::after {
          animation-name: arrowmymove;
          animation-duration: 0.25s;
          animation-fill-mode: forwards; }
          @media (min-width: 1200px) {
            #hero .swiper-slide .slider-item .slider-content h1:hover::after, #hero .swiper-slide .slider-item .slider-content .h1:hover::after {
              opacity: 1; } }
  #hero .swiper-slide .scroll-text img {
    position: absolute;
    z-index: 9; }
    #hero .swiper-slide .scroll-text img:nth-child(1) {
      left: 23px;
      top: 28%; }
    #hero .swiper-slide .scroll-text img:nth-child(2) {
      left: 20px;
      top: 45%; }
  #hero .swiper-slide-active .slider-image {
    transform: scale(1.5) !important; }
  #hero .swiper-slide-active .slider-content h1, #hero .swiper-slide-active .slider-content .h1 {
    opacity: 1 !important;
    -webkit-transition-delay: 0.6s;
    -moz-transition-delay: 0.6s;
    -ms-transition-delay: 0.6s;
    -o-transition-delay: 0.6s;
    transition-delay: 0.6s;
    -webkit-transform: translateX(0px);
    -moz-transform: translateX(0px);
    -ms-transform: translateX(0px);
    -o-transform: translateX(0px);
    transform: translateX(0px) !important;
    text-shadow: 0px 4px 4px #000000; }
  #hero .banner-slide-next-btn {
    width: 50px;
    height: 50px;
    line-height: 48px;
    right: 50px;
    background-color: rgba(255, 99, 96, 0.6);
    position: absolute;
    top: 90%;
    margin-top: -25px;
    z-index: 2;
    outline: none;
    color: #fff;
    cursor: pointer;
    text-align: center;
    border-radius: 0 0 10px;
    transition: all 1ms ease-in-out; }
    #hero .banner-slide-next-btn svg path {
      fill: #ffffff; }
    #hero .banner-slide-next-btn.active {
      background-color: #f05a23;
      transition: all 1ms ease-in; }
  #hero .banner-slide-prev-btn {
    width: 50px;
    height: 50px;
    line-height: 48px;
    right: 100px;
    background-color: rgba(255, 99, 96, 0.6);
    position: absolute;
    top: 90%;
    margin-top: -25px;
    z-index: 2;
    outline: none;
    color: #fff;
    cursor: pointer;
    text-align: center;
    border-radius: 10px 0 0;
    transition: all 1ms ease-in-out; }
    #hero .banner-slide-prev-btn svg path {
      fill: #ffffff; }
    #hero .banner-slide-prev-btn.active {
      background-color: #f05a23;
      transition: all 1ms ease-in; }
  #hero .slider-mobile {
    display: none; }
    @media (max-width: 575.98px) {
      #hero .slider-mobile {
        display: block; } }
  #hero .slider-desktop {
    display: none; }
    @media (min-width: 576px) {
      #hero .slider-desktop {
        display: block; } }

@keyframes arrowmymove {
  0% {
    transform: translate(5px, 28px); }
  100% {
    transform: translate(28px, 28px); } }

section.about-us {
  height: 100%;
  /*
.tilter__figure,
.tilter__deco,
.tilter__caption {
	will-change: transform;
}*/
  /* Example 5 (line animating) */ }
  section.about-us h2, section.about-us .h2 {
    font-size: 32px; }
    @media (min-width: 1200px) {
      section.about-us h2, section.about-us .h2 {
        font-size: 40px;
        line-height: 60px; } }
  section.about-us p {
    font-size: 16px;
    line-height: 26px;
    color: #4e4e4e; }
  section.about-us .thumb-col {
    display: flex;
    align-items: center;
    border-top: 1px solid #dddfe3;
    border-bottom: 1px solid #dddfe3;
    padding-top: 30px;
    padding-bottom: 30px;
    margin-bottom: 30px; }
    section.about-us .thumb-col .image {
      margin-right: 20px; }
  section.about-us .main-about-content h4, section.about-us .main-about-content .h4 {
    font-weight: 400;
    font-size: 20px;
    line-height: 32px; }
  section.about-us .main-about-content .list {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px; }
    section.about-us .main-about-content .list .icon {
      margin-right: 15px; }
    section.about-us .main-about-content .list .content {
      padding: 0; }
      section.about-us .main-about-content .list .content span {
        font-size: 16px;
        line-height: 26px;
        color: #4e4e4e; }
  section.about-us .tilter {
    display: block;
    position: relative;
    color: #fd7e14;
    flex: none;
    perspective: 1000px; }
  section.about-us .tilter * {
    pointer-events: none; }
  section.about-us .tilter:hover,
  section.about-us .tilter:focus {
    color: #fd7e14;
    outline: none; }
    section.about-us .tilter:hover .tilter__deco.tilter__deco--shine,
    section.about-us .tilter:focus .tilter__deco.tilter__deco--shine {
      background-color: #00000063; }
  section.about-us .tilter__figure,
  section.about-us .tilter__image {
    margin: 0;
    width: 100%;
    height: 400px;
    object-fit: cover;
    display: block; }
    @media (min-width: 1300px) {
      section.about-us .tilter__figure,
      section.about-us .tilter__image {
        width: 100%;
        height: 600px;
        object-fit: cover; } }
    @media (min-width: 1500px) {
      section.about-us .tilter__figure,
      section.about-us .tilter__image {
        width: 100%;
        height: 810px; } }
  section.about-us .tilter__figure > * {
    transform: translateZ(0px);
    /* Force correct stacking order */ }
  section.about-us .smooth .tilter__figure,
  section.about-us .smooth .tilter__deco--overlay,
  section.about-us .smooth .tilter__deco--lines,
  section.about-us .smooth .tilter__deco--shine div,
  section.about-us .smooth .tilter__caption {
    transition: transform 0.2s ease-out; }
  section.about-us .tilter__figure {
    position: relative; }
  section.about-us .tilter__figure::before {
    content: '';
    position: absolute;
    width: 90%;
    height: 90%;
    top: 5%;
    left: 5%;
    box-shadow: 0 30px 20px rgba(35, 32, 39, 0.5); }
  section.about-us .tilter__deco {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden; }
  section.about-us .tilter__deco--shine div {
    position: absolute;
    width: 200%;
    height: 200%;
    top: -50%;
    left: -50%;
    background-image: linear-gradient(45deg, rgba(0, 0, 0, 0.5) 0%, rgba(255, 255, 255, 0.25) 50%, transparent 100%); }
  section.about-us .tilter__deco--lines {
    fill: none;
    stroke: #ffffff;
    opacity: 0.6;
    stroke-width: 1.5px; }
  section.about-us .tilter__caption {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center; }
  section.about-us .tilter__title {
    margin: 0;
    font-family: Josefin Sans;
    font-weight: normal;
    line-height: 56px;
    opacity: 0;
    transition: all 0.25s ease;
    font-size: 36px;
    color: #ffffff;
    width: 300px;
    text-align: center;
    line-height: 55px;
    text-transform: uppercase; }
    @media (max-width: 767.98px) {
      section.about-us .tilter__title {
        font-size: 22px; } }
    @media (max-width: 767.98px) {
      section.about-us .tilter__title {
        width: 230px; } }
  section.about-us .tilter__description {
    margin: 1em 0 0 0;
    font-size: 0.85em;
    letter-spacing: 0.15em; }
  section.about-us .tilter--5 .tilter__deco--lines path {
    stroke-dasharray: 1270;
    stroke-dashoffset: 1270;
    transition: stroke-dashoffset 0.7s; }
  section.about-us .tilter--5:hover .tilter__caption h3, section.about-us .tilter--5:hover .tilter__caption .h3 {
    opacity: 1;
    transition: all 0.25s ease; }
  section.about-us .tilter--5:hover .tilter__deco--lines path {
    stroke-dashoffset: 0; }
  section.about-us .tilter--5 .tilter__figure::before {
    box-shadow: none; }
  section.about-us .follow-social {
    display: flex;
    flex-direction: column;
    align-items: center; }
    section.about-us .follow-social span {
      font-weight: 400;
      font-size: 20px;
      line-height: 32px;
      margin-bottom: 20px;
      display: block; }
    section.about-us .follow-social .icon-group {
      display: flex;
      flex-wrap: wrap;
      width: 130px;
      align-items: center;
      justify-content: center; }
      section.about-us .follow-social .icon-group a {
        margin-right: 20px;
        width: 40px;
        height: 40px;
        border: 1px solid #666;
        box-sizing: border-box;
        border-radius: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.25 ease-in-out; }
        section.about-us .follow-social .icon-group a:last-child {
          margin-right: 0; }
        section.about-us .follow-social .icon-group a svg path {
          fill: #666; }
        section.about-us .follow-social .icon-group a:hover {
          border: 1px solid #f05a23;
          transition: all 0.25 ease-in; }
          section.about-us .follow-social .icon-group a:hover svg path {
            fill: #f05a23; }

.concerns {
  background-color: #2b2b2b; }
  .concerns h2, .concerns .h2 {
    text-align: center;
    font-weight: 400;
    font-size: 32px;
    line-height: 48px;
    color: #ffffff;
    margin-bottom: 30px; }
    @media (min-width: 1200px) {
      .concerns h2, .concerns .h2 {
        font-size: 40px;
        line-height: 60px;
        margin-bottom: 50px; } }
  .concerns .grid {
    border-radius: 5px;
    background-color: #424143;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 10px;
    column-gap: 10px; }
    .concerns .grid .column {
      flex: 100%; }
      @media (min-width: 992px) {
        .concerns .grid .column {
          flex: 1; } }
      .concerns .grid .column .thumb {
        position: relative;
        margin-bottom: 10px;
        border: 1px solid transparent;
        border-radius: 10px;
        overflow: hidden; }
        @media (min-width: 992px) {
          .concerns .grid .column .thumb {
            overflow: auto; } }
        .concerns .grid .column .thumb h4, .concerns .grid .column .thumb .h4 {
          position: absolute;
          padding: 10px;
          font-weight: 400;
          font-size: 26px;
          line-height: 38px;
          color: #ffffff;
          bottom: 15px;
          width: 100%;
          text-align: center;
          z-index: 9999;
          opacity: 0;
          text-shadow: 0px 4px 4px #000000;
          transition: all 0.25s ease-in-out; }
          @media (max-width: 1199.98px) {
            .concerns .grid .column .thumb h4, .concerns .grid .column .thumb .h4 {
              opacity: 1; } }
        .concerns .grid .column .thumb a.learn-more {
          text-shadow: 0px 4px 4px #000000;
          position: absolute;
          left: 10px;
          padding: 10px;
          font-weight: 500;
          font-size: 14px;
          line-height: 22px;
          color: #ffffff;
          bottom: 0;
          width: 100%;
          text-align: center;
          z-index: 9999;
          opacity: 0;
          transition: all 0.25s ease-in-out; }
          @media (min-width: 992px) {
            .concerns .grid .column .thumb a.learn-more {
              left: 0; } }
          @media (max-width: 1199.98px) {
            .concerns .grid .column .thumb a.learn-more {
              opacity: 1; } }
          .concerns .grid .column .thumb a.learn-more svg {
            opacity: 0; }
        .concerns .grid .column .thumb:last-child {
          margin-bottom: 10px; }
          @media (min-width: 992px) {
            .concerns .grid .column .thumb:last-child {
              margin-bottom: 0; } }
        .concerns .grid .column .thumb img {
          position: relative;
          margin-bottom: 10px;
          width: 100%;
          filter: grayscale(80%);
          object-fit: cover;
          transition: all 0.25s ease; }
        .concerns .grid .column .thumb::after {
          content: "";
          position: absolute;
          width: 100%;
          height: 100%;
          z-index: 9;
          top: 0;
          left: 0; }
        .concerns .grid .column .thumb:hover h4, .concerns .grid .column .thumb:hover .h4 {
          opacity: 1;
          transition: all 0.25s cubic-bezier(0.075, 0.82, 0.165, 1); }
        .concerns .grid .column .thumb:hover a.learn-more {
          opacity: 1;
          transition: all 0.25s cubic-bezier(0.075, 0.82, 0.165, 1);
          text-decoration: underline; }
          .concerns .grid .column .thumb:hover a.learn-more svg {
            opacity: 1;
            animation-name: gridarrowmymove;
            animation-duration: 0.25s;
            animation-fill-mode: forwards;
            animation-timing-function: linear; }
        .concerns .grid .column .thumb:hover img {
          filter: grayscale(0);
          transition: all 0.25s ease; }
      @media (max-width: 575.98px) {
        .concerns .grid .column:last-child .thumb:last-child {
          margin-bottom: 0; } }

@keyframes gridarrowmymove {
  0% {
    transform: translateX(-4px); }
  100% {
    transform: translateX(4px); } }

#generic-concerns.add-bg .single-concerns-items .content {
  background-color: #fff; }

#generic-concerns .page-header {
  padding-bottom: 55px; }
  @media (max-width: 1199.98px) {
    #generic-concerns .page-header {
      padding-bottom: 30px; } }

#generic-concerns .single-concerns-items {
  border: 1px solid #dddfe3;
  box-sizing: border-box;
  border-radius: 10px;
  margin-bottom: 30px;
  overflow: hidden; }
  #generic-concerns .single-concerns-items.style2 .thumb img {
    width: 100%;
    min-height: 450px;
    object-fit: cover; }
    @media (max-width: 1199.98px) {
      #generic-concerns .single-concerns-items.style2 .thumb img {
        min-height: auto; } }
  #generic-concerns .single-concerns-items.style2 .content {
    padding-left: 15px !important;
    padding-right: 15px !important; }
    @media (max-width: 1199.98px) {
      #generic-concerns .single-concerns-items.style2 .content {
        padding-top: 20px !important;
        padding-bottom: 20px !important; } }
  #generic-concerns .single-concerns-items .thumb img {
    width: 100%;
    height: 250px;
    object-fit: cover; }
  #generic-concerns .single-concerns-items .content {
    padding: 20px;
    position: relative;
    min-height: 330px; }
    #generic-concerns .single-concerns-items .content h3, #generic-concerns .single-concerns-items .content .h3 {
      font-weight: 400;
      font-size: 20px;
      line-height: 32px; }
    #generic-concerns .single-concerns-items .content ul {
      list-style: none;
      padding: 0 0 30px;
      margin-bottom: 30px; }
      #generic-concerns .single-concerns-items .content ul li {
        font-size: 16px;
        line-height: 26px;
        color: #4e4e4e;
        padding: 0;
        display: flex; }
        #generic-concerns .single-concerns-items .content ul li::before {
          content: "\2022";
          color: #f05a23;
          display: inline-block;
          font-size: 22px;
          margin-right: 10px; }
    #generic-concerns .single-concerns-items .content p {
      padding: 0 0 20px;
      margin-bottom: 0; }
    #generic-concerns .single-concerns-items .content .read-more {
      position: absolute;
      left: 0;
      bottom: 0;
      border-top: 1px solid #dddfe3;
      width: 100%;
      padding: 15px 30px;
      font-size: 14px;
      line-height: 24px;
      color: #4e4e4e;
      font-weight: 500; }
      #generic-concerns .single-concerns-items .content .read-more svg {
        position: absolute;
        right: 20px;
        top: 30%; }
      #generic-concerns .single-concerns-items .content .read-more::after {
        content: "";
        position: absolute;
        background-color: #dddfe3;
        width: 1px;
        height: 100%;
        top: 0;
        right: 55px; }
      #generic-concerns .single-concerns-items .content .read-more::before {
        content: "";
        border-radius: 0 0 5px;
        position: absolute;
        background-color: transparent;
        transition: all 0.25s ease-in-out;
        width: 56px;
        height: 100%;
        top: 0;
        right: 0; }
      #generic-concerns .single-concerns-items .content .read-more:hover::before {
        background-color: #f05a23;
        transition: all 0.25s ease-in; }
  #generic-concerns .single-concerns-items.style2 {
    border: none; }
    #generic-concerns .single-concerns-items.style2 .content {
      padding: 60px 0 60px; }
      #generic-concerns .single-concerns-items.style2 .content p {
        margin-bottom: 0; }

#generic-concerns.single-items .single-concerns-items .thumb img {
  width: 100%;
  min-height: 450px;
  object-fit: cover; }
  @media (max-width: 1199.98px) {
    #generic-concerns.single-items .single-concerns-items .thumb img {
      min-height: auto; } }

#generic-concerns.height-auto .single-concerns-items .content {
  min-height: auto; }

#generic-concerns.blue-icon .single-concerns-items .content ul li::before {
  color: #005282; }

.single-our-concerns .size-full img {
  width: 100%;
  height: auto; }

.united-editor {
  width: 100%;
  width: 950px;
  margin: 0 auto; }
  @media (max-width: 1199.98px) {
    .united-editor {
      padding: 60px 0 60px;
      width: 100%; } }
  .united-editor.full-width {
    width: 100% !important; }
  .united-editor h2, .united-editor .h2 {
    font-size: 32px;
    line-height: 48px;
    text-transform: uppercase; }
    @media (min-width: 1200px) {
      .united-editor h2, .united-editor .h2 {
        font-size: 40px;
        line-height: 60px; } }
  .united-editor ul {
    list-style: none;
    padding: 0 0 30px;
    margin-bottom: 0px; }
    .united-editor ul li {
      font-size: 16px;
      line-height: 26px;
      color: #4e4e4e;
      padding: 0;
      display: flex;
      line-height: 34px; }
      .united-editor ul li::before {
        content: "\2022";
        color: #f05a23;
        display: inline-block;
        font-size: 22px;
        margin-right: 10px; }
  .united-editor p:last-child {
    margin-bottom: 0; }
  .united-editor img {
    height: auto; }
    @media (max-width: 1199.98px) {
      .united-editor img {
        margin-bottom: 15px; } }
  .united-editor.blue-icon ul li::before {
    color: #005282; }

.single-concerns-items.style2 .content {
  padding: 15px; }

#video-banner {
  width: 100%;
  height: 500px;
  position: relative;
  z-index: 9;
  background-size: 1920px 650px;
  background-repeat: no-repeat; }
  #video-banner .video-container {
    align-items: flex-end;
    display: flex;
    height: 500px;
    padding-bottom: 50px; }
    #video-banner .video-container .video {
      display: flex;
      align-items: center; }
      #video-banner .video-container .video .icon {
        margin-right: 20px;
        cursor: pointer; }
        #video-banner .video-container .video .icon svg path {
          transition: all 0.25 ease-in-out; }
        #video-banner .video-container .video .icon:hover svg path {
          fill: #f05a23;
          transition: all 0.25 ease-in; }
      #video-banner .video-container .video .content h4, #video-banner .video-container .video .content .h4 {
        font-weight: 400;
        font-size: 26px;
        line-height: 38px;
        color: #ffffff; }
  #video-banner::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    background-color: #000;
    opacity: 0.5;
    top: 0;
    z-index: -1; }

#banner-video {
  height: 100%;
  overflow: hidden; }
  @media (min-width: 1200px) {
    #banner-video {
      height: 100vh; } }
  #banner-video video {
    object-fit: fill;
    width: 100%;
    height: auto; }

#csr-activities {
  background-color: #2b2b2b;
  position: relative;
  background-size: cover; }
  #csr-activities .shape {
    position: absolute;
    bottom: 0;
    left: -25px; }
    #csr-activities .shape img {
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
      user-drag: none; }
  #csr-activities h2, #csr-activities .h2 {
    font-size: 40px;
    line-height: 60px;
    color: #ffffff; }
    @media (max-width: 1199.98px) {
      #csr-activities h2, #csr-activities .h2 {
        font-size: 38px;
        line-height: 48px; } }
  #csr-activities p {
    font-size: 16px;
    line-height: 26px;
    color: #ffffff;
    opacity: 0.8; }
    @media (max-width: 575.98px) {
      #csr-activities p {
        margin-bottom: 50px; } }
  #csr-activities .item {
    display: flex;
    margin-bottom: 30px; }
    #csr-activities .item .icon {
      margin-right: 15px;
      width: 90px;
      height: 90px; }
      @media (max-width: 1199.98px) {
        #csr-activities .item .icon {
          width: 50px; } }
      #csr-activities .item .icon img {
        max-width: inherit;
        max-width: 50px;
        max-height: 50px; }
    #csr-activities .item .content {
      width: calc(100% - 100px); }
      #csr-activities .item .content h3, #csr-activities .item .content .h3 {
        font-size: 40px;
        line-height: 60px;
        color: #ffffff;
        margin-bottom: 10px; }
        @media (max-width: 1199.98px) {
          #csr-activities .item .content h3, #csr-activities .item .content .h3 {
            font-size: 38px;
            line-height: 48px; } }
      #csr-activities .item .content h5, #csr-activities .item .content .h5 {
        font-weight: 400;
        font-size: 20px;
        line-height: 32px;
        color: #ffffff; }
      #csr-activities .item .content .prefix-count {
        font-size: 40px;
        line-height: 60px;
        color: #ffffff;
        margin-bottom: 10px;
        opacity: 1; }
      #csr-activities .item .content span {
        font-size: 16px;
        line-height: 26px;
        color: #ffffff;
        opacity: 0.6; }
  #csr-activities.overlay {
    position: relative;
    z-index: 9; }
    #csr-activities.overlay::after {
      content: '';
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: #0000008c;
      z-index: -1; }
      @media (max-width: 991.98px) {
        #csr-activities.overlay::after {
          background-color: #0000004f; } }

#operation {
  position: relative; }
  #operation .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px; }
  #operation h2, #operation .h2 {
    font-size: 32px;
    line-height: 48px; }
    @media (min-width: 1200px) {
      #operation h2, #operation .h2 {
        font-size: 40px;
        line-height: 60px; } }
  #operation .operation-slider {
    overflow: hidden;
    position: relative; }
    #operation .operation-slider .slider {
      display: flex; }
      @media (max-width: 991.98px) {
        #operation .operation-slider .slider {
          flex-wrap: wrap;
          flex-direction: column-reverse; } }
      #operation .operation-slider .slider .left {
        width: 100%;
        display: flex;
        align-items: center;
        position: relative;
        background: #fafafa;
        border-radius: 5px;
        padding: 30px; }
        @media (min-width: 1200px) {
          #operation .operation-slider .slider .left {
            width: 50%; } }
        @media (min-width: 1300px) {
          #operation .operation-slider .slider .left {
            padding: 50px; } }
        #operation .operation-slider .slider .left .content-box {
          box-sizing: content-box; }
          #operation .operation-slider .slider .left .content-box h3, #operation .operation-slider .slider .left .content-box .h3 {
            font-weight: 400;
            font-size: 26px;
            line-height: 38px;
            opacity: 0;
            -webkit-transform: translateY(40px);
            -moz-transform: translateY(40px);
            -ms-transform: translateY(40px);
            -o-transform: translateY(40px);
            transform: translateY(40px); }
          #operation .operation-slider .slider .left .content-box p {
            font-size: 16px;
            line-height: 26px;
            color: #000000;
            opacity: 0;
            -webkit-transform: translateY(50px);
            -moz-transform: translateY(50px);
            -ms-transform: translateY(50px);
            -o-transform: translateY(50px);
            transform: translateY(50px); }
      #operation .operation-slider .slider .right {
        width: 100%;
        float: right;
        display: flex;
        justify-content: center; }
        @media (min-width: 1200px) {
          #operation .operation-slider .slider .right {
            width: 50%; } }
        #operation .operation-slider .slider .right img {
          width: 100%;
          height: auto;
          object-fit: cover; }
          @media (min-width: 576px) {
            #operation .operation-slider .slider .right img {
              height: 480px; } }
    #operation .operation-slider .swiper-slide-active h3, #operation .operation-slider .swiper-slide-active .h3 {
      opacity: 1 !important;
      -webkit-transition-delay: 0.6s;
      -moz-transition-delay: 0.6s;
      -ms-transition-delay: 0.6s;
      -o-transition-delay: 0.6s;
      transition-delay: 0.6s;
      -webkit-transform: translateX(0px);
      -moz-transform: translateX(0px);
      -ms-transform: translateX(0px);
      -o-transform: translateX(0px);
      transform: translateX(0px) !important; }
    #operation .operation-slider .swiper-slide-active p {
      opacity: 1 !important;
      -webkit-transition-delay: 0.6s;
      -moz-transition-delay: 0.6s;
      -ms-transition-delay: 0.6s;
      -o-transition-delay: 0.6s;
      transition-delay: 0.6s;
      -webkit-transform: translateX(0px);
      -moz-transform: translateX(0px);
      -ms-transform: translateX(0px);
      -o-transform: translateX(0px);
      transform: translateX(0px) !important; }
  #operation .navigation {
    display: flex;
    justify-content: flex-end;
    z-index: 9; }
    #operation .navigation a.prev {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f7f7f7;
      border: 1px solid #f05a23;
      border-radius: 5px 0 0;
      transition: all 0.25s ease-in; }
      #operation .navigation a.prev svg path {
        fill: #f05a23; }
      #operation .navigation a.prev.active {
        background-color: #f05a23;
        transition: all 0.25s ease-in-out; }
        #operation .navigation a.prev.active svg path {
          fill: #000; }
    #operation .navigation a.next {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f7f7f7;
      border: 1px solid #f05a23;
      border-radius: 0 0 5px;
      transition: all 0.25s ease-in-out; }
      #operation .navigation a.next svg path {
        fill: #f05a23; }
      #operation .navigation a.next.active {
        background-color: #f05a23;
        transition: all 0.25s ease-in-out; }
        #operation .navigation a.next.active svg path {
          fill: #000; }

#blog-slider {
  overflow: hidden; }
  #blog-slider h2, #blog-slider .h2 {
    font-size: 32px;
    line-height: 48px; }
    @media (min-width: 1200px) {
      #blog-slider h2, #blog-slider .h2 {
        font-size: 40px;
        line-height: 60px; } }
  #blog-slider .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center; }
    #blog-slider .section-header .navigation {
      display: flex;
      justify-content: flex-end; }
      #blog-slider .section-header .navigation a.prev {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f7f7f7;
        border: 1px solid #f05a23;
        border-radius: 5px 0 0;
        transition: all 0.25s ease-in; }
        #blog-slider .section-header .navigation a.prev svg path {
          fill: #f05a23; }
        #blog-slider .section-header .navigation a.prev.active {
          background-color: #f05a23;
          transition: all 0.25s ease-in-out; }
          #blog-slider .section-header .navigation a.prev.active svg path {
            fill: #000; }
      #blog-slider .section-header .navigation a.next {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f7f7f7;
        border: 1px solid #f05a23;
        border-radius: 0 0 5px;
        transition: all 0.25s ease-in-out; }
        #blog-slider .section-header .navigation a.next svg path {
          fill: #f05a23; }
        #blog-slider .section-header .navigation a.next.active {
          background-color: #f05a23;
          transition: all 0.25s ease-in-out; }
          #blog-slider .section-header .navigation a.next.active svg path {
            fill: #000; }
  #blog-slider .blog-container {
    display: flex;
    width: 100%;
    column-gap: 25px;
    padding-top: 65px;
    overflow: hidden; }
    @media (max-width: 1199.98px) {
      #blog-slider .blog-container {
        padding-top: 0; } }
    #blog-slider .blog-container .blog-item {
      box-sizing: content-box;
      display: flex;
      flex-direction: column;
      margin-bottom: 30px;
      border-radius: 12px 5px 5px 12px;
      overflow: hidden; }
      @media (max-width: 1199.98px) {
        #blog-slider .blog-container .blog-item {
          margin-top: 30px;
          flex-wrap: wrap; } }
      #blog-slider .blog-container .blog-item .thumb {
        width: 100%; }
        #blog-slider .blog-container .blog-item .thumb img {
          width: 100%;
          height: 265px;
          object-fit: cover; }
      #blog-slider .blog-container .blog-item .content {
        width: 100%;
        padding: 20px 0;
        position: relative; }
        #blog-slider .blog-container .blog-item .content .meta-date {
          color: #4e4e4e;
          font-size: 14px;
          line-height: 24px; }
        #blog-slider .blog-container .blog-item .content h4, #blog-slider .blog-container .blog-item .content .h4 {
          font-weight: 500;
          font-size: 20px;
          line-height: 32px;
          transition: all 0.25ms ease-in-out; }
          #blog-slider .blog-container .blog-item .content h4:hover, #blog-slider .blog-container .blog-item .content .h4:hover {
            transition: all 0.25ms ease-in;
            color: #f05a23; }
        #blog-slider .blog-container .blog-item .content p {
          font-size: 16px;
          line-height: 26px;
          color: #4e4e4e; }
          @media (max-width: 1199.98px) {
            #blog-slider .blog-container .blog-item .content p {
              padding-bottom: 30px; } }
        #blog-slider .blog-container .blog-item .content .read-more {
          position: absolute;
          left: 0;
          bottom: 0;
          border-top: 1px solid #dddfe3;
          width: 100%;
          padding: 15px 30px;
          font-size: 14px;
          line-height: 24px;
          color: #4e4e4e; }
          #blog-slider .blog-container .blog-item .content .read-more svg {
            position: absolute;
            right: 20px;
            top: 30%; }
          #blog-slider .blog-container .blog-item .content .read-more::after {
            content: '';
            position: absolute;
            background-color: #dddfe3;
            width: 1px;
            height: 99%;
            top: 0;
            right: 55px; }
          #blog-slider .blog-container .blog-item .content .read-more::before {
            content: '';
            border-radius: 0 0 5px;
            position: absolute;
            background-color: transparent;
            transition: all 0.25s ease-in-out;
            width: 56px;
            height: 99%;
            top: 0;
            right: 0; }
          #blog-slider .blog-container .blog-item .content .read-more:hover::before {
            background-color: #f05a23;
            transition: all 0.25s ease-in; }

#blog.section-spacing {
  padding-top: 50px; }

#blog .single-blog-column {
  margin-bottom: 60px; }
  #blog .single-blog-column .image img {
    border-radius: 20px 0 0 0;
    max-height: 400px;
    object-fit: cover; }
  #blog .single-blog-column .content {
    padding-left: 20px; }
    @media (max-width: 1199.98px) {
      #blog .single-blog-column .content {
        padding-top: 20px;
        padding-left: 0; } }
    #blog .single-blog-column .content .post-date {
      font-size: 14px;
      line-height: 24px;
      color: #4e4e4e; }
    #blog .single-blog-column .content h2, #blog .single-blog-column .content .h2 {
      font-size: 32px;
      line-height: 48px; }
      #blog .single-blog-column .content h2 a, #blog .single-blog-column .content .h2 a {
        font-weight: 400; }
    #blog .single-blog-column .content p {
      font-size: 16px;
      line-height: 26px;
      color: #000000; }

#blog .three-blog-column {
  margin-bottom: 30px; }
  @media (min-width: 992px) {
    #blog .three-blog-column {
      display: flex;
      flex-wrap: wrap; } }
  #blog .three-blog-column .image {
    margin-bottom: 30px; }
    #blog .three-blog-column .image img {
      border-radius: 20px 0 0 0;
      width: 410px;
      height: 270px;
      object-fit: cover; }
  #blog .three-blog-column .content .post-date {
    font-size: 14px;
    line-height: 24px;
    color: #4e4e4e; }
  #blog .three-blog-column .content h2, #blog .three-blog-column .content .h2 {
    font-size: 20px;
    line-height: 32px; }
    #blog .three-blog-column .content h2 a, #blog .three-blog-column .content .h2 a {
      font-weight: 400; }
  #blog .three-blog-column .content p {
    font-size: 16px;
    line-height: 26px;
    color: #4e4e4e;
    margin-bottom: 0; }

#blog .pagination {
  padding: 50px 0 0px; }
  @media (max-width: 1199.98px) {
    #blog .pagination {
      padding: 0; } }
  #blog .pagination .page-link {
    border: none;
    padding: 10px 20px;
    color: #c0c0c0; }
    #blog .pagination .page-link.active {
      color: #f05a23; }
  #blog .pagination .prev a {
    background-color: #f05a23;
    border-radius: 10px 0px 0px 0px; }
  #blog .pagination .next a {
    background-color: #f05a23;
    border-radius: 10px 0px 0px 0px; }

.foundation__day .swiper-slide {
  position: relative; }
  .foundation__day .swiper-slide img {
    object-fit: cover;
    width: 100%;
    height: 320px; }
    @media (min-width: 1300px) {
      .foundation__day .swiper-slide img {
        height: 500px; } }
    @media (min-width: 1500px) {
      .foundation__day .swiper-slide img {
        height: 615px; } }
  .foundation__day .swiper-slide .slide-content {
    opacity: 0;
    position: relative;
    bottom: 0px;
    width: 100%;
    background: rgba(254, 247, 244, 0.247);
    border-radius: 0;
    padding: 10px 15px 0;
    left: 0; }
    @media (min-width: 992px) {
      .foundation__day .swiper-slide .slide-content {
        position: absolute; } }
    @media (max-width: 991.98px) {
      .foundation__day .swiper-slide .slide-content {
        padding: 20px;
        width: 100%;
        left: 0;
        bottom: 0;
        border-radius: 10px 0 0 0; } }
    .foundation__day .swiper-slide .slide-content h3, .foundation__day .swiper-slide .slide-content .h3 {
      font-weight: 400;
      font-size: 28px;
      line-height: 46px; }
      @media (max-width: 991.98px) {
        .foundation__day .swiper-slide .slide-content h3, .foundation__day .swiper-slide .slide-content .h3 {
          max-width: 400px; } }
    .foundation__day .swiper-slide .slide-content .cta {
      padding-top: 15px; }
      .foundation__day .swiper-slide .slide-content .cta::after {
        top: 10px; }
  .foundation__day .swiper-slide.swiper-slide-active .slide-content {
    opacity: 1; }

.foundation__day .banner-slide-next-btn {
  width: 50px;
  height: 50px;
  line-height: 48px;
  right: 50px;
  background-color: rgba(255, 99, 96, 0.6);
  position: absolute;
  top: 90%;
  margin-top: -25px;
  z-index: 2;
  outline: none;
  color: #fff;
  cursor: pointer;
  text-align: center;
  border-radius: 0 0 10px;
  transition: all 1ms ease-in-out; }
  .foundation__day .banner-slide-next-btn svg path {
    fill: #ffffff; }
  .foundation__day .banner-slide-next-btn.active {
    background-color: #f05a23;
    transition: all 1ms ease-in; }

.foundation__day .banner-slide-prev-btn {
  width: 50px;
  height: 50px;
  line-height: 48px;
  right: 100px;
  background-color: rgba(255, 99, 96, 0.6);
  position: absolute;
  top: 90%;
  margin-top: -25px;
  z-index: 2;
  outline: none;
  color: #fff;
  cursor: pointer;
  text-align: center;
  border-radius: 10px 0 0;
  transition: all 1ms ease-in-out; }
  .foundation__day .banner-slide-prev-btn svg path {
    fill: #ffffff; }
  .foundation__day .banner-slide-prev-btn.active {
    background-color: #f05a23;
    transition: all 1ms ease-in; }

#generic-banner {
  padding: 30px 0 30px; }
  @media (min-width: 1200px) {
    #generic-banner {
      padding: 50px 0 50px; } }
  @media (min-width: 1200px) {
    #generic-banner .left {
      width: 80%; } }
  #generic-banner .left span {
    font-weight: bold;
    font-size: 14px;
    line-height: 24px;
    text-transform: uppercase;
    color: #f05a23;
    position: relative;
    display: inline-block; }
    #generic-banner .left span::after {
      content: '';
      position: absolute;
      height: 1px;
      background-color: #f05a23;
      width: 40px;
      right: -50px;
      top: 10px; }
  #generic-banner .left h1, #generic-banner .left .h1 {
    font-size: 38px;
    line-height: 48px;
    color: #000000; }
    @media (min-width: 1200px) {
      #generic-banner .left h1, #generic-banner .left .h1 {
        font-size: 40px;
        line-height: 60px; } }
  @media (min-width: 1200px) {
    #generic-banner .right {
      margin-top: 30px; } }
  #generic-banner .right p {
    font-weight: 400;
    font-size: 14px;
    line-height: 30px;
    max-width: 350px;
    color: #4e4e4e; }
  #generic-banner .right .banner-link a {
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    color: #f05a23;
    transition: all 0.25s ease-in-out; }
    #generic-banner .right .banner-link a:hover {
      transition: all 0.25s ease-in;
      color: #000000; }
      #generic-banner .right .banner-link a:hover svg path {
        fill: #000000; }
  #generic-banner .right .chief-note h4, #generic-banner .right .chief-note .h4 {
    max-width: 350px;
    padding-bottom: 5px;
    font-size: 26px !important;
    font-weight: 400 !important; }
  #generic-banner .right .chief-note p {
    font-size: 26px !important;
    font-weight: 400 !important;
    padding-bottom: 0;
    margin-bottom: 15px; }

#single-banner .overlay-text {
  position: relative; }
  #single-banner .overlay-text img {
    width: 100%;
    object-fit: cover;
    max-height: 700px; }
    @media (max-width: 767.98px) {
      #single-banner .overlay-text img {
        height: 200px; } }
  #single-banner .overlay-text h3, #single-banner .overlay-text .h3 {
    font-weight: 300;
    font-size: 50px;
    line-height: 75px;
    color: #fff;
    z-index: 9;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center; }
    @media (max-width: 1199.98px) {
      #single-banner .overlay-text h3, #single-banner .overlay-text .h3 {
        font-size: 38px;
        line-height: 48px;
        text-align: center;
        padding: 20px; } }

#single-banner.career-section .overlay-content {
  position: absolute;
  bottom: 100px;
  z-index: 10;
  left: 0;
  padding-left: 100px; }
  @media (max-width: 767.98px) {
    #single-banner.career-section .overlay-content {
      padding-left: 15px;
      bottom: 20px; } }
  #single-banner.career-section .overlay-content h4, #single-banner.career-section .overlay-content .h4 {
    color: #fff;
    font-weight: 400;
    font-size: 30px; }
    @media (max-width: 767.98px) {
      #single-banner.career-section .overlay-content h4, #single-banner.career-section .overlay-content .h4 {
        margin-bottom: 5px; } }
  #single-banner.career-section .overlay-content h5, #single-banner.career-section .overlay-content .h5 {
    color: #fff;
    font-weight: 400;
    font-size: 26px; }
    @media (max-width: 767.98px) {
      #single-banner.career-section .overlay-content h5, #single-banner.career-section .overlay-content .h5 {
        font-size: 22px; } }

#single-banner.career-section .overlay-text::after {
  content: '';
  position: absolute;
  z-index: 9;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #00000066; }

#generic-service {
  position: relative; }
  #generic-service .highlight-text {
    margin-bottom: 88px; }
    #generic-service .highlight-text h3, #generic-service .highlight-text .h3 {
      color: #000000;
      font-size: 32px;
      line-height: 48px;
      margin-bottom: 20px; }
      @media (min-width: 1200px) {
        #generic-service .highlight-text h3, #generic-service .highlight-text .h3 {
          font-size: 40px;
          line-height: 60px;
          margin-bottom: 40px; } }
    #generic-service .highlight-text p {
      font-weight: 400;
      font-size: 16px;
      line-height: 1.75;
      color: #000000;
      margin-bottom: 30px; }
    #generic-service .highlight-text ul {
      list-style: none;
      padding: 0 0 30px;
      margin-bottom: 30px; }
      #generic-service .highlight-text ul li {
        font-size: 16px;
        line-height: 26px;
        color: #4e4e4e;
        padding: 0;
        display: flex; }
        #generic-service .highlight-text ul li::before {
          content: '\2022';
          color: #f05a23;
          display: inline-block;
          font-size: 22px;
          margin-right: 10px; }
  #generic-service .main-service-item {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between; }
  #generic-service .single-service-item {
    padding: 30px;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    z-index: 9;
    min-height: 575px;
    overflow: hidden;
    border-radius: 20px 0 0;
    width: 100%;
    margin-bottom: 30px;
    min-height: auto; }
    @media (min-width: 992px) {
      #generic-service .single-service-item {
        min-height: 410px;
        width: 30.33%; } }
    @media (min-width: 1500px) {
      #generic-service .single-service-item {
        min-height: 350px; } }
    #generic-service .single-service-item::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: #dddfe3d9;
      z-index: -1; }
    #generic-service .single-service-item h3, #generic-service .single-service-item .h3 {
      color: #000000;
      font-size: 36px;
      line-height: 46px;
      margin-bottom: 50px;
      position: relative; }
      #generic-service .single-service-item h3::after, #generic-service .single-service-item .h3::after {
        content: '';
        position: absolute;
        background: rgba(255, 255, 255, 0.4);
        height: 1px;
        width: 100%;
        left: 0;
        bottom: -25px; }
    #generic-service .single-service-item p {
      color: #424143;
      font-size: 20px;
      line-height: 32px;
      margin-bottom: 10px; }
  @media (max-width: 575.98px) {
    #generic-service .swiper {
      padding-left: 15px; } }
  #generic-service .swiper-slide {
    width: 90%; }
    @media (min-width: 1300px) {
      #generic-service .swiper-slide {
        width: 540px; } }
    @media (min-width: 1500px) {
      #generic-service .swiper-slide {
        width: 640px; } }
  #generic-service .swiper-pagination-progressbar {
    position: static !important;
    height: 4px !important;
    display: block;
    margin-top: 80px; }
    @media (max-width: 575.98px) {
      #generic-service .swiper-pagination-progressbar {
        width: 95%; } }
  #generic-service .swiper-pagination-progressbar-fill {
    background-color: #f05a23; }
  #generic-service .icon-group {
    margin-top: 77px; }
    #generic-service .icon-group .icon-prev {
      width: 35px;
      cursor: pointer; }
      #generic-service .icon-group .icon-prev:hover svg path {
        fill: #f05a23; }
    #generic-service .icon-group .icon-next {
      width: 35px;
      cursor: pointer; }
      #generic-service .icon-group .icon-next svg path {
        fill: #000000; }
      #generic-service .icon-group .icon-next:hover svg path {
        fill: #f05a23; }

#service-icon .page-header h2, #service-icon .page-header .h2 {
  text-align: left; }

#service-icon .page-header p {
  color: #424143;
  font-weight: 300;
  font-size: 16px;
  line-height: 32px; }

#service-icon .icon-area-row {
  padding-top: 60px; }
  #service-icon .icon-area-row .single-icon-item .thumb {
    height: 100px; }
  #service-icon .icon-area-row .single-icon-item h3, #service-icon .icon-area-row .single-icon-item .h3 {
    font-weight: 400;
    font-size: 20px;
    line-height: 32px;
    margin: 15px 0px; }
  #service-icon .icon-area-row .single-icon-item p {
    font-size: 16px;
    line-height: 26px;
    color: #4e4e4e; }

#service-icon.normal-font-weight p {
  font-weight: normal;
  font-size: 16px;
  line-height: 1.75; }

.fun-fact {
  padding-bottom: 0; }
  .fun-fact .single-funfact {
    display: flex;
    margin-bottom: 40px;
    height: 55px;
    align-items: center;
    max-width: 100%; }
    @media (min-width: 1200px) {
      .fun-fact .single-funfact {
        border-right: 1px solid #dddfe3; } }
    .fun-fact .single-funfact:last-child {
      border: none; }
    .fun-fact .single-funfact .thumb {
      width: 50px; }
      .fun-fact .single-funfact .thumb img {
        width: 45px;
        height: auto; }
    .fun-fact .single-funfact .text {
      width: calc(100% - 50px);
      padding-left: 15px; }
      .fun-fact .single-funfact .text h4, .fun-fact .single-funfact .text .h4 {
        font-weight: 600;
        font-size: 20px;
        line-height: 26px;
        margin-bottom: 2px; }
      .fun-fact .single-funfact .text span {
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #4e4e4e; }
  .fun-fact .content p {
    font-size: 16px;
    line-height: 26px;
    color: #4e4e4e; }

.team .modal {
  visibility: hidden;
  height: 0;
  opacity: 0; }
  .team .modal.open {
    visibility: visible;
    height: 100%;
    opacity: 1;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: 9999;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both; }
    @media (max-width: 767.98px) {
      .team .modal.open {
        overflow: hidden;
        height: 100vh; } }
    .team .modal.open .modal-dialog {
      position: relative;
      width: auto;
      max-width: 1140px;
      margin: auto; }
      .team .modal.open .modal-dialog .modal-content {
        position: relative;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        width: 100%;
        pointer-events: auto;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: 0.3rem;
        outline: 0;
        position: relative; }
        @media (max-width: 767.98px) {
          .team .modal.open .modal-dialog .modal-content {
            height: 100vh; } }
        .team .modal.open .modal-dialog .modal-content .custom-row {
          display: flex;
          flex-wrap: wrap; }
          @media (max-width: 767.98px) {
            .team .modal.open .modal-dialog .modal-content .custom-row {
              justify-content: space-between; } }
          .team .modal.open .modal-dialog .modal-content .custom-row .left {
            flex: 100%; }
            @media (min-width: 768px) {
              .team .modal.open .modal-dialog .modal-content .custom-row .left {
                order: 2;
                flex: 1 1 40%; } }
            .team .modal.open .modal-dialog .modal-content .custom-row .left .member-img {
              position: relative; }
              .team .modal.open .modal-dialog .modal-content .custom-row .left .member-img .name-section {
                position: absolute;
                bottom: 0;
                width: 100%;
                height: 100%;
                padding: 10px;
                background: linear-gradient(360deg, #4b4b4b 0%, rgba(75, 75, 75, 0) 33.54%);
                display: flex;
                flex-direction: column;
                justify-content: flex-end; }
                .team .modal.open .modal-dialog .modal-content .custom-row .left .member-img .name-section h3, .team .modal.open .modal-dialog .modal-content .custom-row .left .member-img .name-section .h3 {
                  font-weight: 400;
                  font-size: 20px;
                  line-height: 32px;
                  color: #ffffff;
                  margin-top: 0;
                  margin-bottom: 5px; }
                .team .modal.open .modal-dialog .modal-content .custom-row .left .member-img .name-section span {
                  font-weight: 400;
                  font-size: 14px;
                  line-height: 24px;
                  color: #fff;
                  margin-bottom: 0px; }
          .team .modal.open .modal-dialog .modal-content .custom-row .right {
            flex: 100%; }
            @media (min-width: 768px) {
              .team .modal.open .modal-dialog .modal-content .custom-row .right {
                order: 1;
                flex: 1 1 60%; } }
        .team .modal.open .modal-dialog .modal-content .details {
          padding: 40px 20px 40px 40px; }
          @media (max-width: 767.98px) {
            .team .modal.open .modal-dialog .modal-content .details {
              padding: 0px 15px 0px 15px; } }
          .team .modal.open .modal-dialog .modal-content .details h3, .team .modal.open .modal-dialog .modal-content .details .h3 {
            margin-top: 0;
            font-weight: 400;
            font-size: 36px;
            line-height: 46px;
            margin-bottom: 15px; }
            @media (max-width: 767.98px) {
              .team .modal.open .modal-dialog .modal-content .details h3, .team .modal.open .modal-dialog .modal-content .details .h3 {
                font-size: 32px;
                line-height: 46px; } }
          .team .modal.open .modal-dialog .modal-content .details span {
            font-weight: 400;
            font-size: 20px;
            line-height: 32px;
            color: #4e4e4e;
            margin-bottom: 20px;
            display: block;
            border-bottom: 1px solid #dddfe3;
            padding-bottom: 20px; }
          .team .modal.open .modal-dialog .modal-content .details p {
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #4e4e4e;
            padding-right: 20px; }
          .team .modal.open .modal-dialog .modal-content .details .overview {
            overflow: auto;
            position: relative;
            /* Track */ }
            @media (min-width: 768px) {
              .team .modal.open .modal-dialog .modal-content .details .overview {
                max-height: 280px; } }
            @media (max-width: 767.98px) {
              .team .modal.open .modal-dialog .modal-content .details .overview p {
                padding-top: 10px; }
                .team .modal.open .modal-dialog .modal-content .details .overview p:last-child {
                  padding-bottom: 30px; } }
            .team .modal.open .modal-dialog .modal-content .details .overview::-webkit-scrollbar {
              width: 5px; }
            .team .modal.open .modal-dialog .modal-content .details .overview::-webkit-scrollbar-track {
              background: #f1f1f1; }
            .team .modal.open .modal-dialog .modal-content .details .overview::-webkit-scrollbar-thumb {
              background: #888; }
            .team .modal.open .modal-dialog .modal-content .details .overview::-webkit-scrollbar-thumb:hover {
              background: #555; }
        .team .modal.open .modal-dialog .modal-content .member-img .close-modal {
          position: absolute;
          background: #f05a23;
          width: 60px;
          z-index: 9999;
          height: 60px;
          border-radius: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          right: 0;
          top: 0;
          transition: all 0.25s ease-in-out;
          margin: 20px;
          cursor: pointer; }
          @media (max-width: 767.98px) {
            .team .modal.open .modal-dialog .modal-content .member-img .close-modal {
              height: 50px;
              width: 50px;
              top: 0;
              right: 0; } }
          .team .modal.open .modal-dialog .modal-content .member-img .close-modal:hover {
            background: #ffffff;
            transition: all 0.25s ease-in; }
            .team .modal.open .modal-dialog .modal-content .member-img .close-modal:hover svg path {
              fill: #000000; }
        .team .modal.open .modal-dialog .modal-content .member-img img {
          width: 100%;
          border-radius: 0; }
          .team .modal.open .modal-dialog .modal-content .member-img img.team-mobile {
            display: none; }
            @media (max-width: 767.98px) {
              .team .modal.open .modal-dialog .modal-content .member-img img.team-mobile {
                display: block; } }
          .team .modal.open .modal-dialog .modal-content .member-img img.team-desktop {
            display: none; }
            @media (min-width: 768px) {
              .team .modal.open .modal-dialog .modal-content .member-img img.team-desktop {
                display: block; } }
          @media (max-width: 767.98px) {
            .team .modal.open .modal-dialog .modal-content .member-img img {
              object-position: top center;
              object-fit: cover; } }

.team h2, .team .h2 {
  font-size: 32px;
  line-height: 48px;
  margin-bottom: 60px; }
  @media (min-width: 1200px) {
    .team h2, .team .h2 {
      font-size: 40px;
      line-height: 60px; } }

.team .member {
  padding-bottom: 40px; }
  .team .member h3, .team .member .h3 {
    font-weight: 400;
    font-size: 26px;
    line-height: 38px;
    margin: 20px 0px 10px; }
  .team .member img {
    height: auto;
    border-radius: 20px 0 0; }
    @media (min-width: 1200px) {
      .team .member img {
        max-height: 500px; } }
  .team .member span {
    font-size: 16px;
    line-height: 26px;
    color: #4e4e4e;
    margin-bottom: 10px; }

@keyframes mymove {
  0% {
    left: 0px; }
  100% {
    left: 85px; } }

@keyframes mymove2 {
  0% {
    left: 85px; }
  100% {
    left: 0px; } }

@keyframes paddinganimation {
  0% {
    padding-left: 0;
    opacity: 0; }
  100% {
    padding-left: 35px;
    opacity: 1; } }

@keyframes removepaddinganimation {
  0% {
    opacity: 0;
    padding-left: 35px; }
  100% {
    padding-left: 0;
    opacity: 1; } }

section#slider-archive {
  background-repeat: no-repeat;
  background-size: cover;
  height: 100%;
  overflow: hidden;
  position: relative;
  z-index: 9;
  margin: 50px 0 0px;
  min-height: 430px;
  padding-top: 50px; }
  @media (min-width: 1200px) {
    section#slider-archive {
      margin: 120px 0 0px;
      padding-top: 0;
      min-height: 450px; } }
  section#slider-archive .for-desktop {
    position: relative; }
    section#slider-archive .for-desktop .timeline-desktop-nav {
      width: 80%;
      display: flex;
      justify-content: flex-end;
      position: absolute;
      bottom: 30px; }
      section#slider-archive .for-desktop .timeline-desktop-nav .navigation {
        display: flex;
        opacity: 0; }
        section#slider-archive .for-desktop .timeline-desktop-nav .navigation a.prev {
          width: 50px;
          height: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: rgba(255, 99, 96, 0.6);
          border-radius: 5px 0 0;
          transition: all 0.25s ease-in; }
          section#slider-archive .for-desktop .timeline-desktop-nav .navigation a.prev svg path {
            fill: #f05a23; }
          section#slider-archive .for-desktop .timeline-desktop-nav .navigation a.prev.active {
            background-color: #f05a23;
            transition: all 0.25s ease-in-out; }
            section#slider-archive .for-desktop .timeline-desktop-nav .navigation a.prev.active svg path {
              fill: #000; }
        section#slider-archive .for-desktop .timeline-desktop-nav .navigation a.next {
          width: 50px;
          height: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: rgba(255, 99, 96, 0.6);
          border-radius: 0 0 5px;
          transition: all 0.25s ease-in-out; }
          section#slider-archive .for-desktop .timeline-desktop-nav .navigation a.next svg path {
            fill: #f05a23; }
          section#slider-archive .for-desktop .timeline-desktop-nav .navigation a.next.active {
            background-color: #f05a23;
            transition: all 0.25s ease-in-out; }
            section#slider-archive .for-desktop .timeline-desktop-nav .navigation a.next.active svg path {
              fill: #000; }
  section#slider-archive .for-mobile .timeline-mobile-nav {
    margin-top: 30px;
    justify-content: center;
    display: flex;
    position: relative; }
  section#slider-archive .for-mobile .navigation {
    display: flex;
    justify-content: flex-end;
    position: absolute;
    z-index: 9999;
    top: 0;
    bottom: 0;
    opacity: 0; }
    section#slider-archive .for-mobile .navigation a.prev {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 99, 96, 0.6);
      border-radius: 5px 0 0;
      transition: all 0.25s ease-in; }
      section#slider-archive .for-mobile .navigation a.prev svg path {
        fill: #f05a23; }
      section#slider-archive .for-mobile .navigation a.prev.active {
        background-color: #f05a23;
        transition: all 0.25s ease-in-out; }
        section#slider-archive .for-mobile .navigation a.prev.active svg path {
          fill: #000; }
    section#slider-archive .for-mobile .navigation a.next {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 99, 96, 0.6);
      border-radius: 0 0 5px;
      transition: all 0.25s ease-in-out; }
      section#slider-archive .for-mobile .navigation a.next svg path {
        fill: #f05a23; }
      section#slider-archive .for-mobile .navigation a.next.active {
        background-color: #f05a23;
        transition: all 0.25s ease-in-out; }
        section#slider-archive .for-mobile .navigation a.next.active svg path {
          fill: #000; }
  section#slider-archive .circular-image {
    position: relative;
    transform: translate(55px); }
    section#slider-archive .circular-image img {
      position: absolute;
      top: 0px;
      left: 0px; }
  section#slider-archive .carousel.for-desktop {
    padding-top: 70px;
    margin-top: 20px;
    padding-bottom: 50px; }
  section#slider-archive .carousel_slide {
    display: flex;
    justify-content: center;
    padding-left: 50px;
    cursor: pointer; }
  section#slider-archive .swiper-wrapper {
    position: relative;
    height: auto;
    background-size: cover; }
    @media (min-width: 1200px) {
      section#slider-archive .swiper-wrapper {
        height: 400px; } }
    section#slider-archive .swiper-wrapper .archive-year {
      background: #1d1d1d;
      color: #ffffff78;
      border-radius: 50px;
      width: 118px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 22px;
      line-height: 38px; }
  section#slider-archive .carousel_container {
    display: flex;
    flex-direction: column;
    justify-content: space-between; }
  section#slider-archive .carousel_container2 {
    height: 100%;
    display: flex;
    align-items: center; }
  section#slider-archive .carousel_slide {
    opacity: 0; }
    section#slider-archive .carousel_slide.swiper-slide-active {
      transition: all 0.25s ease; }
  section#slider-archive .effect2 {
    opacity: 0.5; }
  section#slider-archive .effect3 {
    opacity: 0.65; }
  section#slider-archive .effect4 {
    opacity: 0.75; }
    section#slider-archive .effect4 .archive-year {
      background: #f05a23 !important;
      color: #000000 !important; }
  section#slider-archive .effect5 {
    opacity: 0.75; }
  section#slider-archive .effect6 {
    opacity: 0.75; }
  section#slider-archive .effect_hide {
    opacity: 0;
    pointer-events: none; }
  section#slider-archive .carousel-two {
    position: relative;
    overflow: hidden;
    width: 80%;
    border-radius: 50px; }
    @media (max-width: 1199.98px) {
      section#slider-archive .carousel-two {
        margin: 0 auto;
        width: 90%; } }
    section#slider-archive .carousel-two .top-panel h3, section#slider-archive .carousel-two .top-panel .h3 {
      font-size: 32px;
      line-height: 48px;
      color: #f05a23; }
      @media (min-width: 1200px) {
        section#slider-archive .carousel-two .top-panel h3, section#slider-archive .carousel-two .top-panel .h3 {
          font-size: 40px;
          line-height: 60px; } }
    section#slider-archive .carousel-two .content-panel {
      background: rgba(254, 247, 244, 0.7);
      border-radius: 50px; }
      section#slider-archive .carousel-two .content-panel .content {
        padding: 25px; }
        section#slider-archive .carousel-two .content-panel .content .row {
          align-content: center;
          justify-content: space-between; }
          section#slider-archive .carousel-two .content-panel .content .row p {
            margin-bottom: 0;
            font-weight: 400;
            font-size: 20px;
            line-height: 32px;
            color: #000000; }
      section#slider-archive .carousel-two .content-panel .full-banner {
        height: 300px;
        width: 100%;
        object-fit: cover; }
  section#slider-archive .m-slider-archive {
    height: 100px; }
    @media (max-width: 1199.98px) {
      section#slider-archive .m-slider-archive {
        height: 65px; } }
    section#slider-archive .m-slider-archive .swiper-slide-active {
      position: relative;
      top: 0; }
      section#slider-archive .m-slider-archive .swiper-slide-active .item-of-year {
        background: #f05a23;
        border-radius: 50px;
        color: #000000;
        font-size: 26px;
        line-height: 38px;
        opacity: 1; }
    section#slider-archive .m-slider-archive .item-of-year {
      background: #1d1d1d;
      border-radius: 50px;
      width: 100px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 26px;
      line-height: 38px;
      color: #ffffff;
      opacity: 0.3;
      margin: 0 auto; }

#awards .header-highlight {
  margin-bottom: 0px; }
  @media (min-width: 1200px) {
    #awards .header-highlight {
      margin-bottom: 60px; } }
  #awards .header-highlight h2, #awards .header-highlight .h2 {
    font-size: 32px;
    line-height: 48px; }
    @media (min-width: 1200px) {
      #awards .header-highlight h2, #awards .header-highlight .h2 {
        font-size: 40px;
        line-height: 60px; } }
  #awards .header-highlight .navigation {
    display: flex;
    justify-content: flex-end;
    position: absolute;
    bottom: 26px;
    right: 17px; }
    @media (max-width: 575.98px) {
      #awards .header-highlight .navigation {
        justify-content: flex-end;
        position: relative;
        bottom: 55px;
        right: 0; } }
    #awards .header-highlight .navigation a.prev {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f7f7f7;
      border: 1px solid #f05a23;
      border-radius: 5px 0 0;
      transition: all 0.25s ease-in; }
      #awards .header-highlight .navigation a.prev svg path {
        fill: #f05a23; }
      #awards .header-highlight .navigation a.prev.active {
        background-color: #f05a23;
        transition: all 0.25s ease-in-out; }
        #awards .header-highlight .navigation a.prev.active svg path {
          fill: #000; }
    #awards .header-highlight .navigation a.next {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f7f7f7;
      border: 1px solid #f05a23;
      border-radius: 0 0 5px;
      transition: all 0.25s ease-in-out; }
      #awards .header-highlight .navigation a.next svg path {
        fill: #f05a23; }
      #awards .header-highlight .navigation a.next.active {
        background-color: #f05a23;
        transition: all 0.25s ease-in-out; }
        #awards .header-highlight .navigation a.next.active svg path {
          fill: #000; }

#awards .awards-main {
  overflow: hidden; }
  #awards .awards-main .single-awards {
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center;
    text-align: center;
    border: 1px solid #dddfe3;
    box-sizing: border-box;
    border-radius: 20px 0px 0px 0px; }
    #awards .awards-main .single-awards .thumb {
      padding: 30px; }
      #awards .awards-main .single-awards .thumb img {
        height: 175px;
        object-fit: contain; }
    #awards .awards-main .single-awards .content {
      border-top: 1px solid #dddfe3;
      padding: 30px; }
      #awards .awards-main .single-awards .content h3, #awards .awards-main .single-awards .content .h3 {
        font-weight: 400;
        font-size: 26px;
        line-height: 38px; }
      #awards .awards-main .single-awards .content p {
        font-weight: normal;
        font-size: 14px;
        line-height: 24px;
        color: #4e4e4e; }

#newsletter {
  padding: 145px 0 145px;
  position: relative;
  z-index: 9;
  background-size: cover; }
  #newsletter::after {
    content: '';
    z-index: -1;
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: #0000006e;
    top: 0; }
  #newsletter h3, #newsletter .h3 {
    font-size: 32px;
    line-height: 48px;
    color: #ffffff; }
    @media (min-width: 1200px) {
      #newsletter h3, #newsletter .h3 {
        font-size: 40px;
        line-height: 60px; } }
  #newsletter p {
    font-size: 16px;
    line-height: 26px;
    color: #c0c0c0; }
  #newsletter .mc4wp-form input[type='email'] {
    width: 100%;
    margin-bottom: 30px;
    height: 58px;
    font-size: 16px;
    line-height: 26px;
    color: #4e4e4e;
    padding: 17px 0px 17px 30px;
    border-radius: 5px 0px 0px 0px;
    background-color: #fef7f4;
    outline: none;
    border: none; }
    @media (min-width: 1200px) {
      #newsletter .mc4wp-form input[type='email'] {
        width: 340px;
        margin-bottom: 0px; } }
  #newsletter .mc4wp-form input[type='submit'] {
    background-color: #f05a23;
    border-radius: 5px 0px 0px 0px;
    width: 170px;
    height: 58px;
    border: none;
    font-weight: bold;
    font-size: 16px;
    line-height: 20px;
    text-transform: uppercase;
    color: #ffffff;
    position: relative;
    left: 0;
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out; }
    @media (min-width: 1200px) {
      #newsletter .mc4wp-form input[type='submit'] {
        left: -10px; } }
    #newsletter .mc4wp-form input[type='submit']:hover {
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out;
      background-color: #424143; }

@media (max-width: 1199.98px) {
  #half-to-half h2, #half-to-half .h2 {
    font-size: 32px;
    line-height: 48px; } }

#half-to-half h2.mobile-title, #half-to-half .mobile-title.h2 {
  display: none; }
  @media (max-width: 575.98px) {
    #half-to-half h2.mobile-title, #half-to-half .mobile-title.h2 {
      display: block; } }

#half-to-half h2.desktop-title, #half-to-half .desktop-title.h2 {
  display: none; }
  @media (min-width: 576px) {
    #half-to-half h2.desktop-title, #half-to-half .desktop-title.h2 {
      display: block; } }

#half-to-half .ceritified {
  display: flex;
  flex-wrap: wrap;
  padding-top: 20px; }
  #half-to-half .ceritified .logo {
    flex: 1;
    padding-right: 20px;
    margin-bottom: 20px; }
    @media (min-width: 576px) {
      #half-to-half .ceritified .logo {
        flex: none; } }
    #half-to-half .ceritified .logo img {
      height: auto; }

#half-to-half p {
  font-size: 16px;
  line-height: 26px;
  color: #4e4e4e; }

#half-to-half ul {
  list-style: none;
  padding: 0 0 30px;
  margin-bottom: 0px; }
  #half-to-half ul li {
    font-size: 16px;
    line-height: 26px;
    color: #4e4e4e;
    padding: 0;
    display: flex; }
    #half-to-half ul li::before {
      content: '\2022';
      color: #f05a23;
      display: inline-block;
      font-size: 22px;
      margin-right: 10px; }

#contribution {
  background-color: #fef7f4; }
  #contribution .row {
    padding-top: 60px; }
    #contribution .row .single-contribute-item {
      text-align: center; }
      @media (max-width: 1199.98px) {
        #contribution .row .single-contribute-item {
          width: 50%;
          margin-bottom: 60px; }
          #contribution .row .single-contribute-item:last-child {
            margin-bottom: 0; } }
      @media (max-width: 767.98px) {
        #contribution .row .single-contribute-item {
          width: 100%; } }
      #contribution .row .single-contribute-item .icon {
        margin: 0 auto;
        margin-bottom: 30px;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        text-align: center;
        background-color: #fff;
        border-radius: 10px;
        align-items: center;
        margin-bottom: 30px;
        box-shadow: -1px -1px 4px #6c483a1a;
        width: 100px;
        height: 100px; }
        #contribution .row .single-contribute-item .icon img {
          width: 50px;
          height: 50px; }
      #contribution .row .single-contribute-item .content {
        width: 100%; }
        #contribution .row .single-contribute-item .content h3, #contribution .row .single-contribute-item .content .h3 {
          font-weight: 400;
          font-size: 20px;
          line-height: 32px; }
        #contribution .row .single-contribute-item .content p {
          font-size: 16px;
          line-height: 26px;
          color: #031c3a; }
  #contribution.another-style .single-contribute-item .icon {
    width: 130px;
    height: 138px;
    display: flex;
    align-items: center;
    padding: 0; }

#asset-map {
  background-size: cover;
  background-repeat: no-repeat;
  overflow: hidden; }
  #asset-map .asset-large-map-desktop {
    display: none; }
  #asset-map .asset-map-desktop {
    display: none; }
  #asset-map .asset-map-mobile {
    display: block; }
  @media (min-width: 1200px) {
    #asset-map .asset-large-map-desktop {
      display: none; }
    #asset-map .asset-map-desktop {
      display: block; }
    #asset-map .asset-map-mobile {
      display: none; } }
  @media (min-width: 1500px) {
    #asset-map .asset-large-map-desktop {
      display: block; }
    #asset-map .asset-map-desktop {
      display: none; }
    #asset-map .asset-map-mobile {
      display: none; } }
  #asset-map img {
    width: 100%; }
  #asset-map .map-svg {
    text-align: center;
    position: relative;
    width: 100%;
    height: auto;
    display: flex;
    margin: 0 auto; }
    @media (min-width: 1200px) {
      #asset-map .map-svg {
        width: 650px; } }
    #asset-map .map-svg img {
      height: 100%;
      width: 100%; }
    #asset-map .map-svg .point {
      position: absolute; }
      #asset-map .map-svg .point.one {
        top: 180px;
        left: 184px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.one {
            top: 80px;
            left: 100px; } }
      #asset-map .map-svg .point.two {
        top: 222px;
        left: 215px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.two {
            top: 110px;
            left: 120px; } }
      #asset-map .map-svg .point.three {
        top: 214px;
        left: 394px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.three {
            top: 170px;
            left: 140px; } }
      #asset-map .map-svg .point.four {
        top: 310px;
        left: 237px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.four {
            top: 100px;
            left: 190px; } }
      #asset-map .map-svg .point.five {
        top: 310px;
        left: 320px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.five {
            top: 150px;
            left: 190px; } }
      #asset-map .map-svg .point.six {
        top: 280px;
        left: 353px;
        z-index: 8; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.six {
            top: 170px;
            left: 190px; } }
      #asset-map .map-svg .point.seven {
        left: 134px;
        top: 615px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.seven {
            top: 270px;
            left: 80px; } }
      #asset-map .map-svg .point.eight {
        left: 114px;
        top: 520px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.eight {
            top: 290px;
            left: 100px; } }
      #asset-map .map-svg .point.nine {
        left: 150px;
        top: 554px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.nine {
            top: 320px;
            left: 80px; } }
      #asset-map .map-svg .point.ten {
        left: 236px;
        top: 616px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.ten {
            top: 320px;
            left: 130px; } }
      #asset-map .map-svg .point.eleven {
        right: 30px;
        top: 550px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.eleven {
            top: 300px;
            right: 0px; } }
      #asset-map .map-svg .point.twelve {
        right: 77px;
        top: 550px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.twelve {
            top: 300px;
            right: 30px; } }
      #asset-map .map-svg .point.thirty {
        right: 127px;
        top: 563px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.thirty {
            top: 300px;
            right: 50px; } }
      #asset-map .map-svg .point.fourten {
        right: 115px;
        top: 620px; }
        @media (max-width: 575.98px) {
          #asset-map .map-svg .point.fourten {
            top: 350px;
            right: 50px; } }
      #asset-map .map-svg .point .white-bullet {
        border: 1px solid #fff;
        width: 15px;
        height: 15px;
        border-radius: 100%;
        background-color: #fff;
        position: absolute;
        top: 36%;
        left: 0;
        right: 0;
        margin-left: auto;
        margin-right: auto;
        z-index: 9; }
      #asset-map .map-svg .point .blue-wiling {
        background-color: #118dff;
        width: 50px;
        height: 50px;
        border-radius: 100%;
        position: relative;
        z-index: 9;
        backdrop-filter: blur(4px);
        opacity: 0.45;
        border: 1px solid #118dff; }
  #asset-map .item-row {
    display: flex;
    justify-content: center;
    column-gap: 30px; }
    @media (max-width: 1199.98px) {
      #asset-map .item-row {
        flex-direction: column; } }
    #asset-map .item-row .item-map {
      display: flex;
      align-items: center; }
      @media (max-width: 1199.98px) {
        #asset-map .item-row .item-map {
          padding-bottom: 20px; } }
      #asset-map .item-row .item-map .icon {
        margin-right: 15px; }
      #asset-map .item-row .item-map .content span {
        font-weight: 400;
        font-size: 26px;
        line-height: 38px;
        color: #ffffff; }

.core-services {
  position: relative; }
  .core-services .services-highlighted-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 60px 0 60px;
    justify-content: space-between;
    align-items: flex-start; }
    .core-services .services-highlighted-row .service {
      width: 350px;
      display: flex;
      flex-wrap: wrap;
      justify-content: center; }
      @media (max-width: 1199.98px) {
        .core-services .services-highlighted-row .service {
          width: 320px; } }
      @media (max-width: 991.98px) {
        .core-services .services-highlighted-row .service {
          margin-bottom: 30px;
          flex-direction: column;
          align-items: center;
          width: 50%; } }
      @media (max-width: 767.98px) {
        .core-services .services-highlighted-row .service {
          width: 100%; } }
      .core-services .services-highlighted-row .service .icon {
        width: 100px;
        height: 100px;
        background-color: #fff;
        border-radius: 10px;
        text-align: center;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 30px;
        box-shadow: -1px -1px 4px #6c483a1a; }
        .core-services .services-highlighted-row .service .icon img {
          width: 50px;
          height: auto; }
      .core-services .services-highlighted-row .service .icon-content h4, .core-services .services-highlighted-row .service .icon-content .h4 {
        font-weight: 400;
        font-size: 26px;
        line-height: 38px;
        color: #000000;
        text-align: center; }
        @media (max-width: 1199.98px) {
          .core-services .services-highlighted-row .service .icon-content h4, .core-services .services-highlighted-row .service .icon-content .h4 {
            font-size: 22px; } }
  .core-services h3, .core-services .h3 {
    font-weight: 400;
    font-size: 36px;
    line-height: 48px;
    text-align: center;
    color: #000000; }
  .core-services .service-list-row {
    display: flex;
    justify-content: center;
    padding: 60px 0 60px;
    border-bottom: 1px solid #dddfe3;
    width: 100%;
    margin: 0 auto;
    flex-direction: column; }
    @media (min-width: 1200px) {
      .core-services .service-list-row {
        flex-direction: row; } }
    .core-services .service-list-row .list {
      display: flex;
      align-items: center;
      width: 360px;
      justify-content: center; }
      @media (max-width: 1199.98px) {
        .core-services .service-list-row .list {
          padding-bottom: 20px;
          width: 100%;
          justify-content: flex-start; } }
      .core-services .service-list-row .list .icon {
        margin-right: 15px;
        width: 15px; }
      .core-services .service-list-row .list .content {
        padding-right: 30px;
        font-weight: 400;
        font-size: 20px;
        line-height: 32px;
        color: #c0c0c0; }
  .core-services .short-list-row {
    width: 100%;
    margin: 0 auto;
    padding-top: 60px;
    display: flex;
    flex-wrap: wrap; }
    @media (min-width: 1200px) {
      .core-services .short-list-row {
        width: 920px; } }
    .core-services .short-list-row h3, .core-services .short-list-row .h3 {
      font-size: 40px;
      line-height: 60px;
      text-align: left; }
      @media (max-width: 1199.98px) {
        .core-services .short-list-row h3, .core-services .short-list-row .h3 {
          font-size: 32px;
          line-height: 48px;
          padding-bottom: 60px; } }
    .core-services .short-list-row .left {
      width: 50%; }
      @media (max-width: 1199.98px) {
        .core-services .short-list-row .left {
          width: 100%; } }
    .core-services .short-list-row .right {
      width: 50%; }
      @media (max-width: 1199.98px) {
        .core-services .short-list-row .right {
          width: 100%; } }
      .core-services .short-list-row .right .list-row .list {
        display: flex;
        padding-bottom: 20px; }
        @media (max-width: 1199.98px) {
          .core-services .short-list-row .right .list-row .list {
            flex-wrap: nowrap;
            align-content: center; } }
        .core-services .short-list-row .right .list-row .list .icon {
          margin-right: 10px;
          width: 40px;
          margin-top: 5px; }
        .core-services .short-list-row .right .list-row .list .content {
          width: calc(100% - 40px); }
          .core-services .short-list-row .right .list-row .list .content span {
            font-weight: 400;
            font-size: 20px;
            line-height: 32px;
            color: #000000; }
  .core-services .support-bussiness-title {
    margin: 30px 0 0; }

#united-city {
  background-color: #fafafa; }
  #united-city h2, #united-city .h2 {
    font-size: 40px;
    line-height: 60px;
    margin-bottom: 60px; }
    @media (max-width: 1199.98px) {
      #united-city h2, #united-city .h2 {
        font-size: 32px;
        line-height: 48px; } }
  #united-city img {
    width: 100%;
    height: auto;
    margin-bottom: 60px; }
  #united-city p {
    font-size: 16px;
    line-height: 26px;
    color: #4e4e4e; }
  #united-city .icon-row {
    display: flex;
    flex-wrap: wrap; }
    #united-city .icon-row .icon-item {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 30px;
      width: 50%; }
      #united-city .icon-row .icon-item .icon {
        width: 60px; }
        @media (max-width: 575.98px) {
          #united-city .icon-row .icon-item .icon {
            width: 50px; } }
        #united-city .icon-row .icon-item .icon img {
          position: relative;
          top: 5px;
          width: auto;
          margin-bottom: 0; }
      #united-city .icon-row .icon-item .icon-content h4, #united-city .icon-row .icon-item .icon-content .h4 {
        margin-bottom: 5px;
        font-weight: 600;
        font-size: 20px;
        line-height: 26px; }
        @media (max-width: 575.98px) {
          #united-city .icon-row .icon-item .icon-content h4, #united-city .icon-row .icon-item .icon-content .h4 {
            font-size: 18px; } }
      #united-city .icon-row .icon-item .icon-content span {
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #4e4e4e; }
        @media (max-width: 575.98px) {
          #united-city .icon-row .icon-item .icon-content span {
            font-size: 12px; } }

#education-service-item .item {
  margin-bottom: 60px; }
  @media (max-width: 1199.98px) {
    #education-service-item .item {
      margin-bottom: 40px; } }
  #education-service-item .item:last-child {
    margin-bottom: 0; }
  #education-service-item .item h3, #education-service-item .item .h3 {
    font-weight: 400;
    font-size: 36px;
    line-height: 46px; }
    @media (max-width: 1199.98px) {
      #education-service-item .item h3, #education-service-item .item .h3 {
        margin-top: 30px; } }
  #education-service-item .item p {
    font-size: 16px;
    line-height: 26px;
    color: #000000;
    opacity: 0.8; }
  #education-service-item .item img {
    height: auto; }
  #education-service-item .item:nth-child(even) .row {
    flex-direction: row-reverse; }

#photo-gallery .photo-gallery-nav-desktop {
  display: none; }
  @media (min-width: 992px) {
    #photo-gallery .photo-gallery-nav-desktop {
      display: block; } }

#photo-gallery .photo-gallery-nav-mobile {
  display: none; }
  @media (max-width: 991.98px) {
    #photo-gallery .photo-gallery-nav-mobile {
      display: block; } }

#photo-gallery .photo-gallery-nav-mobile {
  border-bottom: 2px solid #dddfe3;
  padding-bottom: 10px;
  position: relative; }
  #photo-gallery .photo-gallery-nav-mobile::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    background-color: #f05a23;
    height: 2px;
    width: 50%; }
  #photo-gallery .photo-gallery-nav-mobile .swiper-slide {
    width: 40%;
    text-align: center; }
    #photo-gallery .photo-gallery-nav-mobile .swiper-slide a {
      font-weight: 500;
      text-transform: uppercase; }
    #photo-gallery .photo-gallery-nav-mobile .swiper-slide.swiper-slide-active a {
      color: #f05a23;
      font-weight: 500; }

#photo-gallery ul {
  display: flex;
  list-style: none;
  text-transform: uppercase;
  justify-content: center;
  position: relative;
  padding: 0; }
  #photo-gallery ul li a {
    font-weight: 500;
    font-size: 14px;
    line-height: 24px;
    color: #4e4e4e;
    text-align: center;
    padding: 14px 16px;
    border-bottom: 2px solid #dddfe3; }
    #photo-gallery ul li a.active {
      color: #f05a23;
      font-weight: bold;
      border-bottom: 2px solid #f05a23; }

#photo-gallery .gallery {
  margin-top: 60px; }
  #photo-gallery .gallery .single-gallery-item {
    margin-bottom: 30px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    background: #fafafa;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap; }
    #photo-gallery .gallery .single-gallery-item .single-col {
      flex: 50%; }
      #photo-gallery .gallery .single-gallery-item .single-col img {
        width: 100%;
        object-fit: cover; }
      #photo-gallery .gallery .single-gallery-item .single-col:nth-child(1) {
        padding-right: 10px; }
        #photo-gallery .gallery .single-gallery-item .single-col:nth-child(1) img {
          height: 234px; }
      #photo-gallery .gallery .single-gallery-item .single-col:nth-child(2) {
        position: relative; }
        #photo-gallery .gallery .single-gallery-item .single-col:nth-child(2) img {
          height: 117px; }
          #photo-gallery .gallery .single-gallery-item .single-col:nth-child(2) img:first-child {
            padding-bottom: 10px; }
        #photo-gallery .gallery .single-gallery-item .single-col:nth-child(2)::after {
          content: attr(data-total);
          position: absolute;
          left: 0;
          width: 100%;
          height: 50%;
          background-color: #00000063;
          font-weight: 400;
          font-size: 26px;
          line-height: 38px;
          color: #ffffff;
          text-align: center;
          padding-top: 20%; }
    #photo-gallery .gallery .single-gallery-item .content h2, #photo-gallery .gallery .single-gallery-item .content .h2 {
      margin: 5px 0px;
      font-weight: 400;
      font-size: 20px;
      line-height: 32px; }
    #photo-gallery .gallery .single-gallery-item .content span {
      color: #4e4e4e;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px; }

.video-gallery .video-poster-image {
  cursor: pointer;
  position: relative; }
  .video-gallery .video-poster-image img {
    object-fit: cover; }
  .video-gallery .video-poster-image .play-btn {
    position: absolute;
    bottom: 15px;
    left: 0;
    z-index: 9;
    padding: 0 20px 0 20px;
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    align-items: center; }
    .video-gallery .video-poster-image .play-btn svg#play-svg {
      width: 40px;
      height: 66px; }
    .video-gallery .video-poster-image .play-btn .title {
      font-weight: 400;
      font-size: 16px;
      line-height: 26px;
      color: #ffffff;
      padding-left: 10px;
      width: calc(100% - 40px);
      text-transform: uppercase; }
    .video-gallery .video-poster-image .play-btn:hover svg path {
      fill: #f05a23; }

.video-gallery .thumb {
  position: relative;
  border-radius: 20px 0 0 0;
  overflow: hidden; }

.video-gallery .overlay {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #000000a3;
  z-index: 0;
  display: flex;
  align-items: flex-end; }

#reserach-investor .research-nav-menu {
  margin-bottom: 60px; }
  #reserach-investor .research-nav-menu li a {
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    text-transform: uppercase;
    color: #4e4e4e;
    border-bottom: 2px solid #dddfe3;
    padding: 0 50px 10px 50px;
    transition: all 0.25s ease-in-out; }
    #reserach-investor .research-nav-menu li a.active {
      border-bottom: 2px solid #f05a23;
      transition: all 0.25s ease-in; }

#reserach-investor #research-accordion .accordion-item {
  border: none;
  margin-bottom: 30px; }
  #reserach-investor #research-accordion .accordion-item .accordion-header {
    margin-bottom: 0;
    border-top: 1px solid #00000008;
    border-bottom: 1px solid #00000008; }
  #reserach-investor #research-accordion .accordion-item .accordion-button:not(.collapsed) {
    background-color: transparent;
    border-radius: 0;
    box-shadow: none; }
    #reserach-investor #research-accordion .accordion-item .accordion-button:not(.collapsed)::after {
      background-image: url("data:image/svg+xml, %3Csvg width=\"15\" height=\"3\" viewBox=\"0 0 15 3\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M14.7917 0.458344H0.208374V2.54168H14.7917V0.458344Z\" fill=\"%23F05A23\"/%3E%3C/svg%3E"); }
  #reserach-investor #research-accordion .accordion-item .accordion-item:first-of-type .accordion-button {
    background-color: transparent;
    border-radius: 0; }
  #reserach-investor #research-accordion .accordion-item .accordion-button {
    font-weight: 400;
    font-size: 20px;
    line-height: 32px;
    color: #000000;
    text-transform: uppercase; }
    #reserach-investor #research-accordion .accordion-item .accordion-button:focus {
      border: none;
      box-shadow: none; }
    #reserach-investor #research-accordion .accordion-item .accordion-button::after {
      background-image: url("data:image/svg+xml, %3Csvg width=\"15\" height=\"15\" viewBox=\"0 0 15 15\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M14.7917 8.54167H8.54171V14.7917H6.45837V8.54167H0.208374V6.45834H6.45837V0.208336H8.54171V6.45834H14.7917V8.54167Z\" fill=\"black\"/%3E%3C/svg%3E"); }
  #reserach-investor #research-accordion .accordion-item .accordion-body {
    margin-top: 20px;
    padding: 0; }
    #reserach-investor #research-accordion .accordion-item .accordion-body .item {
      background: #f5f5f582;
      padding: 15px; }
      #reserach-investor #research-accordion .accordion-item .accordion-body .item .left {
        padding-right: 30px; }
      #reserach-investor #research-accordion .accordion-item .accordion-body .item h3, #reserach-investor #research-accordion .accordion-item .accordion-body .item .h3 {
        font-weight: 400;
        font-size: 20px;
        line-height: 32px;
        margin: 0;
        color: #000000; }
      #reserach-investor #research-accordion .accordion-item .accordion-body .item h4, #reserach-investor #research-accordion .accordion-item .accordion-body .item .h4 {
        font-weight: 400;
        font-size: 16px;
        line-height: 26px;
        color: #4e4e4e; }
      #reserach-investor #research-accordion .accordion-item .accordion-body .item a {
        color: #f05a23;
        font-weight: bold;
        font-size: 16px;
        line-height: 20px;
        text-transform: uppercase; }
        #reserach-investor #research-accordion .accordion-item .accordion-body .item a svg {
          position: relative;
          top: -4px; }
        @media (max-width: 575.98px) {
          #reserach-investor #research-accordion .accordion-item .accordion-body .item a span {
            display: none; } }
      #reserach-investor #research-accordion .accordion-item .accordion-body .item:first-child {
        border: 1px solid #00000008;
        background: #f5f5f5; }

#reserach-investor .research-nav-mobile.for-mobile {
  margin-bottom: 30px;
  border-bottom: 2px solid #dddfe3;
  padding-bottom: 10px;
  position: relative; }
  #reserach-investor .research-nav-mobile.for-mobile::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    background-color: #f05a23;
    height: 2px;
    width: 50%; }
  #reserach-investor .research-nav-mobile.for-mobile .swiper-slide {
    font-weight: 500;
    text-transform: uppercase; }
    #reserach-investor .research-nav-mobile.for-mobile .swiper-slide.swiper-slide-active a {
      color: #f05a23;
      font-weight: 500; }
  #reserach-investor .research-nav-mobile.for-mobile .swiper-slide {
    max-width: 200px;
    text-align: left; }

#cheif-message .cheif-message-archive {
  width: 100%;
  margin: 0 auto 40px auto;
  height: auto;
  overflow: hidden;
  position: relative;
  border-bottom: 2px solid #dddfe3; }
  @media (min-width: 1300px) {
    #cheif-message .cheif-message-archive {
      width: 930px; } }
  #cheif-message .cheif-message-archive .swiper-wrapper {
    padding-bottom: 10px; }
    #cheif-message .cheif-message-archive .swiper-wrapper .swiper-slide {
      font-weight: bold;
      font-size: 14px;
      line-height: 24px;
      text-transform: uppercase;
      color: #4e4e4e;
      cursor: pointer; }
      #cheif-message .cheif-message-archive .swiper-wrapper .swiper-slide.swiper-slide-active {
        color: #f05a23;
        padding-left: 20px; }
  #cheif-message .cheif-message-archive .cheif-button-prev {
    transform: rotateY(180deg);
    position: absolute;
    top: 0;
    left: 0px;
    cursor: pointer;
    z-index: 9; }
  #cheif-message .cheif-message-archive .cheif-button-next {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    z-index: 9; }

#cheif-message .cheif-message-content {
  width: 100%;
  margin: 0 auto;
  overflow: hidden;
  border-bottom: 2px solid #dddfe3; }
  @media (min-width: 1300px) {
    #cheif-message .cheif-message-content {
      width: 930px; } }
  @media (max-width: 991.98px) {
    #cheif-message .cheif-message-content h2, #cheif-message .cheif-message-content .h2 {
      font-size: 32px;
      line-height: 48px; } }
  #cheif-message .cheif-message-content p {
    font-size: 16px;
    line-height: 26px;
    color: #4e4e4e;
    margin-bottom: 15px; }

#cheif-message .bio {
  width: 100%;
  text-align: left;
  padding-top: 60px; }
  @media (min-width: 1200px) {
    #cheif-message .bio {
      width: 930px;
      margin: 0 auto; } }
  #cheif-message .bio h3, #cheif-message .bio .h3 {
    font-size: 26px;
    font-weight: 400; }
  #cheif-message .bio h4, #cheif-message .bio .h4 {
    font-size: 26px;
    font-weight: 400; }
  @media (max-width: 1199.98px) {
    #cheif-message .bio .social {
      padding-top: 30px; } }
  #cheif-message .bio .social li a {
    padding: 15px; }

.single-site-main .post-meta {
  padding-top: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px; }
  .single-site-main .post-meta .post-date {
    font-size: 14px;
    line-height: 24px;
    color: #4e4e4e; }
  .single-site-main .post-meta .social li a {
    padding: 10px; }

.single-site-main article {
  padding-top: 50px !important;
  padding-bottom: 100px; }
  @media (max-width: 1199.98px) {
    .single-site-main article {
      padding-bottom: 50px; } }

.single-site-main h2, .single-site-main .h2 {
  font-size: 32px;
  line-height: 48px; }
  @media (min-width: 1200px) {
    .single-site-main h2, .single-site-main .h2 {
      font-size: 40px;
      line-height: 60px; } }

.single-site-main p {
  font-size: 16px;
  line-height: 26px;
  color: #4e4e4e; }

.may-also-like {
  background: #fef7f4;
  padding: 100px 0 100px; }
  @media (max-width: 1199.98px) {
    .may-also-like {
      padding: 50px 0 50px; } }
  .may-also-like .page-section {
    margin-bottom: 60px; }
    @media (max-width: 991.98px) {
      .may-also-like .page-section {
        margin-bottom: 40px; } }
    .may-also-like .page-section h3, .may-also-like .page-section .h3 {
      font-size: 32px;
      line-height: 48px; }
      @media (min-width: 1200px) {
        .may-also-like .page-section h3, .may-also-like .page-section .h3 {
          font-size: 40px;
          line-height: 60px; } }
    .may-also-like .page-section p {
      color: #4e4e4e;
      font-size: 16px;
      line-height: 26px; }
    .may-also-like .page-section .cta {
      top: -30px;
      float: right; }
      @media (max-width: 991.98px) {
        .may-also-like .page-section .cta {
          float: left; } }
  @media (min-width: 992px) {
    .may-also-like .three-blog-column {
      display: flex;
      flex-wrap: wrap; } }
  .may-also-like .three-blog-column .image {
    margin-bottom: 30px; }
    .may-also-like .three-blog-column .image img {
      border-radius: 20px 0 0 0;
      height: 230px;
      width: 100%;
      object-fit: cover; }
  .may-also-like .three-blog-column .content .post-date {
    font-size: 14px;
    line-height: 24px;
    color: #4e4e4e; }
  .may-also-like .three-blog-column .content h2, .may-also-like .three-blog-column .content .h2 {
    font-size: 20px;
    line-height: 32px; }
    .may-also-like .three-blog-column .content h2 a, .may-also-like .three-blog-column .content .h2 a {
      font-weight: 400; }
  .may-also-like .three-blog-column .content p {
    font-size: 16px;
    line-height: 26px;
    color: #4e4e4e; }

.single-career .career-banner-bg {
  padding: 200px 0 200px;
  background-size: cover;
  position: relative;
  z-index: 1; }
  .single-career .career-banner-bg::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000000;
    opacity: 0.6;
    transform: matrix(1, 0, 0, -1, 0, 0);
    z-index: -1; }
  .single-career .career-banner-bg .content {
    max-width: 730px;
    margin: 0 auto;
    text-align: center;
    z-index: 2;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 30px;
    align-items: center;
    flex-wrap: wrap; }
    .single-career .career-banner-bg .content h2, .single-career .career-banner-bg .content .h2 {
      font-family: Barlow;
      font-size: 64px;
      font-weight: 600;
      line-height: 75px;
      text-align: center;
      color: #FFF;
      text-transform: none;
      margin-bottom: 0; }
    .single-career .career-banner-bg .content .group-action {
      display: flex;
      justify-content: center;
      gap: 15px; }
      .single-career .career-banner-bg .content .group-action .item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        text-align: center;
        flex-wrap: wrap;
        min-width: 130px;
        min-height: 40px;
        box-sizing: border-box;
        padding: 8px 20px;
        border-radius: 5px 0px 0px 0px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid #FFFFFF;
        transition: all 0.3s ease; }
        .single-career .career-banner-bg .content .group-action .item:hover {
          background: rgba(255, 255, 255, 0.1); }
        .single-career .career-banner-bg .content .group-action .item .item-content {
          color: #ffffff;
          font-size: 14px;
          font-weight: 500;
          line-height: 24px;
          cursor: default; }

@media (min-width: 1200px) {
  .single-career #career-content-details .content {
    max-width: 830px;
    margin: 0 auto; } }

@media screen and (max-width: 1024px) {
  .single-career .career-banner-bg .content h2, .single-career .career-banner-bg .content .h2 {
    font-size: 40px;
    line-height: 60px; } }

@media screen and (max-width: 560px) {
  .single-career .career-banner-bg .content {
    padding-left: 15px;
    padding-right: 15px; }
    .single-career .career-banner-bg .content .group-action {
      flex-wrap: wrap;
      gap: 10px; } }

.single-post .post-banner {
  margin-bottom: 30px;
  border-radius: 20px 0 0 0;
  width: 100%;
  max-height: 500px; }

.single-post .single-site-main article {
  max-width: 830px;
  margin: 0 auto; }

#contact-form {
  background-color: #fafafa; }
  #contact-form h3, #contact-form .h3 {
    color: #000000;
    text-align: center;
    font-size: 32px;
    line-height: 48px; }
    @media (min-width: 1200px) {
      #contact-form h3, #contact-form .h3 {
        font-size: 40px;
        line-height: 60px; } }
  #contact-form p {
    font-weight: 400;
    font-size: 20px;
    line-height: 32px;
    color: #4e4e4e;
    text-align: center; }
  #contact-form form {
    margin-top: 60px; }
    #contact-form form .form-group {
      margin-bottom: 30px;
      /* For IE10 */ }
      #contact-form form .form-group input,
      #contact-form form .form-group select {
        width: 100%;
        height: 50px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        box-sizing: border-box;
        border-radius: 4px;
        padding: 12px 15px;
        color: #4e4e4e;
        transition: all 1ms ease-in-out; }
        #contact-form form .form-group input:focus,
        #contact-form form .form-group select:focus {
          outline: none;
          border: 2px solid #f05a23;
          transition: all 1ms ease-in; }
      #contact-form form .form-group input::placeholder {
        color: #4e4e4e; }
      #contact-form form .form-group select {
        /* for Firefox */
        -moz-appearance: none;
        /* for Chrome */
        -webkit-appearance: none; }
      #contact-form form .form-group select::-ms-expand {
        display: none; }
      #contact-form form .form-group.arrow {
        position: relative; }
        #contact-form form .form-group.arrow::after {
          position: absolute;
          content: '';
          background-image: url("data:image/svg+xml, %3Csvg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.0773 6.07739L17.2559 7.2559L9.99994 14.5118L2.74402 7.2559L3.92253 6.07739L9.99994 12.1548L16.0773 6.07739Z\" fill=\"%234E4E4E\"/%3E%3C/svg%3E");
          right: 0;
          width: 50px;
          height: 50px;
          background-repeat: no-repeat;
          top: 15px; }
      #contact-form form .form-group textarea {
        width: 100%;
        border: 1px solid rgba(0, 0, 0, 0.1);
        box-sizing: border-box;
        border-radius: 4px;
        padding: 12px 15px;
        color: #4e4e4e;
        transition: all 0.25s ease-in-out; }
        #contact-form form .form-group textarea:focus {
          outline: none;
          border: 2px solid #f05a23;
          transition: all 0.25s ease-in; }
      #contact-form form .form-group input[type='submit'] {
        width: 215px;
        height: 50px;
        margin-top: 30px;
        background: #f05a23;
        border-radius: 5px 0px 0px 0px;
        text-transform: uppercase;
        color: #000000;
        border: none;
        transition: all 0.25s ease-in-out;
        font-weight: 500;
        font-size: 16px; }
        #contact-form form .form-group input[type='submit']:hover {
          background-color: #424143;
          color: #ffffff;
          transition: all 0.25s ease-in; }

#contact-map .custom-row {
  display: flex;
  flex-wrap: wrap; }
  #contact-map .custom-row .left-side {
    background-repeat: no-repeat;
    background-size: cover;
    max-width: 100%;
    flex: 100%; }
    @media (min-width: 1200px) {
      #contact-map .custom-row .left-side {
        max-width: 50%;
        flex: 50%; } }
    #contact-map .custom-row .left-side .custom-container {
      height: 100%;
      position: relative; }
      #contact-map .custom-row .left-side .custom-container .map-heading {
        text-align: center;
        padding: 30px;
        font-size: 38px;
        line-height: 48px;
        color: #ffffff; }
        @media (min-width: 1200px) {
          #contact-map .custom-row .left-side .custom-container .map-heading {
            padding: 100px 0;
            font-size: 40px;
            line-height: 60px;
            text-align: left; } }
      #contact-map .custom-row .left-side .custom-container .box-contact-info {
        background-color: #ffffffb8;
        height: auto;
        width: 100%;
        bottom: 0;
        padding: 30px;
        padding: 30px 30px 0; }
        @media (min-width: 1200px) {
          #contact-map .custom-row .left-side .custom-container .box-contact-info {
            border-radius: 20px 0px 0px 0px;
            position: absolute;
            height: 480px;
            padding: 50px 60px 0; } }
        #contact-map .custom-row .left-side .custom-container .box-contact-info .address {
          margin-bottom: 40px; }
          #contact-map .custom-row .left-side .custom-container .box-contact-info .address .item {
            display: flex;
            flex-wrap: wrap;
            padding-bottom: 40px; }
            #contact-map .custom-row .left-side .custom-container .box-contact-info .address .item:last-child {
              padding-bottom: 0; }
            #contact-map .custom-row .left-side .custom-container .box-contact-info .address .item .icon {
              max-width: 30px; }
            #contact-map .custom-row .left-side .custom-container .box-contact-info .address .item .content {
              max-width: calc(100% - 30px);
              padding-left: 20px; }
              #contact-map .custom-row .left-side .custom-container .box-contact-info .address .item .content span {
                font-size: 16px;
                line-height: 26px; }
        @media (max-width: 1199.98px) {
          #contact-map .custom-row .left-side .custom-container .box-contact-info .social-icons {
            padding-bottom: 30px; } }
        #contact-map .custom-row .left-side .custom-container .box-contact-info .social-icons .icon {
          width: 40px;
          height: 40px;
          margin-right: 15px;
          background-color: #f05a2344;
          border-radius: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.25s ease; }
          #contact-map .custom-row .left-side .custom-container .box-contact-info .social-icons .icon:hover {
            background-color: #f05a23;
            transition: all 0.25s ease; }
            #contact-map .custom-row .left-side .custom-container .box-contact-info .social-icons .icon:hover svg path {
              fill: #fff; }
  #contact-map .custom-row .right-side {
    max-width: 50%;
    flex: 100%;
    text-align: right; }
    @media (max-width: 1199.98px) {
      #contact-map .custom-row .right-side {
        max-width: 100%;
        flex: 50%; } }
    #contact-map .custom-row .right-side img {
      width: 100%;
      object-fit: cover;
      max-height: 550px; }
      @media (min-width: 1300px) {
        #contact-map .custom-row .right-side img {
          max-height: 550px; } }
      @media (min-width: 1500px) {
        #contact-map .custom-row .right-side img {
          max-height: 650px; } }

#image-slider {
  position: relative; }
  #image-slider .swiper .swiper-slide {
    height: auto; }
    @media (min-width: 1200px) {
      #image-slider .swiper .swiper-slide {
        height: 100%; } }
  #image-slider .swiper .swiper-slide .slider-content {
    position: relative; }
    #image-slider .swiper .swiper-slide .slider-content img {
      display: block;
      width: 100%;
      height: 300px;
      object-fit: cover; }
      @media (min-width: 1200px) {
        #image-slider .swiper .swiper-slide .slider-content img {
          height: 600px; } }
      @media (min-width: 1500px) {
        #image-slider .swiper .swiper-slide .slider-content img {
          height: 800px; } }
    #image-slider .swiper .swiper-slide .slider-content h4, #image-slider .swiper .swiper-slide .slider-content .h4 {
      position: absolute;
      bottom: 30px;
      left: 50px;
      color: #fff;
      background: rgba(57, 57, 57, 0.6);
      backdrop-filter: blur(5px);
      padding: 5px 20px 5px 10px; }
      @media (max-width: 991.98px) {
        #image-slider .swiper .swiper-slide .slider-content h4, #image-slider .swiper .swiper-slide .slider-content .h4 {
          bottom: -7px;
          left: 0; } }
  #image-slider .banner-slide-next-btn {
    width: 50px;
    height: 50px;
    line-height: 48px;
    right: 50px;
    background-color: rgba(255, 99, 96, 0.6);
    position: absolute;
    bottom: 30px;
    z-index: 2;
    outline: none;
    color: #fff;
    cursor: pointer;
    text-align: center;
    border-radius: 0 0 10px;
    transition: all 1ms ease-in-out; }
    @media (max-width: 991.98px) {
      #image-slider .banner-slide-next-btn {
        top: 50%;
        bottom: 50%;
        right: 0; } }
    #image-slider .banner-slide-next-btn svg path {
      fill: #ffffff; }
    #image-slider .banner-slide-next-btn.active {
      background-color: #f05a23;
      transition: all 1ms ease-in; }
  #image-slider .banner-slide-prev-btn {
    width: 50px;
    height: 50px;
    line-height: 48px;
    right: 100px;
    background-color: rgba(255, 99, 96, 0.6);
    position: absolute;
    bottom: 30px;
    z-index: 2;
    outline: none;
    color: #fff;
    cursor: pointer;
    text-align: center;
    border-radius: 10px 0 0;
    transition: all 1ms ease-in-out; }
    @media (max-width: 991.98px) {
      #image-slider .banner-slide-prev-btn {
        top: 50%;
        bottom: 50%;
        left: 0; } }
    #image-slider .banner-slide-prev-btn svg path {
      fill: #ffffff; }
    #image-slider .banner-slide-prev-btn.active {
      background-color: #f05a23;
      transition: all 1ms ease-in; }
  @media (min-width: 1200px) {
    #image-slider.wider-height .swiper .swiper-slide img {
      height: 600px; } }
  @media (min-width: 1500px) {
    #image-slider.wider-height .swiper .swiper-slide img {
      height: 800px; } }

.carousel-slider {
  padding-top: 80px;
  padding-bottom: 80px; }
  @media (max-width: 991.98px) {
    .carousel-slider {
      padding-top: 60px;
      padding-bottom: 60px; } }
  .carousel-slider h2, .carousel-slider .h2 {
    font-size: 32px;
    line-height: 48px; }
    @media (min-width: 1200px) {
      .carousel-slider h2, .carousel-slider .h2 {
        font-size: 40px;
        line-height: 60px; } }
  .carousel-slider .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center; }
    .carousel-slider .section-header .navigation {
      display: flex;
      justify-content: flex-end; }
      .carousel-slider .section-header .navigation a.prev {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f7f7f7;
        border: 1px solid #f05a23;
        border-radius: 5px 0 0;
        transition: all 0.25s ease-in; }
        .carousel-slider .section-header .navigation a.prev svg path {
          fill: #f05a23; }
        .carousel-slider .section-header .navigation a.prev.active {
          background-color: #f05a23;
          transition: all 0.25s ease-in-out; }
          .carousel-slider .section-header .navigation a.prev.active svg path {
            fill: #000; }
      .carousel-slider .section-header .navigation a.next {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f7f7f7;
        border: 1px solid #f05a23;
        border-radius: 0 0 5px;
        transition: all 0.25s ease-in-out; }
        .carousel-slider .section-header .navigation a.next svg path {
          fill: #f05a23; }
        .carousel-slider .section-header .navigation a.next.active {
          background-color: #f05a23;
          transition: all 0.25s ease-in-out; }
          .carousel-slider .section-header .navigation a.next.active svg path {
            fill: #000; }
  .carousel-slider .navigation {
    display: flex;
    justify-content: flex-end; }
    .carousel-slider .navigation a.prev {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f7f7f7;
      border: 1px solid #f05a23;
      border-radius: 5px 0 0;
      transition: all 0.25s ease-in; }
      .carousel-slider .navigation a.prev svg path {
        fill: #f05a23; }
      .carousel-slider .navigation a.prev.active {
        background-color: #f05a23;
        transition: all 0.25s ease-in-out; }
        .carousel-slider .navigation a.prev.active svg path {
          fill: #000; }
    .carousel-slider .navigation a.next {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f7f7f7;
      border: 1px solid #f05a23;
      border-radius: 0 0 5px;
      transition: all 0.25s ease-in-out; }
      .carousel-slider .navigation a.next svg path {
        fill: #f05a23; }
      .carousel-slider .navigation a.next.active {
        background-color: #f05a23;
        transition: all 0.25s ease-in-out; }
        .carousel-slider .navigation a.next.active svg path {
          fill: #000; }
  .carousel-slider .myCarouselSwiper {
    margin-top: 60px; }
  .carousel-slider .swiper-slide {
    max-width: 255px;
    align-items: center;
    width: 100%;
    border-left: 1px solid #f5f5f5;
    border-top: 1px solid #f5f5f5;
    border-bottom: 1px solid #f5f5f5;
    height: auto;
    display: flex;
    justify-content: center; }
    @media (max-width: 991.98px) {
      .carousel-slider .swiper-slide {
        max-width: 100%; } }
    .carousel-slider .swiper-slide img {
      transform: scale(0.9); }
  .carousel-slider .swiper-pagination {
    display: none; }

#full-single-image {
  position: relative; }
  #full-single-image .banner-slide-next-btn {
    width: 50px;
    height: 50px;
    line-height: 48px;
    right: 50px;
    background-color: rgba(255, 99, 96, 0.6);
    position: absolute;
    bottom: 30px;
    z-index: 2;
    outline: none;
    color: #fff;
    cursor: pointer;
    text-align: center;
    border-radius: 0 0 10px;
    transition: all 1ms ease-in-out; }
    @media (max-width: 991.98px) {
      #full-single-image .banner-slide-next-btn {
        top: 50%;
        bottom: 50%;
        right: 0; } }
    #full-single-image .banner-slide-next-btn svg path {
      fill: #ffffff; }
    #full-single-image .banner-slide-next-btn.active {
      background-color: #f05a23;
      transition: all 1ms ease-in; }
  #full-single-image .banner-slide-prev-btn {
    width: 50px;
    height: 50px;
    line-height: 48px;
    right: 100px;
    background-color: rgba(255, 99, 96, 0.6);
    position: absolute;
    bottom: 30px;
    z-index: 2;
    outline: none;
    color: #fff;
    cursor: pointer;
    text-align: center;
    border-radius: 10px 0 0;
    transition: all 1ms ease-in-out; }
    @media (max-width: 991.98px) {
      #full-single-image .banner-slide-prev-btn {
        top: 50%;
        bottom: 50%;
        left: 0; } }
    #full-single-image .banner-slide-prev-btn svg path {
      fill: #ffffff; }
    #full-single-image .banner-slide-prev-btn.active {
      background-color: #f05a23;
      transition: all 1ms ease-in; }

#highligted-project {
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  z-index: 9; }
  #highligted-project::after {
    content: '';
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.712); }
  #highligted-project .full-imgage {
    margin-bottom: 40px; }
    #highligted-project .full-imgage img {
      width: 100%;
      object-fit: cover; }
  #highligted-project .content h3, #highligted-project .content .h3 {
    font-size: 40px;
    line-height: 60px;
    color: #ffffff;
    margin-bottom: 40px; }
    @media (max-width: 1199.98px) {
      #highligted-project .content h3, #highligted-project .content .h3 {
        font-size: 32px;
        line-height: 48px; } }
  #highligted-project .content p {
    color: #ffffff;
    font-size: 16px;
    line-height: 26px; }

.residential-project {
  background-color: #fafafa; }
  @media (max-width: 1199.98px) {
    .residential-project.spacing-top-none .mySwiper {
      padding-top: 0; } }
  @media (max-width: 1199.98px) {
    .residential-project.spacing-top-none .mySwiper .custom-row {
      padding-top: 0; } }
  .residential-project h2, .residential-project .h2 {
    font-weight: 400;
    font-size: 40px;
    line-height: 60px; }
    @media (max-width: 1199.98px) {
      .residential-project h2, .residential-project .h2 {
        font-size: 32px;
        line-height: 48px; } }
  .residential-project p {
    font-size: 16px;
    line-height: 26px; }
  .residential-project .navigation {
    display: flex;
    justify-content: flex-start;
    position: relative;
    bottom: -50px;
    right: 0;
    float: right;
    z-index: 9; }
    @media (min-width: 1300px) {
      .residential-project .navigation {
        justify-content: flex-end;
        position: absolute;
        bottom: -70px;
        right: 40px; } }
    @media (min-width: 1500px) {
      .residential-project .navigation {
        bottom: 0px; } }
    .residential-project .navigation a.prev {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 99, 96, 0.6);
      border-radius: 5px 0 0;
      transition: all 0.25s ease-in; }
      .residential-project .navigation a.prev svg path {
        fill: #f05a23; }
      .residential-project .navigation a.prev.active {
        background-color: #f05a23;
        transition: all 0.25s ease-in-out; }
        .residential-project .navigation a.prev.active svg path {
          fill: #000; }
    .residential-project .navigation a.next {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 99, 96, 0.6);
      border-radius: 0 0 5px;
      transition: all 0.25s ease-in-out; }
      .residential-project .navigation a.next svg path {
        fill: #f05a23; }
      .residential-project .navigation a.next.active {
        background-color: #f05a23;
        transition: all 0.25s ease-in-out; }
        .residential-project .navigation a.next.active svg path {
          fill: #000; }
  .residential-project .mySwiper {
    padding-top: 30px; }
  @media (max-width: 1199.98px) {
    .residential-project .custom-row {
      padding: 15px; } }
  .residential-project .custom-row .project {
    display: flex;
    flex-wrap: wrap;
    align-items: center; }
    .residential-project .custom-row .project .left-part {
      flex: 50%;
      max-width: 50%; }
      @media (max-width: 1199.98px) {
        .residential-project .custom-row .project .left-part {
          flex: 100%;
          max-width: 100%;
          order: 2; } }
      .residential-project .custom-row .project .left-part .content {
        position: relative;
        width: 470px; }
        @media (max-width: 1199.98px) {
          .residential-project .custom-row .project .left-part .content {
            width: 100%; } }
        .residential-project .custom-row .project .left-part .content h3, .residential-project .custom-row .project .left-part .content .h3 {
          font-size: 32px;
          line-height: 48px;
          margin-bottom: 40px; }
          @media (min-width: 1200px) {
            .residential-project .custom-row .project .left-part .content h3, .residential-project .custom-row .project .left-part .content .h3 {
              font-size: 40px;
              line-height: 60px; } }
        .residential-project .custom-row .project .left-part .content .icon-row {
          padding-top: 30px;
          display: flex;
          flex-wrap: wrap; }
          .residential-project .custom-row .project .left-part .content .icon-row .icon-item {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 30px;
            width: 50%; }
            .residential-project .custom-row .project .left-part .content .icon-row .icon-item .icon {
              width: 60px; }
              @media (max-width: 575.98px) {
                .residential-project .custom-row .project .left-part .content .icon-row .icon-item .icon {
                  width: 50px; } }
              .residential-project .custom-row .project .left-part .content .icon-row .icon-item .icon img {
                position: relative;
                top: 5px; }
            .residential-project .custom-row .project .left-part .content .icon-row .icon-item .icon-content h4, .residential-project .custom-row .project .left-part .content .icon-row .icon-item .icon-content .h4 {
              margin-bottom: 5px;
              font-weight: 600;
              font-size: 20px;
              line-height: 26px; }
              @media (max-width: 575.98px) {
                .residential-project .custom-row .project .left-part .content .icon-row .icon-item .icon-content h4, .residential-project .custom-row .project .left-part .content .icon-row .icon-item .icon-content .h4 {
                  font-size: 18px; } }
            .residential-project .custom-row .project .left-part .content .icon-row .icon-item .icon-content span {
              font-weight: 400;
              font-size: 14px;
              line-height: 24px;
              color: #4e4e4e; }
              @media (max-width: 575.98px) {
                .residential-project .custom-row .project .left-part .content .icon-row .icon-item .icon-content span {
                  font-size: 12px; } }
    .residential-project .custom-row .project .right-part {
      flex: 50%;
      max-width: 50%; }
      @media (max-width: 1199.98px) {
        .residential-project .custom-row .project .right-part {
          flex: 100%;
          max-width: 100%;
          order: 1; } }
      @media (max-width: 767.98px) {
        .residential-project .custom-row .project .right-part {
          padding-top: 30px; } }
      .residential-project .custom-row .project .right-part img {
        height: auto;
        width: 100%;
        object-fit: cover;
        margin-bottom: 30px;
        height: 230px; }
        @media (min-width: 1300px) {
          .residential-project .custom-row .project .right-part img {
            height: 400px;
            margin-bottom: 0; } }
        @media (min-width: 1500px) {
          .residential-project .custom-row .project .right-part img {
            height: 650px; } }
  .residential-project.without-items .navigation {
    bottom: 0; }
    @media (max-width: 575.98px) {
      .residential-project.without-items .navigation {
        bottom: -30px; } }
  .residential-project.order .project .left-part {
    order: 2; }
  .residential-project.order .project .right-part {
    order: 1; }

#career-opportunties .section-page-header {
  margin-bottom: 60px; }

#career-opportunties .search-bar {
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  border-radius: 5px;
  padding: 10px 30px;
  margin-bottom: 30px; }
  @media (max-width: 1199.98px) {
    #career-opportunties .search-bar {
      padding: 10px; } }
  #career-opportunties .search-bar .categories {
    width: 200px;
    height: 100%;
    position: relative; }
    @media (max-width: 1199.98px) {
      #career-opportunties .search-bar .categories {
        width: 150px; } }
    #career-opportunties .search-bar .categories select {
      width: 130px;
      border: none;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #4e4e4e;
      outline: none; }
    #career-opportunties .search-bar .categories::after {
      content: '';
      position: absolute;
      height: 1px;
      top: 10px;
      right: -15px;
      display: inline-block;
      width: 61px;
      transform: rotate(90deg);
      background-color: #dddfe3; }
  #career-opportunties .search-bar .search-job {
    width: calc(100% - 200px);
    position: relative; }
    @media (max-width: 1199.98px) {
      #career-opportunties .search-bar .search-job {
        width: calc(100% - 150px); } }
    #career-opportunties .search-bar .search-job input {
      height: 40px;
      width: 100%;
      border: none;
      outline: none;
      padding-left: 30px;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #4e4e4e; }
    #career-opportunties .search-bar .search-job::after {
      content: '';
      position: absolute;
      background-image: url("data:image/svg+xml, %3Csvg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z\" stroke=\"%234E4E4E\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/%3E%3Cpath d=\"M20.9999 21L16.6499 16.65\" stroke=\"%234E4E4E\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      top: 8px;
      left: 0;
      width: 30px;
      height: 30px; }

#career-opportunties .job-post .job-item {
  border: 1px solid #dddfe3;
  box-sizing: border-box;
  border-radius: 10px 0px 0px 0px;
  padding: 20px;
  margin-bottom: 20px; }
  #career-opportunties .job-post .job-item .navigate {
    text-align: right; }
    #career-opportunties .job-post .job-item .navigate a {
      background-color: #f05a23;
      border-radius: 0px 0px 10px 0px;
      width: 50px;
      height: 50px;
      display: inline-block;
      line-height: 48px;
      text-align: center; }
      #career-opportunties .job-post .job-item .navigate a:hover {
        background-color: rgba(255, 99, 96, 0.6); }
  #career-opportunties .job-post .job-item h3 a, #career-opportunties .job-post .job-item .h3 a {
    font-weight: 400;
    font-size: 26px;
    line-height: 38px;
    color: #4e4e4e;
    margin-bottom: 20px; }
  @media (max-width: 1199.98px) {
    #career-opportunties .job-post .job-item .group-item {
      justify-content: flex-start; } }
  #career-opportunties .job-post .job-item .group-item .item {
    border: 1px solid #dddfe3;
    box-sizing: border-box;
    border-radius: 5px 0px 0px 0px;
    background: #fafafa;
    padding: 10px;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: #4e4e4e;
    display: flex;
    align-items: center;
    margin-right: 15px; }
    @media (max-width: 1199.98px) {
      #career-opportunties .job-post .job-item .group-item .item {
        margin-bottom: 15px; } }
    #career-opportunties .job-post .job-item .group-item .item svg {
      margin-right: 5px; }

#drop-resume {
  background-color: #fef7f4;
  padding: 80px 0 80px; }
  #drop-resume h3, #drop-resume .h3 {
    font-weight: 400;
    font-size: 36px;
    line-height: 46px;
    margin-bottom: 30px; }
  #drop-resume .primay-button {
    background-color: #f05a23;
    border-radius: 5px 0px 0px 0px;
    width: 245px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 13px 50px;
    text-transform: uppercase;
    color: #000000;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    transition: all 1ms ease-in-out; }
    @media (min-width: 1200px) {
      #drop-resume .primay-button {
        float: right; } }
    #drop-resume .primay-button:hover {
      transition: all 1ms ease-in;
      background-color: #424143;
      color: #ffffff; }

#application-form {
  background-color: #fafafa; }
  #application-form .page-header {
    max-width: 530px;
    margin: 0 auto;
    text-align: center;
    margin-bottom: 60px; }
    #application-form .page-header h2, #application-form .page-header .h2 {
      font-size: 36px;
      line-height: 48px;
      color: #000000;
      margin-bottom: 20px; }
      @media (min-width: 1200px) {
        #application-form .page-header h2, #application-form .page-header .h2 {
          font-size: 40px;
          line-height: 60px; } }
      #application-form .page-header h2 p, #application-form .page-header .h2 p {
        font-size: 16px;
        line-height: 26px;
        color: #4e4e4e; }
  #application-form form {
    /* For IE10 */ }
    #application-form form input,
    #application-form form select {
      background: #ffffff;
      border: 1px solid rgba(0, 0, 0, 0.1);
      box-sizing: border-box;
      border-radius: 4px;
      width: 100%;
      height: 50px;
      margin-bottom: 30px;
      padding: 10px;
      color: #4e4e4e; }
    #application-form form .fake-file-input {
      border: 1px dashed #f05a23;
      box-sizing: border-box;
      border-radius: 5px;
      height: 178px;
      position: relative; }
      #application-form form .fake-file-input .fake-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: absolute;
        height: 100%; }
        #application-form form .fake-file-input .fake-content h3, #application-form form .fake-file-input .fake-content .h3 {
          font-weight: 400;
          font-size: 20px;
          line-height: 32px;
          text-align: center; }
      #application-form form .fake-file-input input {
        height: 178px;
        opacity: 0;
        cursor: pointer; }
    #application-form form #submit {
      margin-top: 30px;
      width: 254px;
      height: 50px;
      background: #f05a23;
      border-radius: 5px 0px 0px 0px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 13px 50px;
      text-transform: uppercase;
      font-size: 16px;
      line-height: 24px;
      font-weight: bold;
      transition: all 1ms ease-in-out; }
      #application-form form #submit:hover {
        background-color: #424143;
        color: #ffffff;
        transition: all 1ms ease-in; }
    #application-form form #verify-email {
      background-color: #fff;
      padding: 20px; }
      #application-form form #verify-email span {
        color: #4e4e4e;
        font-weight: 400;
        font-size: 16px;
        line-height: 26px;
        margin-bottom: 30px; }
      #application-form form #verify-email .form-row {
        display: flex;
        margin-top: 20px; }
        @media (max-width: 991.98px) {
          #application-form form #verify-email .form-row {
            flex-direction: column; } }
      @media (max-width: 1199.98px) {
        #application-form form #verify-email .verify-email {
          margin-bottom: 20px; } }
      #application-form form #verify-email .verify-email input {
        margin-bottom: 0; }
      @media (max-width: 991.98px) {
        #application-form form #verify-email .col-lg-2 {
          display: flex;
          justify-content: center; } }
    #application-form form #applicate-form-resume h3, #application-form form #applicate-form-resume .h3 {
      font-weight: 400;
      font-size: 26px;
      line-height: 38px;
      color: #000000;
      margin-bottom: 30px; }
    #application-form form #applicate-form-resume.disible input,
    #application-form form #applicate-form-resume.disible select {
      color: rgba(78, 78, 78, 0.5); }
    #application-form form #applicate-form-resume.disible .fake-file-input {
      border: 1px dashed #c0c0c0; }
      #application-form form #applicate-form-resume.disible .fake-file-input .fake-content svg path {
        fill: rgba(0, 0, 0, 0.5); }
      #application-form form #applicate-form-resume.disible .fake-file-input .fake-content h3, #application-form form #applicate-form-resume.disible .fake-file-input .fake-content .h3 {
        color: rgba(0, 0, 0, 0.5); }
    #application-form form #applicate-form-resume.disible #submit {
      background-color: #dddfe3;
      color: #424143; }
    #application-form form .arrow {
      position: relative; }
      #application-form form .arrow::after {
        position: absolute;
        content: '';
        background-image: url("data:image/svg+xml, %3Csvg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.0773 6.07739L17.2559 7.2559L9.99994 14.5118L2.74402 7.2559L3.92253 6.07739L9.99994 12.1548L16.0773 6.07739Z\" fill=\"%234E4E4E\"/%3E%3C/svg%3E");
        right: 0;
        width: 50px;
        height: 50px;
        background-repeat: no-repeat;
        top: 15px; }
    #application-form form input[type='submit'] {
      color: #000000; }
    #application-form form select {
      /* for Firefox */
      -moz-appearance: none;
      /* for Chrome */
      -webkit-appearance: none; }
    #application-form form select::-ms-expand {
      display: none; }

#application-form form #applicate-form-resume.disible input,
#application-form form #applicate-form-resume.disible select {
  color: #7b7b7b; }

#message-from-ceo {
  margin-top: 60px;
  margin-bottom: 60px;
  min-height: var(--app-height); }
  @media (min-width: 1300px) {
    #message-from-ceo {
      margin-top: 80px;
      margin-bottom: 80px;
      min-height: 100%; } }
  @media (min-width: 1500px) {
    #message-from-ceo {
      height: 100%; } }
  #message-from-ceo .message-container {
    background-color: #f7f7f7;
    border-radius: 20px 0 0 0;
    overflow: hidden; }
    #message-from-ceo .message-container .content {
      margin: auto; }
      #message-from-ceo .message-container .content .message {
        padding: 15px 15px 20px 15px; }
        @media (min-width: 1300px) {
          #message-from-ceo .message-container .content .message {
            padding: 40px 40px 40px 0; } }
      #message-from-ceo .message-container .content h2, #message-from-ceo .message-container .content .h2 {
        text-align: left;
        font-size: 28px;
        line-height: 48px;
        margin-bottom: 10px; }
        @media (min-width: 1300px) {
          #message-from-ceo .message-container .content h2, #message-from-ceo .message-container .content .h2 {
            margin-bottom: 15px;
            font-size: 40px;
            line-height: 60px; } }
        @media (min-width: 1500px) {
          #message-from-ceo .message-container .content h2, #message-from-ceo .message-container .content .h2 {
            margin-bottom: 15px;
            font-size: 40px;
            line-height: 60px; } }
      #message-from-ceo .message-container .content p {
        font-size: 16px;
        line-height: 26px;
        color: #000000; }
        @media (max-width: 1199.98px) {
          #message-from-ceo .message-container .content p {
            text-align: left; } }
        #message-from-ceo .message-container .content p:last-child {
          margin-bottom: 0; }
      @media (max-width: 1199.98px) {
        #message-from-ceo .message-container .content .content-title {
          padding-top: 5px; } }
      #message-from-ceo .message-container .content .name {
        font-weight: 400;
        font-size: 20px;
        line-height: 32px;
        margin-bottom: 0px;
        position: relative; }
        #message-from-ceo .message-container .content .name::before {
          content: '';
          width: 50px;
          background-color: #c0c0c0;
          height: 1px;
          position: absolute;
          top: -5px;
          left: 0px; }
      #message-from-ceo .message-container .content span {
        font-size: 16px;
        line-height: 26px;
        color: #000000;
        display: block;
        text-transform: uppercase; }
      #message-from-ceo .message-container .content img {
        object-fit: fill;
        width: 100%;
        height: 100%; }
        @media (min-width: 1300px) {
          #message-from-ceo .message-container .content img {
            height: 100%;
            max-width: 470px; } }
  #message-from-ceo .content-bio {
    height: 210px;
    overflow: auto;
    padding-right: 20px;
    margin-bottom: 20px;
    /* Track */ }
    @media (max-width: 1199.98px) {
      #message-from-ceo .content-bio {
        height: calc(var(--app-height) - 460px);
        padding-right: 10px; } }
    #message-from-ceo .content-bio::-webkit-scrollbar {
      width: 5px; }
    #message-from-ceo .content-bio::-webkit-scrollbar-track {
      background: #f1f1f1; }
    #message-from-ceo .content-bio::-webkit-scrollbar-thumb {
      background: #888; }
    #message-from-ceo .content-bio::-webkit-scrollbar-thumb:hover {
      background: #555; }
    #message-from-ceo .content-bio ul {
      list-style: none;
      padding: 0 0 30px;
      margin-bottom: 0px; }
      #message-from-ceo .content-bio ul li {
        font-size: 16px;
        line-height: 26px;
        color: #4e4e4e;
        padding: 0;
        display: flex; }
        #message-from-ceo .content-bio ul li::before {
          content: '\2022';
          color: #f05a23;
          display: inline-block;
          font-size: 22px;
          margin-right: 10px; }

.supplier {
  background-color: #fef7f4; }
  .supplier h2, .supplier .h2 {
    font-size: 32px;
    line-height: 48px;
    margin-bottom: 50px; }
    @media (min-width: 1200px) {
      .supplier h2, .supplier .h2 {
        font-size: 40px;
        line-height: 60px; } }
  .supplier .fuc-kit-heading {
    max-width: 960px;
    display: flex;
    flex-wrap: wrap; }
    .supplier .fuc-kit-heading h4, .supplier .fuc-kit-heading .h4 {
      font-weight: 400;
      font-size: 32px;
      line-height: 46px; }
    .supplier .fuc-kit-heading .left {
      width: 400px; }
      @media (max-width: 575.98px) {
        .supplier .fuc-kit-heading .left {
          width: 100%; } }
    .supplier .fuc-kit-heading .right {
      width: 400px; }
      @media (max-width: 575.98px) {
        .supplier .fuc-kit-heading .right {
          width: 100%; } }
  .supplier .fun-kit {
    max-width: 960px;
    display: flex;
    flex-wrap: wrap;
    padding-top: 20px;
    padding-bottom: 20px; }
    .supplier .fun-kit .left {
      width: 400px; }
      .supplier .fun-kit .left h3, .supplier .fun-kit .left .h3 {
        font-weight: 400;
        font-size: 32px;
        line-height: 46px;
        margin-bottom: 40px; }
      .supplier .fun-kit .left h4, .supplier .fun-kit .left .h4 {
        font-weight: 400;
        font-size: 28px;
        line-height: 46px;
        margin-bottom: 40px; }
    .supplier .fun-kit .right {
      width: 400px; }
      .supplier .fun-kit .right h3, .supplier .fun-kit .right .h3 {
        font-weight: 400;
        font-size: 32px;
        line-height: 46px;
        margin-bottom: 40px; }
      .supplier .fun-kit .right .list-row {
        padding-top: 10px; }
        .supplier .fun-kit .right .list-row .list {
          margin-bottom: 20px;
          display: flex;
          align-items: center; }
          .supplier .fun-kit .right .list-row .list .icon {
            width: 40px; }
          .supplier .fun-kit .right .list-row .list .icon-content {
            width: calc(100% - 40px); }
            .supplier .fun-kit .right .list-row .list .icon-content span {
              font-weight: 400;
              font-size: 20px;
              line-height: 32px;
              color: #000000; }

.list-group .page-header {
  margin-bottom: 60px; }

.list-group .list {
  display: flex;
  padding-bottom: 20px; }
  @media (max-width: 1199.98px) {
    .list-group .list {
      flex-wrap: nowrap;
      align-content: center; } }
  .list-group .list .icon {
    margin-right: 10px;
    width: 40px; }
  .list-group .list .content {
    width: calc(100% - 40px); }
    .list-group .list .content span {
      font-weight: 400;
      font-size: 20px;
      line-height: 32px;
      color: #000000; }

.ug__career {
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: 60px; }
  .ug__career_image {
    width: 100%;
    height: 400px;
    border-radius: 20px 0px 0px 0px;
    overflow: hidden; }
    .ug__career_image img {
      width: 100%;
      height: 100%;
      object-fit: cover; }
  .ug__career_content {
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 20px; }
    .ug__career_content * {
      margin-bottom: 0; }
    .ug__career_content_title {
      font-family: Barlow;
      font-size: 26px;
      font-weight: 500;
      line-height: 38px;
      text-align: left; }
    .ug__career_content_details {
      font-family: Barlow;
      font-size: 16px;
      font-weight: 400;
      line-height: 26px;
      text-align: left;
      color: #4E4E4E; }

@media screen and (max-width: 560px) {
  .ug__career {
    grid-gap: 20px; }
    .ug__career_image {
      height: 200px; } }

.site-footer {
  background-color: #2b2b2b; }
  .site-footer a.footer-logo {
    padding-bottom: 60px;
    display: block; }
    @media (max-width: 1199.98px) {
      .site-footer a.footer-logo {
        display: flex;
        align-items: center;
        justify-content: center; } }
  .site-footer span.copyright {
    display: block;
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    color: #ffffff;
    text-transform: uppercase;
    opacity: 0.6;
    margin-bottom: 15px; }
    @media (max-width: 1199.98px) {
      .site-footer span.copyright {
        text-align: center; } }
  .site-footer ul.footer-menu-bottom {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0; }
    @media (max-width: 1199.98px) {
      .site-footer ul.footer-menu-bottom {
        justify-content: center;
        margin: 0 0 30px 0; } }
    .site-footer ul.footer-menu-bottom li a {
      border-right: 1px solid;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      opacity: 0.6;
      line-height: 14px;
      margin-right: 10px;
      padding-right: 10px;
      transition: all 0.25s ease-in-out; }
      .site-footer ul.footer-menu-bottom li a:hover {
        transition: all 0.25s ease-in;
        color: #f05a23; }
    .site-footer ul.footer-menu-bottom li:last-child a {
      border: none; }
  .site-footer ul.footer-menu {
    margin: 0;
    padding: 0px;
    list-style: none; }
    @media (min-width: 992px) {
      .site-footer ul.footer-menu {
        padding: 0 0 0 50px; } }
    .site-footer ul.footer-menu li {
      padding-bottom: 30px; }
      .site-footer ul.footer-menu li a {
        font-size: 16px;
        line-height: 26px;
        color: #ffffff;
        transition: all 0.25s ease-in-out; }
        .site-footer ul.footer-menu li a svg path {
          fill: #ffffff; }
        .site-footer ul.footer-menu li a:hover {
          transition: all 0.25s ease-in;
          color: #f05a23; }
          .site-footer ul.footer-menu li a:hover svg path {
            fill: #f05a23; }
  @media (max-width: 1199.98px) {
    .site-footer .footer-social ul {
      justify-content: center;
      display: flex;
      flex-wrap: wrap; }
      .site-footer .footer-social ul li {
        margin-right: 20px; }
    .site-footer .footer-item-2 {
      display: flex;
      width: 50%;
      text-align: left; } }
  @media (max-width: 1199.98px) and (max-width: 1199.98px) {
    .site-footer .footer-item-2 {
      display: inline-block;
      text-align: center; } }

