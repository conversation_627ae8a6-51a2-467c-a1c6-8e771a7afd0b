{"name": "united-group", "version": "1.0.0", "description": "[![Build Status](https://travis-ci.org/Automattic/_s.svg?branch=master)](https://travis-ci.org/Automattic/_s)", "main": "webpack.config.js", "scripts": {"build": "webpack", "start": "webpack-dev-server", "watch": "webpack --watch --progress"}, "engines": {"node": "14.x"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/boomerangdigitaldevelopers/united-group.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://bitbucket.org/boomerangdigitaldevelopers/united-group/issues"}, "homepage": "https://bitbucket.org/boomerangdigitaldevelopers/united-group#readme", "dependencies": {"@babel/core": "^7.20.5", "animate.css": "^4.1.1", "babel-core": "^6.26.3", "babel-loader": "^8.2.4", "babel-preset-env": "^1.7.0", "bootstrap": "^5.1.3", "css-loader": "^6.7.1", "file-loader": "^6.2.0", "headroom.js": "^0.12.0", "html-loader": "^3.1.0", "html-webpack-plugin": "^5.5.0", "jquery-parallax.js": "^1.5.0", "mini-css-extract-plugin": "^2.6.0", "node-sass": "^7.0.1", "sass-loader": "^12.6.0", "swiper": "^8.0.7", "webpack": "^5.71.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.8.1"}}