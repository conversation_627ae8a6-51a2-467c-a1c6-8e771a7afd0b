<?php

/**
 * Carbon filed theme settings options
 * 
 */

// carbon_get_post_meta( $id, $name )
// carbon_get_theme_option("key")
use Carbon_Fields\Container;
use Carbon_Fields\Field;
use Carbon_Fields\Block;

add_action('carbon_fields_register_fields', 'united_theme_option_register');


function united_theme_option_register()
{
    $prefix = 'united_';
    $basic_options_container = Container::make('theme_options', __('Theme Options', 'united'))
        ->add_fields(array(
            Field::make('header_scripts',  $prefix . 'header_script', __('Header Script', 'united')),
            Field::make('footer_scripts',  $prefix . 'footer_script', __('Footer Script', 'united')),
            Field::make('image',  $prefix . 'main_og', __('OG Image', 'united'))->set_help_text('Dimensions 1200x630 px, file type: jpg. OG image is the thumbnail which appears on social media share posts.'),

        ));



    Container::make('theme_options', "Footer settings")
        ->set_page_parent($basic_options_container) // reference to a top level container
        ->add_tab(
            __('Useful Links (Part 1)', 'united'),
            array(
                Field::make('complex', 'useful_links_1', 'Menu')
                    ->set_layout('tabbed-horizontal')
                    ->add_fields(array(
                        Field::make('text', 'menu_title',  'Menu Title'),
                        Field::make('text', 'menu_url',  'Menu URL'),

                    )),
            )
        )
        ->add_tab(
            __('Useful Links (Part 2)', 'united'),
            array(
                Field::make('complex', 'useful_links_2', 'Menu')
                    ->set_layout('tabbed-horizontal')
                    ->add_fields(array(
                        Field::make('text', 'menu_title',  'Menu Title'),
                        Field::make('text', 'menu_url',  'Menu URL'),
                    )),
            )
        )
        ->add_tab(
            __('Soical Icons', 'united'),
            array(
                Field::make('complex', 'social_link', 'Soical Icon')
                    ->set_layout('tabbed-horizontal')
                    ->add_fields(array(
                        Field::make('image', 'icon_image', 'Image'),
                        Field::make('textarea', 'icon_svg', 'Icon SVG'),
                        Field::make('text', 'icon_url',  'URL'),

                    )),
            )
        )
        ->add_tab(
            __('Footer Bottom', 'united'),
            array(
                Field::make('text', 'copyright',  'Copyright'),
                Field::make('complex', 'footer_bottom_link', 'Footer Menu')
                    ->set_layout('tabbed-horizontal')
                    ->add_fields(array(
                        Field::make('text', 'name',  'Menu Name'),
                        Field::make('text', 'url',  'Menu Link'),

                    )),
            )
        );

    Container::make('theme_options', "General settings")
        ->set_page_parent($basic_options_container) // reference to a top level container
        ->add_fields(array(
            Field::make('text', $prefix . 'drop_resume_email', "To Drop Resume Email"),
            Field::make('text', $prefix . 'privacy_policy', "Privacy Policy Link"),
        ));
}
