<?php
add_action('wp_ajax_get_cateogories_gallery', 'get_cateogories_gallery');
add_action('wp_ajax_nopriv_get_cateogories_gallery', 'get_cateogories_gallery');

function get_cateogories_gallery()
{
    $slug = $_POST['slug'];
    if (isset($slug) && !empty($slug)) {
        $args = array(
            'post_type' => 'gallery',
            'tax_query' => array(
                array(
                    'taxonomy' => 'photos_categories',
                    'field'    => 'slug',
                    'terms'    => $slug,
                ),
            ),
        );
        $the_query = new WP_Query($args);

        ?>
        <div class="row">
            <?php while ($the_query->have_posts()) : $the_query->the_post(); ?>
                <?php get_template_part('template-parts/content', 'gallery-item') ?>
            <?php endwhile; ?>

        </div>

<?php
    }
    wp_die();
}
