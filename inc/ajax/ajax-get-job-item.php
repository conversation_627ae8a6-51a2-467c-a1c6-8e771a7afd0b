<?php
add_action('wp_ajax_get_job_item', 'get_job_item');
add_action('wp_ajax_nopriv_get_job_item', 'get_job_item');

function get_job_item()
{
    if (isset($_POST['slug']) && !empty($_POST['slug']) || isset($_POST['keyword']) && !empty($_POST['keyword'])) {

        $args = array(
            'post_type'     => 'career',
            'post_status'   => 'publish',
            'orderby' => 'name',
            'order' => 'ASC',
            's' => $_POST['keyword'],
        );

        if ($_POST['slug'] !== 'all') {
            $args2 = array(
                'tax_query' => array(
                    array(
                        'taxonomy' => 'career-categories',
                        'field'    => 'slug',
                        'terms'    => $_POST['slug'],
                    ),
                ),
            );
        }

        $args = wp_parse_args($args, $args2);

        $the_query  = new WP_Query($args);
        if ($the_query->have_posts()) :
            while ($the_query->have_posts()) : $the_query->the_post();
                get_template_part('template-parts/loop/content', 'job-item');
            endwhile;
        else :
            _e('<p>Sorry, no posts matched your criteria.</p>');
        endif;
    }
    wp_die();
}
