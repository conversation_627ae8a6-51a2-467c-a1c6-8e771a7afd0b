<?php
add_action('wp_ajax_gallery_item', 'gallery_item');
add_action('wp_ajax_nopriv_gallery_item', 'gallery_item');

function gallery_item()
{
    $id = intval($_POST['id']);

    if (isset($id) && !empty($id)) {
        $media = carbon_get_post_meta($id, 'gallery-item-tab-list-conditional');
    }

    foreach ($media as $item) :
        $image = wp_get_attachment_image_src($item['image'], 'full');
        ?>
        <a href="<?php echo $image[0] ?>" data-sub-html="<?php echo '<h3>' . $item['caption_title'] . '</h3> <p>' . $item['caption_discription'] . '</p>'; ?>">
            <img src="<?php echo $image[0] ?>" />
        </a>
    <?php endforeach; ?>

<?php
    wp_die();
}
