<?php
add_action('wp_ajax_get_slider_body', 'get_slider_body');
add_action('wp_ajax_nopriv_get_slider_body', 'get_slider_body');

function get_slider_body()
{
    if (isset($_POST['id']) && !empty($_POST['id'])) {
        $id = intval($_POST['id']);
        $args = array(
            'post_type' => 'about_archive',
            'tax_query' => array(
                array(
                    'taxonomy' => 'about_year_archive_type',
                    'field'    => 'slug',
                    'terms'    => $id,
                ),
            ),
        );
        $the_query = new WP_Query($args);


        if ($the_query->have_posts()) :
            while ($the_query->have_posts()) : $the_query->the_post();
                ob_start();
                get_template_part('template-parts/content', 'archive-slider');
            endwhile;
        endif;
    }
    die();
}
