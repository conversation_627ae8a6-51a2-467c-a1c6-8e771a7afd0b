<?php

use Carbon_Fields\Container;
use Carbon_Fields\Field;
use Carbon_Fields\Block;

add_action('carbon_fields_register_fields', 'united_register_gutenberg_component');
function united_register_gutenberg_component()
{
    Block::make(__('Hero', 'united'))
        ->add_fields(array(
            Field::make('checkbox', 'half_mobile_height', 'Half Mobile Height?'),
            Field::make('complex', 'hero-item-tab-conditional', 'Hero Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                    Field::make('image', 'mobile_image', 'Mobile Image'),
                    Field::make('text', 'name', 'Name'),
                    Field::make('text', 'link', 'Link'),

                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Hero', 'united')])
        ->set_description(__('Hero', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/home/<USER>');
        });

    Block::make(__('Home About', 'united'))
        ->add_fields(array(
            Field::make('text', 'about_heading', 'Heading'),
            Field::make('textarea', 'about_short_text', 'Short Text'),
            Field::make('text', 'learn_more', 'Learn More Link'),
            Field::make('complex', 'follow-us-item-tab-conditional', 'Icon List Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('textarea', 'icon', 'Icon (SVG)'),
                    Field::make('text', 'link', 'Link'),

                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Home About', 'united')])
        ->set_description(__('Home About', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/home/<USER>');
        });

    Block::make(__('Home Concerns', 'united'))
        ->add_fields(array(
            Field::make('complex', 'grid-column-tab-conditional', 'Grid Column')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('complex', 'grid-thumb-tab-conditional', 'Grid Thumb')
                        ->set_layout('tabbed-vertical')
                        ->add_fields(array(
                            Field::make('image', 'grid_image', 'Grid Image'),
                            Field::make('text', 'grid_title', 'Grid Title'),
                            Field::make('text', 'grid_title_url', 'Grid Title Url'),
                        ))

                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Home Grid', 'united')])
        ->set_description(__('Home Grid', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/home/<USER>');
        });

    Block::make(__('Home Video', 'united'))
        ->add_fields(array(
            Field::make('image', 'videos_banner_image', 'Image'),
            Field::make('text', 'videos_banner_heading', 'Heading'),
            Field::make('textarea', 'videos_link', 'Video Link'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Home Video', 'united')])
        ->set_description(__('Home Video', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/home/<USER>');
        });

    Block::make(__('CSR', 'united'))
        ->add_fields(array(
            Field::make('text', 'csr_heading', 'Heading'),
            Field::make('text', 'csr_short_details', 'Details'),
            Field::make('checkbox', 'padding_bottom', 'Padding Bottom None'),
            Field::make('image', 'bg_image', 'BG Image')->set_value_type('url'),
            Field::make('complex', 'csr-item-tab-conditional', 'CSR Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                    Field::make('text', 'number', 'Number'),
                    Field::make('text', 'prefix', 'Prefix'),
                    Field::make('text', 'short_text', 'Short Text'),
                    Field::make('checkbox', 'heading_short_text', 'Want to short text would be heading?'),
                    Field::make('checkbox', 'animated_countdown', 'Animated Coundown?'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('CSR', 'united')])
        ->set_description(__('CSR', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/home/<USER>');
        });

    Block::make(__('Operation', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('complex', 'operation-item-tab-conditional', 'Operation Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('select', 'selected_ids', __('Select News'))
                        ->add_options(get_news_post_arr('post', 'news')),
                    Field::make('image', 'image', 'Image'),
                    Field::make('text', 'short_text', 'Short Text'),

                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Operation', 'united')])
        ->set_description(__('Operation', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/home/<USER>');
        });



    Block::make(__('Life at United', 'united'))
        ->add_fields(array(
            Field::make('complex', 'life-item-tab-conditional', 'Life Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                    Field::make('text', 'heading', 'Heading'),
                    Field::make('text', 'date', 'Post Date'),
                    Field::make('text', 'short_text', 'Short Text'),
                    Field::make('text', 'btn_url', 'Button Url'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Life at United', 'united')])
        ->set_description(__('Life at United', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/home/<USER>');
        });

    Block::make(__('Fun Fact', 'united'))
        ->add_fields(array(
            Field::make('checkbox', 'space_around', 'Around Space?'),
            Field::make('complex', 'fun-fact-item-tab-conditional', 'Fun Fact Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                    Field::make('text', 'heading', 'Heading'),
                    Field::make('text', 'short_text', 'Short Text'),
                )),
            Field::make('rich_text', 'details', 'Details'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Fun Fact', 'united')])
        ->set_description(__('Fun Fact', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/fun-fact');
        });


    Block::make(__('Generic Banner', 'united'))
        ->add_fields(array(
            Field::make('text', 'page_name', 'Page Name'),
            Field::make('text', 'page_heading', 'Page Heading'),
            Field::make('text', 'website_link', 'Website Link'),
            Field::make('rich_text', 'description', 'Discription'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Generic Banner', 'united')])
        ->set_description(__('Generic Banner', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/generic-banner');
        });

    Block::make(__('Image Slider', 'united'))
        ->add_fields(array(
            Field::make('checkbox', 'wider_height', 'Wider Height'),
            Field::make('checkbox', 'show_navigate', 'Show Navigate?'),
            Field::make('checkbox', 'autoplay', 'Autoplay'),
            Field::make('text', 'image_caption', 'Image Caption'),
            Field::make('media_gallery', 'image', __('Media Gallery'))
                ->set_type(array('image', 'video'))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Image Slider', 'united')])
        ->set_description(__('Image Slider', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/image-slider');
        });



    Block::make(__('Image Carousel Slider', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('media_gallery', 'image', __('Media Gallery'))
                ->set_type(array('image')),
            Field::make('complex', 'image-carosel-slider-item-tab-conditional', 'Carousel Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                    Field::make('select', 'selected_ids', __('Select Concerns'))
                        ->add_options(get_post_arr('our-concerns')),
                    Field::make('text', 'optional_link', 'Optional Link'),
                )),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Image Carousel Slider', 'united')])
        ->set_description(__('Image Carousel Slider', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/carousel-slider');
        });

    Block::make(__('Foundation Day Slider', 'united'))
        ->add_fields(array(
            Field::make('complex', 'foundation-day-item-tab-conditional', 'Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                    Field::make('text', 'title', 'Title'),
                    Field::make('text', 'url', 'Url'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('oundation Day Slider', 'united')])
        ->set_description(__('oundation Day Slider', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/foundation-day');
        });
    Block::make(__('Services', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('rich_text', 'description', 'Discription'),
            Field::make('checkbox', 'padding_bottom', 'Padding Bottom None'),
            Field::make('complex', 'service-item-tab-conditional', 'Service Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('text', 'heading', 'Heading'),
                    Field::make('rich_text', 'short_text', 'Short Text'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Services', 'united')])
        ->set_description(__('Services', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/services');
        });

    Block::make(__('Team', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('complex', 'team-member-tab-conditional', 'Service Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'member_image', 'Member Image'),
                    Field::make('image', 'mobile_member_image', 'Mobile Member Image'),
                    Field::make('text', 'name', 'Name'),
                    Field::make('text', 'designation', 'Designation'),
                    Field::make('rich_text', 'discription', 'Discription'),
                    Field::make('text', 'learn_more_link', 'Learn More Page Link'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Services', 'united')])
        ->set_description(__('Services', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/team');
        });




    Block::make(__('Slider Archive', 'united'))
        ->add_fields(array(
            Field::make('text', 'name', 'Name'),
            Field::make('image', 'bg_image', 'BG Image')->set_value_type('url'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Services', 'united')])
        ->set_description(__('Services', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/slider-archive');
        });

    Block::make(__('Awards', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('color', 'section_background', __('Background Color')),
            Field::make('complex', 'awards-tab-conditional', 'Awards Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                    Field::make('text', 'title', 'Title'),
                    Field::make('textarea', 'content', 'Content'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('awards', 'united')])
        ->set_description(__('awards', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/awards');
        });

    Block::make(__('Newsletter', 'united'))
        ->add_fields(array(
            Field::make('image', 'image', 'Image')->set_value_type('url'),
            Field::make('text', 'title', 'Title'),
            Field::make('text', 'short_text', 'Short Text'),
            Field::make('text', 'short_code', 'Shortcode'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Newsletter', 'united')])
        ->set_description(__('Newsletter', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/newsletter');
        });

    Block::make(__('Half to Half', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('checkbox', 'remove_padding_bottom', 'None Padding Bottom?'),
            Field::make('rich_text', 'details', 'Details'),
            Field::make('complex', 'certified-tab-conditional', 'Certified Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Half to Half', 'united')])
        ->set_description(__('Half to Half', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/half-to-half');
        });

    Block::make(__('Contribution', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('checkbox', 'style_change', 'Another Style?'),
            Field::make('checkbox', 'padding_top', 'Padding Top None?'),
            Field::make('checkbox', 'item_flex', 'Flex Item?'),
            Field::make('checkbox', 'padding_bottom', 'Padding Bottom None?'),
            Field::make('color', 'section_background', __('Background Color')),
            Field::make('complex', 'contribution-tab-conditional', 'Contribution Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                    Field::make('text', 'title', 'Title'),
                    Field::make('textarea', 'content', 'Content'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Contribution', 'united')])
        ->set_description(__('Contribution', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/contribution');
        });

    Block::make(__('Asset Map', 'united'))
        ->add_fields(array(
            Field::make('image', 'desktop_large_section_image', 'Desktop Large Section Image')->set_value_type('url'),
            Field::make('image', 'mobile_section_image', 'Mobile Section Image')->set_value_type('url'),
            Field::make('image', 'section_image', 'Section Image')->set_value_type('url'),
            Field::make('image', 'map_image', 'Map Image'),
            Field::make('complex', 'map-list-tab-conditional', 'Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('text', 'title', 'Title'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Asset Map', 'united')])
        ->set_description(__('Asset Map', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/asset-map');
        });


    Block::make(__('Core Busniness', 'united'))
        ->add_tab(__('Core Items'), array(
            Field::make('complex', 'core-item-tab-conditional', 'Core Items (Part 1)')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Icon'),
                    Field::make('text', 'title', 'Title'),
                )),
            Field::make('complex', 'core-company-item-tab-conditional', 'Company Names (Part 1)')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('text', 'title', 'Title'),
                )),

            Field::make('complex', 'core-item2-tab-conditional', 'Core Items (Part 2)')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Border Image'),
                    Field::make('text', 'title', 'Title'),
                )),
            Field::make('complex', 'core-company2-item-tab-conditional', 'Company Names (Part 2)')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('text', 'title', 'Title'),
                ))
        ))
        ->add_tab(__('Core Content'), array(
            Field::make('text', 'core_content_title', 'Title'),
            Field::make('complex', 'core-list-item-tab-conditional', 'List')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('text', 'title', 'Title'),
                ))

        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Asset Map', 'united')])
        ->set_description(__('Asset Map', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/core-busniness');
        });

    Block::make(__('Generic Concerns', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('checkbox', 'add_bg', 'Add Background'),
            Field::make('checkbox', 'change_style', 'Change Style?'),
            Field::make('checkbox', 'single_item', 'Single Items?'),
            Field::make('checkbox', 'hide_title', 'Hide Title?'),
            Field::make('checkbox', 'hide_section', 'Hide Section?'),
            Field::make('checkbox', 'height_auto', 'Height Auto?'),
            Field::make('checkbox', 'blue_bullet', 'Bule Bullet?'),
            Field::make('text', 'col_size', 'Column'),
            Field::make('complex', 'generic-concerns-conditional', 'Generic Concerns')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                    Field::make('text', 'title', 'Title'),
                    Field::make('text', 'website_link', 'Website Link'),
                    Field::make('rich_text', 'concerns_details', 'Details'),
                    Field::make('select', 'selected_ids', __('Select Concerns'))
                        ->add_options(get_post_arr('our-concerns')),

                ))
        ))->set_icon('star-filled')
        ->set_keywords([__('Asset Map', 'united')])
        ->set_description(__('Asset Map', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/concerns');
        });


    Block::make(__('United Editor', 'united'))
        ->add_fields(array(
            Field::make('rich_text', 'editor', 'Editor'),
            Field::make('checkbox', 'padding_bottom', 'Padding Bottom None'),
            Field::make('checkbox', 'padding_top', 'Paddint Top None'),
            Field::make('checkbox', 'half_top_space', 'Half Top Space'),
            Field::make('checkbox', 'full_width', 'Full width?'),
            Field::make('checkbox', 'blue_bullet', 'Bule Bullet?'),

        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Services', 'united')])
        ->set_description(__('Services', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/united-editor');
        });


    Block::make(__('United City', 'united'))
        ->add_fields(array(
            Field::make('text', 'page_heading', 'Page Heading'),
            Field::make('image', 'image', 'Image'),
            Field::make('checkbox', 'remove_padding_bottom', 'None Padding Bottom?'),
            Field::make('rich_text', 'left_editor', 'Left Editor'),
            Field::make('rich_text', 'right_editor', 'Right Editor'),
            Field::make('checkbox', 'use_full_paragraph', 'Full Width Text?'),
            Field::make('complex', 'residential-icon-sub-item', 'Item')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image')->set_value_type('url'),
                    Field::make('text', 'title', 'Title'),
                    Field::make('text', 'details', 'Details'),
                    Field::make('text', 'learn_more', 'Learn More'),
                )),
            Field::make('checkbox', 'white_bg', 'White Background'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('United City', 'united')])
        ->set_description(__('United City', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/united-city');
        });


    Block::make(__('Service Icon', 'united'))
        ->add_fields(array(
            Field::make('text', 'heading', 'Heading'),
            Field::make('checkbox', 'font_weight_normal', 'Font Weight Normal'),
            Field::make('rich_text', 'details', 'Text'),
            Field::make('complex', 'service-icon', 'Service Icon')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                    Field::make('text', 'title', 'Title'),
                    Field::make('textarea', 'details', 'Details'),

                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Service Icon', 'united')])
        ->set_description(__('Service Icon', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/service-icon');
        });

    Block::make(__('Single Image', 'united'))
        ->add_fields(array(
            Field::make('image', 'image', 'Image')->set_value_type('url'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Single Image', 'united')])
        ->set_description(__('Single Image', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/single-image');
        });



    Block::make(__('Full Single Image', 'united'))
        ->add_fields(array(
            Field::make('complex', 'full-image', 'Image')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'desktop_image', 'Image'),
                    Field::make('image', 'mobile_image', 'Mobile Image'),
                ))

        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Single Image', 'united')])
        ->set_description(__('Single Image', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/full-width-single-image');
        });


    Block::make(__('Education Item Service', 'united'))
        ->add_fields(array(
            Field::make('complex', 'education-service-item', 'Education Item Service')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                    Field::make('text', 'title', 'Title'),
                    Field::make('textarea', 'details', 'Details'),
                    Field::make('text', 'link', 'Link'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Service Icon', 'united')])
        ->set_description(__('Service Icon', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/education-service-item');
        });

    Block::make(__('Photo and Gallery', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Service Icon', 'united')])
        ->set_description(__('Service Icon', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/photo-gellery');
        });

    Block::make(__('Research and investor Cateogories Tab', 'united'))
        ->add_fields(array(
            Field::make('complex', 'reserach-categories-item', 'Categories')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('text', 'title', 'Title'),
                    Field::make('complex', 'reserach-sub-categories-item', 'Sub -  Categories')
                        ->set_layout('tabbed-vertical')
                        ->add_fields(array(
                            Field::make('text', 'title', 'Title'),
                            Field::make('complex', 'reserach-sub-item', 'Item')
                                ->set_layout('tabbed-vertical')
                                ->add_fields(array(
                                    Field::make('text', 'title', 'Title'),
                                    Field::make('file', 'download_pdf', 'Attach PDF')->set_value_type('url'),
                                ))
                        ))
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Research and investor Cateogories Tab', 'united')])
        ->set_description(__('Research and investor Cateogories Tab', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/research-investor');
        });


    Block::make(__('Chief Message', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('text', 'name', 'Name'),
            Field::make('text', 'designation', 'Designation'),
            Field::make('text', 'company_name', 'Company Name'),
            Field::make('text', 'slug', 'Slug'),
            Field::make('text', 'note_slug', 'Note Slug'),
            Field::make('complex', 'year-sub-item', 'Year Item')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('text', 'year', 'Year'),
                )),
            Field::make('checkbox', 'hide_slider', 'Hide Nav?'),

        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Cheif Message', 'united')])
        ->set_description(__('Cheif Message', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/cheif-message');
        });


    Block::make(__('All Life at United or News', 'united'))
        ->add_fields(array(
            Field::make('text', 'categories_name', 'Category Slug'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Cheif Message', 'united')])
        ->set_description(__('Cheif Message', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/blog');
        });

    Block::make(__('Single Banner', 'united'))
        ->add_fields(array(
            Field::make('image', 'image', 'Image')->set_value_type('url'),
            Field::make('text', 'title', 'Title'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Single Banner', 'united')])
        ->set_description(__('Single Banner', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/single-banner');
        });

    Block::make(__('Career Banner', 'united'))
        ->add_fields(array(
            Field::make('image', 'image', 'Image')->set_value_type('url'),
            Field::make('text', 'title', 'Title'),
            Field::make('text', 'short_title', 'Short Title'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Career Banner', 'united')])
        ->set_description(__('Career Banner', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/career-banner');
        });

    Block::make(__('Contact Form', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('text', 'sub-title', 'Sub Title'),
            Field::make('text', 'shortcode', 'Contact form shortcode'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Contact Form', 'united')])
        ->set_description(__('Contact Form', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/contact-form');
        });

    Block::make(__('Contact Map', 'united'))
        ->add_fields(array(
            Field::make('image', 'background_image', 'Background Image')->set_value_type('url'),
            Field::make('image', 'map_image', 'Map Image')->set_value_type('url'),
            Field::make('text', 'title', 'Title'),
            Field::make('text', 'location_name', 'Location Name'),
            Field::make('text', 'phone', 'Phone'),
            Field::make('text', 'email', 'Email'),
            Field::make('text', 'webmail', 'Webmail'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Contact Map', 'united')])
        ->set_description(__('Contact Map', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/contact-map');
        });

    Block::make(__('Application Form', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('text', 'description', 'Description'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Application Form', 'united')])
        ->set_description(__('Application Form', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/application-form');
        });


    Block::make(__('Highlighted Project', 'united'))
        ->add_fields(array(
            Field::make('image', 'iransparent_banner', 'Transparent Banner')->set_value_type('url'),
            Field::make('image', 'image', 'Image')->set_value_type('url'),
            Field::make('text', 'title', 'Title'),
            Field::make('rich_text', 'left_half_content', 'Half Content'),
            Field::make('rich_text', 'right_half_content', 'Half Content'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Highlighted Project', 'united')])
        ->set_description(__('Highlighted Project', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/highlighted-project');
        });

    Block::make(__('Residential  Projects', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('rich_text', 'content', 'Content'),
            Field::make('text', 'add_class', 'Add Class'),
            Field::make('checkbox', 'order_item', 'Order Item?'),
            Field::make('checkbox', 'remove_padding_bottom', 'None Padding Bottom?'),
            Field::make('complex', 'residential-sub-item', 'Item')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image')->set_value_type('url'),
                    Field::make('text', 'title', 'Title'),
                    Field::make('textarea', 'details', 'Details'),
                    Field::make('text', 'url', 'url'),
                    Field::make('select', 'selected_ids', __('Select Concerns'))
                        ->add_options(get_post_arr('our-concerns')),
                    Field::make('complex', 'residential-icon-sub-item', 'Item')
                        ->set_layout('tabbed-vertical')
                        ->add_fields(array(
                            Field::make('image', 'image', 'Image')->set_value_type('url'),
                            Field::make('text', 'title', 'Title'),
                            Field::make('textarea', 'details', 'Details'),
                        ))
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Residential  Projects', 'united')])
        ->set_description(__('Residential  Projects', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/residential-project');
        });

    Block::make(__('Carrer Opportunities', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('textarea', 'content', 'Content'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Carrer Opportunities', 'united')])
        ->set_description(__('Carrer Opportunities', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/career-opportunities');
        });

    Block::make(__('Drop your resume', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('text', 'button_link', 'Button Link'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Drop your resume', 'united')])
        ->set_description(__('Drop your resume', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/drop-resusme');
        });



    Block::make(__('Drop your resume form', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('text', 'sub_text', 'Short Text'),
            Field::make('text', 'sub_input_title', 'Sub Title'),

            Field::make('complex', 'interest-department', 'Interested department')
                ->set_layout('tabbed-horizontal')
                ->add_fields(array(
                    Field::make('text', 'title', 'Title'),
                )),
            Field::make('complex', 'division-department', 'Division')
                ->set_layout('tabbed-horizontal')
                ->add_fields(array(
                    Field::make('text', 'title', 'Title'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Drop your resume form', 'united')])
        ->set_description(__('Drop your resume form', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/drop-resusme-form');
        });


    Block::make(__('Career Details', 'united'))
        ->add_fields(array(
            Field::make('image', 'image', 'Image'),
            Field::make("complex", "content", "Content")
                ->set_layout("tabbed-vertical")
                ->add_fields(array(
                    Field::make('text', 'title', 'Content Title'),
                    Field::make('rich_text', 'rich_text', 'Content Details'),
                )),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Career Details', 'united')])
        ->set_description(__('Career Details', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/job_career_details');
        });

    Block::make(__('Message from CEO', 'united'))
        ->add_fields(array(
            Field::make('text', 'message_title', 'Title'),
            Field::make('image', 'image', 'Image'),
            Field::make('image', 'mobile_image', 'Mobile Image'),
            Field::make('rich_text', 'message', 'Message'),
            Field::make('text', 'name_ceo', 'Name of CEO'),
            Field::make('text', 'designation', 'Designation'),
            Field::make('text', 'company', 'Company'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Message from CEO', 'united')])
        ->set_description(__('Message from CEO', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/message-from-ceo');
        });


    Block::make(__('Banner Video', 'united'))
        ->add_fields(array(
            Field::make('file', 'video', __('File'))
                ->set_type(array('video'))
                ->set_value_type('url'),
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Banner Video', 'united')])
        ->set_description(__('Banner Video', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/banner-video');
        });

    Block::make(__('Our suppliers', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('rich_text', 'details', 'Details'),
            Field::make('text', 'title_one', 'Title One'),
            Field::make('text', 'title_two', 'Title Two'),
            Field::make('complex', 'suppliers-item-title-tab-conditional', 'List')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('text', 'suppliers_title', 'Suppliers Title'),
                    Field::make('complex', 'suppliers-item-tab-conditional', 'List')
                        ->set_layout('tabbed-vertical')
                        ->add_fields(array(
                            Field::make('text', 'item', 'Item'),

                        ))
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Our suppliers', 'united')])
        ->set_description(__('Our suppliers', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/our-suppliers');
        });


    Block::make(__('Video Gallery', 'united'))
        ->add_fields(array(
            Field::make('complex', 'video-item-title-tab-conditional', 'Gallery')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'video_poster_image', 'Image'),
                    Field::make('text', 'video_title', 'Video  Title'),
                    Field::make('text', 'video_embed', 'Embed Url'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Video Gallery', 'united')])
        ->set_description(__('Video Gallery', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/video-gallery');
        });

    Block::make(__('Partner Logo', 'united'))
        ->add_fields(array(
            Field::make('text', 'title', 'Title'),
            Field::make('checkbox', 'large_logo', 'Large Logo'),
            Field::make('complex', 'partner-tab-conditional', 'Partner Items')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'Image'),
                    Field::make('text', 'link', 'Link'),
                ))
        ))
        ->set_icon('star-filled')
        ->set_keywords([__('Partner Logo', 'united')])
        ->set_description(__('Partner Logo', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/partner-logo');
        });


    Block::make(__('List Group', 'united'))
        ->add_fields(array(
            Field::make('checkbox', 'padding_top', 'Padding Top None?'),
            Field::make('checkbox', 'check_orange', 'Orange Icon'),
            Field::make('checkbox', 'check_real_estate', 'Real estate'),
            Field::make('checkbox', 'check_retail', 'Retail'),
            Field::make('text', 'title', 'Title'),
            Field::make('complex', 'left-list-conditional', 'Left List')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('text', 'text', 'Title'),
                )),
            Field::make('complex', 'right-list-conditional', 'Right List')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('text', 'text', 'Title'),
                ))

        ))
        ->set_icon('star-filled')
        ->set_keywords([__('List Group', 'united')])
        ->set_description(__('List Group', 'united'))
        ->set_render_callback(function ($fields, $attributes, $inner_blocks) {
            setData($fields);
            get_template_part('component/list-group');
        });
}
