<?php


// Hooking up our function to theme setup
add_action('init', 'united_register_post_type');

function united_register_post_type()
{
    register_post_type(
        'about_archive',
        [
            'labels' => array(
                'name' => 'Archive',
                // 'singular_name' => ucfirst($post_name).'s'
            ),
            'taxonomies'  => 'about_archive',
            'public' => true,
            'has_archive' => false,
            'show_in_rest' => true,
            'exclude_from_search' => true,
            'taxonomies' => [],
            'supports' => ['title', 'editor', 'thumbnail'],
            'capability_type'    => 'about_archive',

            'hierarchical' => true,
            'menu_icon'           => 'dashicons-calendar',
        ]
    );

    register_post_type(
        'our-concerns',
        [
            'labels' => array(
                'name' => 'Our Concerns',
                // 'singular_name' => ucfirst($post_name).'s'
            ),
            'taxonomies'  => 'our-concerns',
            'public' => true,
            'has_archive' => true,
            'show_in_rest' => true,
            'exclude_from_search' => true,
            'taxonomies' => [],
            'supports' => ['title', 'editor', 'thumbnail', 'excerpt'],
            'hierarchical' => true,
            'menu_icon'           => 'dashicons-calendar',
            'capability_type'    => 'our-concerns',

        ]
    );

    register_post_type(
        'gallery',
        [
            'labels' => array(
                'name' => 'Our Gallery',
                // 'singular_name' => ucfirst($post_name).'s'
            ),
            'taxonomies'  => 'gallery',
            'public' => true,
            'has_archive' => true,
            'show_in_rest' => true,
            'exclude_from_search' => true,
            'taxonomies' => [],
            'supports' => ['title'],
            'hierarchical' => true,
            'capability_type'    => 'gallery',

            'menu_icon'           => 'dashicons-calendar',
        ]
    );

    register_post_type(
        'cheif-message-note',
        [
            'labels' => array(
                'name' => 'Message Note',
                // 'singular_name' => ucfirst($post_name).'s'
            ),
            'taxonomies'  => 'cheif-message-note',
            'public' => true,
            'has_archive' => true,
            'show_in_rest' => true,
            'exclude_from_search' => true,
            'taxonomies' => [],
            'supports' => ['title', 'editor'],
            'capability_type'    => 'cheif-message-note',
            'hierarchical' => true,
            'menu_icon'           => 'dashicons-calendar',
        ]
    );

    register_post_type(
        'submission-user-form',
        [
            'labels' => array(
                'name' => 'Submission Form',
                // 'singular_name' => ucfirst($post_name).'s'
            ),
            'taxonomies'  => 'submission-user-form',
            'public' => false,
            'has_archive' => true,
            'show_in_rest' => true,
            'exclude_from_search' => true,
            'capability_type'    => 'submission-user-form',

            'taxonomies' => [],
            'supports' => ['title', 'editor'],
            'hierarchical' => true,
            'menu_icon'           => 'dashicons-calendar',
        ]
    );

    register_post_type(
        'career',
        [
            'labels' => array(
                'name' => 'Career',
                // 'singular_name' => ucfirst($post_name).'s'
            ),
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'show_in_rest'       => true,
            'query_var'          => true,
            'rewrite'            => array('slug' => 'career-categories'),
            'capability_type'    => 'career',
            'map_meta_cap'       => true,
            'has_archive'        => true,
            'hierarchical'       => false,
            'menu_position'      => null,
        ]
    );
}



function career_register_taxonomy()
{
    $labels = array(
        'name'              => _x('Categories', 'taxonomy general name', 'united'),
        'singular_name'     => _x('Categories', 'taxonomy singular name', 'united'),
        'search_items'      => __('Search Categories', 'united'),
        'all_items'         => __('All Categories', 'united'),
        'parent_item'       => __('Parent Categories', 'united'),
        'parent_item_colon' => __('Parent Categories:', 'united'),
        'edit_item'         => __('Edit Categories', 'united'),
        'update_item'       => __('Update Categories', 'united'),
        'add_new_item'      => __('Add New Categories', 'united'),
        'new_item_name'     => __('New Categories Name', 'united'),
        'menu_name'         => __('Categories', 'united'),
    );
    $args   = array(
        'hierarchical'      => true, // make it hierarchical (like categories)
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'show_in_rest' => true,
        'rewrite'           => ['slug' => 'career-categories'],
    );
    register_taxonomy('career-categories', ['career'], $args);
}
add_action('init', 'career_register_taxonomy');

function about_year_register_taxonomy()
{
    $labels = array(
        'name'              => _x('Year', 'taxonomy general name', 'united'),
        'singular_name'     => _x('Year', 'taxonomy singular name', 'united'),
        'search_items'      => __('Search Year', 'united'),
        'all_items'         => __('All Year', 'united'),
        'parent_item'       => __('Parent Year', 'united'),
        'parent_item_colon' => __('Parent Year:', 'united'),
        'edit_item'         => __('Edit Year', 'united'),
        'update_item'       => __('Update Year', 'united'),
        'add_new_item'      => __('Add New Year', 'united'),
        'new_item_name'     => __('New Year Name', 'united'),
        'menu_name'         => __('Year', 'united'),
    );
    $args   = array(
        'hierarchical'      => true, // make it hierarchical (like categories)
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'show_in_rest' => true,
        'rewrite'           => ['slug' => 'about_year_archive'],
    );
    register_taxonomy('about_year_archive_type', ['about_archive'], $args);
}
add_action('init', 'about_year_register_taxonomy');


function gallery_register_taxonomy()
{
    $labels = array(
        'name'              => _x('Categories', 'taxonomy general name', 'united'),
        'singular_name'     => _x('Categories', 'taxonomy singular name', 'united'),
        'search_items'      => __('Search Categories', 'united'),
        'all_items'         => __('All Categories', 'united'),
        'parent_item'       => __('Parent Categories', 'united'),
        'parent_item_colon' => __('Parent Categories:', 'united'),
        'edit_item'         => __('Edit Categories', 'united'),
        'update_item'       => __('Update Categories', 'united'),
        'add_new_item'      => __('Add New Categories', 'united'),
        'new_item_name'     => __('New Categories Name', 'united'),
        'menu_name'         => __('Categories', 'united'),
    );
    $args   = array(
        'hierarchical'      => true, // make it hierarchical (like categories)
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'show_in_rest' => true,
        'rewrite'           => ['slug' => 'photos_categories'],
    );
    register_taxonomy('photos_categories', ['gallery'], $args);
}
add_action('init', 'gallery_register_taxonomy');


function cheif_message_register_taxonomy()
{
    $labels = array(
        'name'              => _x('Year', 'taxonomy general name', 'united'),
        'singular_name'     => _x('Year', 'taxonomy singular name', 'united'),
        'search_items'      => __('Search Year', 'united'),
        'all_items'         => __('All Year', 'united'),
        'parent_item'       => __('Parent Year', 'united'),
        'parent_item_colon' => __('Parent Year:', 'united'),
        'edit_item'         => __('Edit Year', 'united'),
        'update_item'       => __('Update Year', 'united'),
        'add_new_item'      => __('Add New Year', 'united'),
        'new_item_name'     => __('New Year Name', 'united'),
        'menu_name'         => __('Year', 'united'),
    );
    $args   = array(
        'hierarchical'      => true, // make it hierarchical (like categories)
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'show_in_rest' => true,
        'rewrite'           => ['slug' => 'cheif-message-note'],
    );
    register_taxonomy('cheif-message-year', ['cheif-message-note'], $args);
}
add_action('init', 'cheif_message_register_taxonomy');

function cheif_message_categories_register_taxonomy()
{
    $labels = array(
        'name'              => _x('Categories', 'taxonomy general name', 'united'),
        'singular_name'     => _x('Categories', 'taxonomy singular name', 'united'),
        'search_items'      => __('Search Categories', 'united'),
        'all_items'         => __('All Categories', 'united'),
        'parent_item'       => __('Parent Categories', 'united'),
        'parent_item_colon' => __('Parent Categories:', 'united'),
        'edit_item'         => __('Edit Categories', 'united'),
        'update_item'       => __('Update Categories', 'united'),
        'add_new_item'      => __('Add New Categories', 'united'),
        'new_item_name'     => __('New Categories Name', 'united'),
        'menu_name'         => __('Categories', 'united'),
    );
    $args   = array(
        'hierarchical'      => true, // make it hierarchical (like categories)
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'show_in_rest' => true,
        'rewrite'           => ['slug' => 'cheif-message-note'],
    );
    register_taxonomy('cheif-message-categories', ['cheif-message-note'], $args);
}
add_action('init', 'cheif_message_categories_register_taxonomy');

function cheif_message_note_categories_register_taxonomy()
{
    $labels = array(
        'name'              => _x('Note Categories', 'taxonomy general name', 'united'),
        'singular_name'     => _x('Note Categories', 'taxonomy singular name', 'united'),
        'search_items'      => __('Search Note Categories', 'united'),
        'all_items'         => __('All Note Categories', 'united'),
        'parent_item'       => __('Parent Note Categories', 'united'),
        'parent_item_colon' => __('Parent Note Categories:', 'united'),
        'edit_item'         => __('Edit Note Categories', 'united'),
        'update_item'       => __('Update Note Categories', 'united'),
        'add_new_item'      => __('Add Note New Categories', 'united'),
        'new_item_name'     => __('New Note Categories Name', 'united'),
        'menu_name'         => __('Note Categories', 'united'),
    );
    $args   = array(
        'hierarchical'      => true, // make it hierarchical (like categories)
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'show_in_rest' => true,
        'rewrite'           => ['slug' => 'cheif-message-note'],
    );
    register_taxonomy('cheif-message-note-categories', ['cheif-message-note'], $args);
}
add_action('init', 'cheif_message_note_categories_register_taxonomy');

function concern_register_taxonomy()
{
    $labels = array(
        'name'              => _x('Categories', 'taxonomy general name', 'united'),
        'singular_name'     => _x('Categories', 'taxonomy singular name', 'united'),
        'search_items'      => __('Search Categories', 'united'),
        'all_items'         => __('All Categories', 'united'),
        'parent_item'       => __('Parent Categories', 'united'),
        'parent_item_colon' => __('Parent Categories:', 'united'),
        'edit_item'         => __('Edit Categories', 'united'),
        'update_item'       => __('Update Categories', 'united'),
        'add_new_item'      => __('Add New Categories', 'united'),
        'new_item_name'     => __('New Categories Name', 'united'),
        'menu_name'         => __('Categories', 'united'),
    );
    $args   = array(
        'hierarchical'      => true, // make it hierarchical (like categories)
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'show_in_rest' => true,
        'rewrite'           => ['slug' => 'concerns_categories'],
    );
    register_taxonomy('concerns_categories', ['our-concerns'], $args);
}
add_action('init', 'concern_register_taxonomy');
