<?php

/**
 * Product page metabox function/register will go here
 */

use Carbon_Fields\Container;
use Carbon_Fields\Field;
use Carbon_Fields\Block;

add_action('carbon_fields_register_fields', 'ilc_post_meta');

// carbon_get_post_meta( $id, $name ) 
// carbon_get_post_meta( $id, $name )
function ilc_post_meta()
{
    Container::make('post_meta', 'External link')
        ->where('post_type', '=', 'post')
        ->add_fields(array(
            Field::make('text', 'external_link', 'Link'),
        ));
    Container::make('post_meta', 'External link')
        ->where('post_type', '=', 'events')
        ->add_fields(array(
            Field::make('text', 'external_link', 'Link'),
        ));

    Container::make('post_meta', 'File')
        ->where('post_type', '=', 'resources')
        ->add_fields(array(
            Field::make('file', 'resources_pdf_english_download', 'PDF English File'),
            Field::make('file', 'resources_pdf_french_download', 'PDF French File'),

            Field::make('file', 'resources_word_english_download', 'Word English File'),
            Field::make('file', 'resources_word_french_download', 'Word French File'),
        ));
}
