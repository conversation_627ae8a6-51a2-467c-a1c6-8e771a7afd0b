<?php

/**
 * about archive page metabox function/register will go here
 */

use Carbon_Fields\Container;
use Carbon_Fields\Field;
use Carbon_Fields\Block;

add_action('carbon_fields_register_fields', 'united_post_page_meta');

// carbon_get_post_meta( $id, $name ) 
// carbon_get_post_meta( $id, $name )
function united_post_page_meta()
{
    Container::make('post_meta', __('Archive Information', 'united'))
        ->where('post_type', '=', 'about_archive')
        ->add_fields(array(
            Field::make('image', 'company_logo',   __('Company Logo', 'united')),
            Field::make('text', 'vist_link',   __('Vist Link', 'united')),
            Field::make('image', 'full_img',   __('Full Banner Image', 'united')),
            Field::make('complex', 'archive-year-content-tab-list-conditional', 'Item List')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('text', 'list', 'List'),
                ))
        ));

    // Container::make('post_meta', __('Banner Information', 'united'))
    //     ->where('post_type', '=', 'our-concerns')
    //     ->add_fields(array(
    //         Field::make('image', 'concerns_full_banner',   __('Page Banner', 'united')),
    //         Field::make('textarea', 'concerns_full_banner_short_text',   __('Short Text', 'united')),
    //         Field::make('text', 'concerns_website_link',   __('Visit Website', 'united')),

    //     ));

    Container::make('post_meta', __('Our Media', 'united'))
        ->where('post_type', '=', 'gallery')
        ->add_fields(array(
            Field::make('complex', 'gallery-item-tab-list-conditional', 'Gallery List')
                ->set_layout('tabbed-vertical')
                ->add_fields(array(
                    Field::make('image', 'image', 'image'),
                    Field::make('text', 'caption_title', 'Caption Title'),
                    Field::make('textarea', 'caption_discription', 'Caption Discription'),
                ))

        ));

    Container::make('post_meta', __('Description', 'united'))
        ->where('post_type', '=', 'career')
        ->add_fields(array(
            Field::make('image', 'job_banner', __('Banner Image'))->set_value_type('url'),
            Field::make('text', 'job_title', __('Job Type')),
            Field::make('text', 'job_experience', __('Experience')),
            Field::make('text', 'job_joining', __('Submission Deadline')),
        ));

    Container::make('post_meta', __('Banner', 'united'))
        ->where('post_type', '=', 'post')
        ->add_fields(array(
            Field::make('image', 'post_banner', __('Banner Image'))->set_value_type('url'),
        ));
}
