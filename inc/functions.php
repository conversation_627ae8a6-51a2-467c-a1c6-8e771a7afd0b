<?php

/**
 * @package united-group
 */

/**
 * Get Carbon fields set_render_callback data
 *
 * @return void
 */
function getData()
{
    return get_query_var('component_data', []);
}

/**
 * Set Carbon fields set_render_callback data
 *
 * @return void
 */
function setData($data)
{
    return set_query_var('component_data', $data);
}

/**
 * Html Pre tag set print_r
 *
 * @param [type] $data
 * @return void
 */
function  pre_inline($data)
{
    echo "<pre>";
    print_r($data);
    echo "</pre>";
}

/**
 * Check the column row
 *
 * @param [type] $col
 * @return void
 */
function col_size($col)
{
    if ($col == 1) {
        echo 'col-lg-12';
    } elseif ($col == 2) {
        echo 'col-lg-6';
    } elseif ($col == 3) {
        echo 'col-lg-4';
    } else {
        echo 'col-lg-4';
    }
}

/**
 * Convert to slug by string
 *
 * @param [type] $string
 * @return void
 */
function fixForUri($string)
{
    $slug = trim($string); // trim the string
    $slug = preg_replace('/[^a-zA-Z0-9 -]/', '', $slug); // only take alphanumerical characters, but keep the spaces and dashes too...
    $slug = str_replace(' ', '-', $slug); // replace spaces by dashes
    $slug = strtolower($slug);  // make it lowercase
    return $slug;
}

/**
 * Pagination 
 * @param [type] $array
 */

function united_get_paginated_links($query)
{
    // When we're on page 1, 'paged' is 0, but we're counting from 1,
    // so we're using max() to get 1 instead of 0
    $currentPage = max(1, get_query_var('paged', 1));

    // This creates an array with all available page numbers, if there
    // is only *one* page, max_num_pages will return 0, so here we also
    // use the max() function to make sure we'll always get 1
    $pages = range(1, max(1, $query->max_num_pages));

    // Now, map over $pages and return the page number, the url to that
    // page and a boolean indicating whether that number is the current page
    return array_map(function ($page) use ($currentPage) {
        return (object) array(
            "isCurrent" => $page == $currentPage,
            "page" => $page,
            "url" => get_pagenum_link($page)
        );
    }, $pages);
}


function get_post_arr($pType)
{
    $output = ['0' => "None"];
    $games = get_posts(['post_type' => $pType, 'posts_per_page' => -1,]);
    foreach ($games as $game) {
        $output[$game->ID] = $game->post_title;
    }
    return $output;
}

function get_news_post_arr($pType, $categories)
{
    $output = ['0' => "None"];
    $games = get_posts(['post_type' => $pType, 'category_name' => $categories, 'posts_per_page' => -1,]);
    foreach ($games as $game) {
        $output[$game->ID] = $game->post_title;
    }
    return $output;
}
