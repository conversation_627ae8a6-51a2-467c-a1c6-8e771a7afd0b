<?php
// Exit if afbessed directly.
defined('ABSPATH') || exit;

if (!function_exists('united_scripts')) {
	/**
	 * Load theme's JavaScript and CSS sources.
	 */
	function united_scripts()
	{
		// Get the theme data.
		$the_theme     = wp_get_theme();
		$theme_version = $the_theme->get('Version');
		wp_enqueue_style('united-style', get_stylesheet_uri());
		wp_enqueue_style('lightgallery-style', get_template_directory_uri() . '/dist/vendor/css/lightgallery.css', array(), 1);
		wp_enqueue_style('animate-style', get_template_directory_uri() . '/dist/vendor/css/animate.css', array(), 1);
		wp_enqueue_style('theme-style', get_template_directory_uri() . '/dist/theme.css');

		wp_enqueue_script('parallax-script', get_template_directory_uri() . '/dist/vendor/js/parallax.js', array('jquery'));
		wp_enqueue_script('wow-script', get_template_directory_uri() . '/dist/vendor/js/wow.min.js', array('jquery'));
		wp_enqueue_script('lightgallery-script', get_template_directory_uri() . '/dist/vendor/js/lightgallery-all.min.js', array('jquery'));
		wp_enqueue_script('theme-script', get_template_directory_uri() . '/dist/theme.js', array('jquery'));
		wp_enqueue_script('waypoints-script', 'https://cdnjs.cloudflare.com/ajax/libs/waypoints/2.0.5/waypoints.min.js');


		wp_localize_script('theme-script', 'urls', [
			'siteRoot' => home_url('/'),
			'adminAjax' => admin_url('admin-ajax.php')
		]);
	}
} // endif function_exists( 'united_scripts' ).

add_action('wp_enqueue_scripts', 'united_scripts');
