<?php

$to_email = get_option('_united_drop_resume_email');

/**
 * Application_form class
 */
class Application_form
{

    public $email;

    public $submissionID;

    /**
     *
     * @param [type] $email
     */
    public function __construct($email)
    {
        add_action('admin_post_add_resume', [$this, 'verify_email_handle_form']);
        add_action('admin_post_nopriv_add_resume', [$this, 'verify_email_handle_form']);


        add_action('wp_ajax_verify_email_handle_form', [$this, 'verify_email_handle_form']);
        add_action('wp_ajax_nopriv_verify_email_handle_form', [$this, 'verify_email_handle_form']);

        add_action('admin_post_add_resume_default', [$this, 'default_handle_form']);
        add_action('admin_post_nopriv_add_resume_default', [$this, 'default_handle_form']);

        $this->email = $email;
    }


    /**
     * Email Handle Form
     *
     * @return void
     */
    public function verify_email_handle_form()
    {
        if (isset($_POST['submission_submit'])) {
            global $wpdb;
            $email          = sanitize_email($_POST['email']);
            $getEmail       = $wpdb->get_var($wpdb->prepare("SELECT meta_value FROM $wpdb->postmeta WHERE meta_key = %s AND meta_value = %s LIMIT 1", 'submission_email',  $email));

            if (empty($getEmail)) {
                $created_post_id    = wp_insert_post([
                    'post_title'    => $_POST['submisson_id'] . "_" . uniqid(),
                    'post_status'   => 'publish',
                    'post_type'     => 'submission-user-form',
                    'post_content'  => '---',
                    'post_status'   => 'publish',
                ]);
                $digits = 5;
                $otp = rand(pow(10, $digits - 1), pow(10, $digits) - 1);
                add_post_meta($created_post_id, 'submission_email', $email);
                add_post_meta($created_post_id, 'submisson_id_inactived', 0);
                add_post_meta($created_post_id, 'sumission_otp', $otp);

                $headers = array('Content-Type: text/html; charset=UTF-8');
                $subject = esc_html('OTP Verification');
                $body =  esc_html('Your OTP is ' . $otp);

                wp_mail($email, $subject, $body, $headers);
                wp_send_json(array(
                    'message'  => esc_html('We have sent a verification code in the mail ' . $email . '. Please verify your email before apply'),
                    'status'   => 1
                ));
            } else {
                $getId          = $wpdb->get_var($wpdb->prepare("SELECT post_id FROM $wpdb->postmeta WHERE meta_key = %s AND meta_value = %s LIMIT 1", 'submission_email',  $email));

                $otp            = $wpdb->get_var($wpdb->prepare("SELECT meta_value FROM $wpdb->postmeta WHERE post_id = %d AND meta_key = %s LIMIT 1",  $getId,  'sumission_otp'));

                $isformSubmited = get_post_meta($getId, 'submisson_id_inactived', true);
                if ($isformSubmited == 0) {
                    $headers = array('Content-Type: text/html; charset=UTF-8');
                    $subject = esc_html('OTP Verification');
                    $body =  esc_html('Your OTP is ' . $otp);
                    wp_mail($email, $subject, $body, $headers);
                    wp_send_json(array(
                        'message'  => esc_html('We have again sent a verification code in the mail ' . $email . '. Please verify your email before apply'),
                        'status'   => 1
                    ));
                } else {
                    wp_send_json(array(
                        'message'  => esc_html('You have already submitted the form.'),
                        'status'   => 0
                    ));
                }
            }
        }

        if (isset($_POST['submit']) && !empty($_POST['submit'])) {
            if (!isset($_POST['cpt_nonce_field']) || !wp_verify_nonce($_POST['cpt_nonce_field'], 'cpt_nonce_action')) {
                print 'Sorry, your nonce did not verify.';
            } else {
                if (!function_exists('wp_handle_upload')) {
                    require_once(ABSPATH . 'wp-admin/includes/file.php');
                }

                $verification_code      = $_POST['verification_code'];
                $email                  = $_POST['email'];
                $phone                  = $_POST['phone'];
                $interest_department    = $_POST['interest_department'];
                $current_company        = $_POST['current_company'];
                $division               = $_POST['division'];

                if (!empty($_POST['name']) && !empty($_POST['phone'] && !empty($_POST['interest_department']) && !empty($_FILES['resume']))) {

                    global $wpdb;

                    $getId          = $wpdb->get_var($wpdb->prepare("SELECT post_id FROM $wpdb->postmeta WHERE meta_key = %s AND meta_value = %s LIMIT 1", 'submission_email',  $email));
                    $match_verify   = $wpdb->get_var($wpdb->prepare("SELECT meta_value FROM $wpdb->postmeta WHERE post_id = %d AND meta_key = %s LIMIT 1",  $getId,  'sumission_otp'));

                    if ($verification_code === $match_verify) {
                        $uploadedfile = $_FILES['resume'];

                        $upload_overrides = array('test_form' => false);

                        $movefile = wp_handle_upload($uploadedfile, $upload_overrides);

                        if ($movefile && !isset($movefile['error'])) {
                            $name               = sanitize_text_field($_POST['name']);
                            $email              = sanitize_email($_POST['email']);
                            $phone              = sanitize_text_field($_POST['phone']);
                            $current_company    = sanitize_text_field($_POST['current_company']);

                            $body = '';

                            if (isset($name) && !empty($name) && $name != 'undefined') {
                                $body .= '<p> Name : ' . $name . '</p>';
                            }

                            if (isset($email) && !empty($email) && $email != 'undefined') {
                                $body .= '<p> Email : ' . $email . ' </p>';
                            }

                            if (isset($phone) && !empty($phone) && $phone != 'undefined') {
                                $body .= '<p> Phone : ' . $phone . ' </p>';
                            }

                            if (isset($division) && !empty($division) && $division != 'undefined') {
                                $body .= '<p> Division : ' . $division . ' </p>';
                            }

                            if (isset($interest_department) && !empty($interest_department) && $interest_department != 'undefined') {
                                $body .= '<p> Interested department : ' . $interest_department . ' </p>';
                            }

                            if (isset($current_company) && !empty($current_company) && $current_company != 'undefined') {
                                $body .= '<p> Company Name : ' . $current_company . ' </p>';
                            }

                            $body = wp_kses_post($body);

                            if (isset($_POST['job_title'])) {
                                $subject = "[Job Application] " . $_POST['job_title'];
                            } else {
                                $subject = 'Applicaton Form';
                            }

                            $attachment         = array($movefile['file']);
                            $headers            = array('Content-Type: text/html; charset=UTF-8');

                            wp_mail($this->email, $subject, $body, $headers, $attachment);

                            wp_delete_file($movefile['file']);
                            update_post_meta($getId, 'submisson_id_inactived', 1);
                            wp_send_json(array(
                                'message'  => esc_html('The form has been submitted.'),
                                'status'   => 1
                            ));
                        }
                    } else {
                        wp_send_json(array(
                            'message'  => esc_html('The verification code is not matching'),
                            'status'   => 0
                        ));
                    }
                } else {
                    wp_send_json(array(
                        'message'  => esc_html('Fill out the form correctly.'),
                        'status'   => 0
                    ));
                }
            }
        }
        die();
    }

    public function default_handle_form()
    {
        if (isset($_POST['submit']) && !empty($_POST['submit'])) {
            if (!isset($_POST['cpt_nonce_field']) || !wp_verify_nonce($_POST['cpt_nonce_field'], 'cpt_nonce_action')) {
                print 'Sorry, your nonce did not verify.';
            } else {

                if (!function_exists('wp_handle_upload')) {
                    require_once(ABSPATH . 'wp-admin/includes/file.php');
                }

                if (isset($_POST['name']) && !empty($_POST['name']) || isset($_POST['subject']) && !empty($_POST['subject']) ||  isset($_POST['email']) && !empty($_POST['email']) || isset($_POST['phone']) && !empty($_POST['phone'])) {
                    $uploadedfile = $_FILES['resume'];

                    $upload_overrides = array('test_form' => false);

                    $movefile = wp_handle_upload($uploadedfile, $upload_overrides);

                    if ($movefile && !isset($movefile['error'])) {
                        $name               = sanitize_text_field($_POST['name']);
                        $email              = sanitize_email($_POST['email']);
                        $phone              = sanitize_text_field($_POST['phone']);
                        $subject2           = sanitize_text_field($_POST['subject']);
                        $current_company    = sanitize_text_field($_POST['current_company']);
                        $subject            = sanitize_text_field('Apply to' . $subject2);
                        $body               = wp_kses_post('<p> Name : ' . $name . ' </p> <p> Email : ' . $email . ' </p> <p> Phone : ' . $phone . ' </p> <p> Company Name : ' . $current_company . ' </p>');
                        $attachment         = array($movefile['file']);
                        $headers = array('Content-Type: text/html; charset=UTF-8');

                        wp_mail($this->email, $subject, $body, $headers, $attachment);
                        wp_delete_file($movefile['file']);
                        echo '<script>  alert("Thank you for submitting your form. ");   </script>';
                        echo '<script> window.location.href = "/"; </script>';
                    }
                } else {
                    echo "Something went wrong!";
                }
            }
        }
    }
}


$form = new Application_form($to_email);
