<?php

/**
 * United Group functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package United Group
 */

if (!defined('_S_VERSION')) {
	// Replace the version number of the theme on each release.
	define('_S_VERSION', '1.0.0');
}
define('THEMEROOT', get_stylesheet_directory_uri());
define('IMG', THEMEROOT . '/dist/img');
define('ICON', THEMEROOT . '/dist/icons');
define('JS', THEMEROOT . '/dist/js');
define('CSS', THEMEROOT . '/dist/css');


$prefix_includes = array(
	'/setup.php',
	'/enqueue.php',
	'/register-gutenberg-components.php',
	'/theme-settings-page.php',
	'/cpt.php',
	'/custom-fields.php',
	'/ajax/ajax-get-current-archive-year.php',
	'/ajax/ajax-gallery-item.php',
	'/ajax/ajax-get-category-item.php',
	'/ajax/ajax-get-job-item.php',
	'/functions.php',
	'/class-application-form.php',
);

foreach ($prefix_includes as $file) {
	$filepath = locate_template('inc' . $file);
	if (!$filepath) {
		trigger_error(sprintf('Error locating /inc%s for inclusion', $file), E_USER_ERROR);
	}
	require_once $filepath;
}


add_image_size('video-poster', 600, 300, true);

function mytheme_custom_excerpt_length($length)
{
	return 20;
}
add_filter('excerpt_length', 'mytheme_custom_excerpt_length', 999);
