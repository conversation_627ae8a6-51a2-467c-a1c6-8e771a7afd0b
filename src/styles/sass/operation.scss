#operation {
	position: relative;
	.section-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 30px;
	}
	h2 {
		@include breakpoint('xl') {
			@include primary-heading();
		}
		font-size: 32px;
		line-height: 48px;
	}
	.operation-slider {
		overflow: hidden;
		position: relative;

		.slider {
			display: flex;
			@include media-breakpoint-down(lg) {
				flex-wrap: wrap;
				flex-direction: column-reverse;
			}
			.left {
				width: 100%;
				@include breakpoint('xl') {
					width: 50%;
				}
				display: flex;
				align-items: center;
				position: relative;
				background: #fafafa;
				border-radius: 5px;
				padding: 30px;
				@include breakpoint(xl-desktop) {
					padding: 50px;
				}
				.content-box {
					box-sizing: content-box;

					h3 {
						font-weight: 400;
						font-size: 26px;
						line-height: 38px;
						opacity: 0;
						-webkit-transform: translateY(40px);
						-moz-transform: translateY(40px);
						-ms-transform: translateY(40px);
						-o-transform: translateY(40px);
						transform: translateY(40px);
					}
					p {
						font-size: 16px;
						line-height: 26px;
						color: #000000;
						opacity: 0;
						-webkit-transform: translateY(50px);
						-moz-transform: translateY(50px);
						-ms-transform: translateY(50px);
						-o-transform: translateY(50px);
						transform: translateY(50px);
					}
				}
			}
			.right {
				width: 100%;
				@include breakpoint('xl') {
					width: 50%;
				}
				float: right;
				display: flex;
				justify-content: center;
				img {
					width: 100%;
					height: auto;
					object-fit: cover;
					@include media-breakpoint-up(sm) {
						height: 480px;
					}
				}
			}
		}
		.swiper-slide-active {
			h3 {
				opacity: 1 !important;
				-webkit-transition-delay: 0.6s;
				-moz-transition-delay: 0.6s;
				-ms-transition-delay: 0.6s;
				-o-transition-delay: 0.6s;
				transition-delay: 0.6s;
				-webkit-transform: translateX(-0px);
				-moz-transform: translateX(-0px);
				-ms-transform: translateX(-0px);
				-o-transform: translateX(-0px);
				transform: translateX(-0px) !important;
			}
			p {
				opacity: 1 !important;
				-webkit-transition-delay: 0.6s;
				-moz-transition-delay: 0.6s;
				-ms-transition-delay: 0.6s;
				-o-transition-delay: 0.6s;
				transition-delay: 0.6s;
				-webkit-transform: translateX(-0px);
				-moz-transform: translateX(-0px);
				-ms-transform: translateX(-0px);
				-o-transform: translateX(-0px);
				transform: translateX(-0px) !important;
			}
		}
	}
	.navigation {
		display: flex;
		justify-content: flex-end;
		z-index: 9;
		@include sectionNavigate();
	}
}
