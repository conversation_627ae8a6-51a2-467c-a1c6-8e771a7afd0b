.single-site-main {

	// padding: 50px 0 50px;
	.post-meta {
		padding-top: 15px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30px;

		.post-date {
			font-size: 14px;
			line-height: 24px;
			color: #4e4e4e;
		}

		.social {
			li {
				a {
					padding: 10px;
				}
			}
		}
	}

	article {
		padding-top: 50px !important;
		padding-bottom: 100px;

		@include media-breakpoint-down(xl) {
			padding-bottom: 50px;
		}
	}

	h2 {
		font-size: 32px;
		line-height: 48px;

		@include breakpoint('xl') {
			@include primary-heading();
		}
	}

	p {
		font-size: 16px;
		line-height: 26px;
		color: #4e4e4e;
		// text-align: justify;
	}
}

.may-also-like {
	background: #fef7f4;
	padding: 100px 0 100px;

	@include media-breakpoint-down(xl) {
		padding: 50px 0 50px;
	}

	.page-section {
		margin-bottom: 60px;

		@include media-breakpoint-down(lg) {
			margin-bottom: 40px;
		}

		h3 {
			font-size: 32px;
			line-height: 48px;

			@include breakpoint('xl') {
				@include primary-heading();
			}
		}

		p {
			color: #4e4e4e;
			font-size: 16px;
			line-height: 26px;
		}

		.cta {
			top: -30px;
			float: right;

			@include media-breakpoint-down(lg) {
				float: left;
			}
		}
	}

	.three-blog-column {
		@include media-breakpoint-up(lg) {
			display: flex;
			flex-wrap: wrap;
		}

		.image {
			margin-bottom: 30px;

			img {
				border-radius: 20px 0 0 0;
				height: 230px;
				width: 100%;
				object-fit: cover;
			}
		}

		.content {
			.post-date {
				font-size: 14px;
				line-height: 24px;
				color: #4e4e4e;
			}

			h2 {
				a {
					font-weight: 400;
				}

				font-size: 20px;
				line-height: 32px;
			}

			p {
				font-size: 16px;
				line-height: 26px;
				color: #4e4e4e;
			}
		}
	}
}

// Generic Carrer
.single-career {
	.career-banner-bg {
		padding: 200px 0 200px;
		background-size: cover;
		position: relative;
		z-index: 1;

		&::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: #000000;
			opacity: 0.6;
			transform: matrix(1, 0, 0, -1, 0, 0);
			z-index: -1;
		}

		.content {
			max-width: 730px;
			margin: 0 auto;
			text-align: center;
			z-index: 2;
			position: relative;
			display: flex;
			flex-direction: column;
			gap: 30px;
			align-items: center;
			flex-wrap: wrap;

			h2 {
				font-family: Barlow;
				font-size: 64px;
				font-weight: 600;
				line-height: 75px;
				text-align: center;
				color: #FFF;
				text-transform: none;
				margin-bottom: 0;

				@include breakpoint('xxl') {
					@include primary-heading();
				}
			}

			.group-action {
				display: flex;
				justify-content: center;
				gap: 15px;

				.item {
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 10px;
					text-align: center;
					flex-wrap: wrap;
					min-width: 130px;
					min-height: 40px;
					box-sizing: border-box;
					padding: 8px 20px;
					border-radius: 5px 0px 0px 0px;
					background: rgba(255, 255, 255, 0.05);
					border: 1px solid #FFFFFF;
					transition: all 0.3s ease;

					&:hover {
						background: rgba(255, 255, 255, 0.1);
					}

					.item-content {
						color: $white;
						font-size: 14px;
						font-weight: 500;
						line-height: 24px;
						cursor: default;
					}
				}
			}
		}
	}

	#career-content-details {
		.content {
			@include media-breakpoint-up(xl) {
				max-width: 830px;
				margin: 0 auto;
			}
		}
	}
}

@media screen and (max-width:1024px) {
	.single-career {
		.career-banner-bg {
			.content {
				h2 {
					@include primary-heading();
				}
			}
		}
	}
}

@media screen and (max-width:560px) {
	.single-career {
		.career-banner-bg {
			.content {
				padding-left: 15px;
				padding-right: 15px;
				.group-action {
					flex-wrap: wrap;
					gap: 10px;
				}
			}
		}
	}
}

.single-post {
	.post-banner {
		margin-bottom: 30px;
		border-radius: 20px 0 0 0;
		width: 100%;
		max-height: 500px;
	}

	.single-site-main {
		article {
			max-width: 830px;
			margin: 0 auto;
		}
	}
}