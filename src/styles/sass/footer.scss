.site-footer {
	background-color: #2b2b2b;
	a.footer-logo {
		padding-bottom: 60px;
		display: block;
		@include media-breakpoint-down('xl') {
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
	span.copyright {
		@include media-breakpoint-down('xl') {
			text-align: center;
		}
		display: block;
		font-weight: 400;
		font-size: 12px;
		line-height: 14px;
		color: #ffffff;
		text-transform: uppercase;
		opacity: 0.6;
		margin-bottom: 15px;
	}
	ul.footer-menu-bottom {
		display: flex;
		list-style: none;
		margin: 0;
		padding: 0;
		@include media-breakpoint-down('xl') {
			justify-content: center;
			margin: 0 0 30px 0;
		}
		li {
			a {
				border-right: 1px solid;
				font-weight: 400;
				font-size: 12px;
				color: #ffffff;
				opacity: 0.6;
				line-height: 14px;
				margin-right: 10px;
				padding-right: 10px;
				transition: all 0.25s ease-in-out;

				&:hover {
					transition: all 0.25s ease-in;
					color: #f05a23;
				}
			}
			&:last-child {
				a {
					border: none;
				}
			}
		}
	}
	ul.footer-menu {
		margin: 0;
		padding: 0px;
		@include breakpoint('lg') {
			padding: 0 0 0 50px;
		}
		list-style: none;
		li {
			padding-bottom: 30px;
			a {
				svg {
					path {
						fill: #ffffff;
					}
				}

				font-size: 16px;
				line-height: 26px;
				color: $white;
				transition: all 0.25s ease-in-out;

				&:hover {
					transition: all 0.25s ease-in;
					color: #f05a23;
					svg {
						path {
							fill: #f05a23;
						}
					}
				}
			}
		}
	}
	@include media-breakpoint-down('xl') {
		.footer-social ul {
			justify-content: center;
			display: flex;
			flex-wrap: wrap;
			li {
				margin-right: 20px;
			}
		}
		.footer-item-2 {
			display: flex;
			@include media-breakpoint-down(xl) {
				display: inline-block;
				text-align: center;
			}
			width: 50%;
			text-align: left;
		}
	}
}
