#newsletter {
	padding: 145px 0 145px;
	position: relative;
	z-index: 9;
	background-size: cover;
	&::after {
		content: '';
		z-index: -1;
		position: absolute;
		width: 100%;
		height: 100%;
		background-color: #0000006e;
		top: 0;
	}
	h3 {
		font-size: 32px;
		line-height: 48px;
		color: #ffffff;
		@include breakpoint('xl') {
			@include primary-heading();
		}
	}
	p {
		font-size: 16px;
		line-height: 26px;
		color: #c0c0c0;
	}
	.mc4wp-form {
		input[type='email'] {
			width: 100%;
			margin-bottom: 30px;
			@include breakpoint('xl') {
				width: 340px;
				margin-bottom: 0px;
			}
			height: 58px;
			font-size: 16px;
			line-height: 26px;
			color: #4e4e4e;
			padding: 17px 0px 17px 30px;
			border-radius: 5px 0px 0px 0px;
			background-color: #fef7f4;
			outline: none;
			border: none;
		}
		input[type='submit'] {
			background-color: #f05a23;
			border-radius: 5px 0px 0px 0px;
			width: 170px;
			height: 58px;
			border: none;
			font-weight: bold;
			font-size: 16px;
			line-height: 20px;
			text-transform: uppercase;
			color: #ffffff;
			position: relative;
			left: 0;
			@include breakpoint('xl') {
				left: -10px;
			}
			@include transition(0.5s);
			&:hover {
				@include transition(0.5s);
				background-color: $gray;
			}
		}
	}
}
