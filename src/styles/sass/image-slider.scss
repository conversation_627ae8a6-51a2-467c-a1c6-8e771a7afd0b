#image-slider {
	position: relative;
	.swiper {
		.swiper-slide {
			height: auto;
			@include breakpoint(xl) {
				height: 100%;
			}
		}
		.swiper-slide {
			.slider-content {
				position: relative;
				img {
					display: block;
					width: 100%;
					height: 300px;
					object-fit: cover;
					@include breakpoint(xl) {
						height: 600px;
					}
					@include breakpoint(xxl-desktop) {
						height: 800px;
					}
				}
				h4 {
					position: absolute;
					bottom: 30px;
					left: 50px;
					color: #fff;
					background: rgba(57, 57, 57, 0.6);
					backdrop-filter: blur(5px);
					padding: 5px 20px 5px 10px;
					@include media-breakpoint-down(lg) {
						bottom: -7px;
						left: 0;
					}
				}
			}
		}
	}
	.banner-slide-next-btn {
		width: 50px;
		height: 50px;
		line-height: 48px;
		right: 50px;
		background-color: rgba(255, 99, 96, 0.6);
		position: absolute;
		bottom: 30px;
		z-index: 2;
		outline: none;
		color: #fff;
		cursor: pointer;
		text-align: center;
		border-radius: 0 0 10px;
		transition: all 1ms ease-in-out;
		@include media-breakpoint-down(lg) {
			top: 50%;
			bottom: 50%;
			right: 0;
		}

		svg {
			path {
				fill: $white;
			}
		}
		&.active {
			background-color: #f05a23;
			transition: all 1ms ease-in;
		}
	}
	.banner-slide-prev-btn {
		width: 50px;
		height: 50px;
		line-height: 48px;
		right: 100px;
		background-color: rgba(255, 99, 96, 0.6);
		position: absolute;
		bottom: 30px;
		z-index: 2;
		outline: none;
		color: #fff;
		cursor: pointer;
		text-align: center;
		border-radius: 10px 0 0;
		transition: all 1ms ease-in-out;
		@include media-breakpoint-down(lg) {
			top: 50%;
			bottom: 50%;
			left: 0;
		}

		svg {
			path {
				fill: $white;
			}
		}
		&.active {
			background-color: #f05a23;
			transition: all 1ms ease-in;
		}
	}
	&.wider-height {
		.swiper .swiper-slide img {
			@include breakpoint(xl) {
				height: 600px;
			}
			@include breakpoint(xxl-desktop) {
				height: 800px;
			}
		}
	}
}

// Image Carousel Slider

.carousel-slider {
	padding-top: 80px;
	padding-bottom: 80px;
	@include media-breakpoint-down(lg) {
		padding-top: 60px;
		padding-bottom: 60px;
	}

	h2 {
		font-size: 32px;
		line-height: 48px;
		@include breakpoint('xl') {
			@include primary-heading();
		}
	}
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.navigation {
			display: flex;
			justify-content: flex-end;
			a.prev {
				width: 50px;
				height: 50px;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: #f7f7f7;
				border: 1px solid #f05a23;
				border-radius: 5px 0 0;
				transition: all 0.25s ease-in;
				svg {
					path {
						fill: #f05a23;
					}
				}
				&.active {
					background-color: #f05a23;
					transition: all 0.25s ease-in-out;
					svg {
						path {
							fill: #000;
						}
					}
				}
			}
			a.next {
				width: 50px;
				height: 50px;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: #f7f7f7;
				border: 1px solid #f05a23;
				border-radius: 0 0 5px;
				transition: all 0.25s ease-in-out;
				svg {
					path {
						fill: #f05a23;
					}
				}
				&.active {
					background-color: #f05a23;
					transition: all 0.25s ease-in-out;
					svg {
						path {
							fill: #000;
						}
					}
				}
			}
		}
	}

	.navigation {
		display: flex;
		justify-content: flex-end;
		a.prev {
			width: 50px;
			height: 50px;
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: #f7f7f7;
			border: 1px solid #f05a23;
			border-radius: 5px 0 0;
			transition: all 0.25s ease-in;
			svg {
				path {
					fill: #f05a23;
				}
			}
			&.active {
				background-color: #f05a23;
				transition: all 0.25s ease-in-out;
				svg {
					path {
						fill: #000;
					}
				}
			}
		}
		a.next {
			width: 50px;
			height: 50px;
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: #f7f7f7;
			border: 1px solid #f05a23;
			border-radius: 0 0 5px;
			transition: all 0.25s ease-in-out;
			svg {
				path {
					fill: #f05a23;
				}
			}
			&.active {
				background-color: #f05a23;
				transition: all 0.25s ease-in-out;
				svg {
					path {
						fill: #000;
					}
				}
			}
		}
	}
	.myCarouselSwiper {
		margin-top: 60px;
	}
	.swiper-slide {
		max-width: 255px;
		@include media-breakpoint-down(lg) {
			max-width: 100%;
		}
		align-items: center;
		width: 100%;
		border-left: 1px solid #f5f5f5;
		border-top: 1px solid #f5f5f5;
		border-bottom: 1px solid #f5f5f5;
		height: auto;
		display: flex;
		justify-content: center;
		img {
			transform: scale(0.9);
		}
	}
	.swiper-pagination {
		display: none;
	}
}

#full-single-image {
	position: relative;
	.banner-slide-next-btn {
		width: 50px;
		height: 50px;
		line-height: 48px;
		right: 50px;
		background-color: rgba(255, 99, 96, 0.6);
		position: absolute;
		bottom: 30px;
		z-index: 2;
		outline: none;
		color: #fff;
		cursor: pointer;
		text-align: center;
		border-radius: 0 0 10px;
		transition: all 1ms ease-in-out;
		@include media-breakpoint-down(lg) {
			top: 50%;
			bottom: 50%;
			right: 0;
		}

		svg {
			path {
				fill: $white;
			}
		}
		&.active {
			background-color: #f05a23;
			transition: all 1ms ease-in;
		}
	}
	.banner-slide-prev-btn {
		width: 50px;
		height: 50px;
		line-height: 48px;
		right: 100px;
		background-color: rgba(255, 99, 96, 0.6);
		position: absolute;
		bottom: 30px;
		z-index: 2;
		outline: none;
		color: #fff;
		cursor: pointer;
		text-align: center;
		border-radius: 10px 0 0;
		transition: all 1ms ease-in-out;
		@include media-breakpoint-down(lg) {
			top: 50%;
			bottom: 50%;
			left: 0;
		}

		svg {
			path {
				fill: $white;
			}
		}
		&.active {
			background-color: #f05a23;
			transition: all 1ms ease-in;
		}
	}
}
