#message-from-ceo {
	margin-top: 60px;
	margin-bottom: 60px;
	min-height: var(--app-height);

	@include breakpoint(xl-desktop) {
		margin-top: 80px;
		margin-bottom: 80px;
		// height: 470px;
		min-height: 100%;
	}
	@include breakpoint(xxl-desktop) {
		height: 100%;
	}

	.message-container {
		background-color: #f7f7f7;
		border-radius: 20px 0 0 0;
		// padding: 120px 0 120px;
		overflow: hidden;
		.content {
			margin: auto;
			.message {
				padding: 15px 15px 20px 15px;
				// height: calc(var(--app-height) - 250px);
				@include breakpoint(xl-desktop) {
					padding: 40px 40px 40px 0;
				}
			}
			h2 {
				text-align: left;
				font-size: 28px;
				line-height: 48px;
				margin-bottom: 10px;
				@include breakpoint(xl-desktop) {
					@include breakpoint('primary-heading');
					margin-bottom: 15px;
					font-size: 40px;
					line-height: 60px;
				}
				@include breakpoint(xxl-desktop) {
					@include breakpoint('primary-heading');
					margin-bottom: 15px;
					font-size: 40px;
					line-height: 60px;
				}
			}
			p {
				font-size: 16px;
				line-height: 26px;
				color: $black;
				@include media-breakpoint-down(xl) {
					text-align: left;
				}
				&:last-child {
					margin-bottom: 0;
				}
			}
			.content-title {
				@include media-breakpoint-down(xl) {
					padding-top: 5px;
				}
			}
			.name {
				font-weight: 400;
				font-size: 20px;
				line-height: 32px;
				margin-bottom: 0px;
				position: relative;
				&::before {
					content: '';
					width: 50px;
					background-color: #c0c0c0;
					height: 1px;
					position: absolute;
					top: -5px;
					left: 0px;
				}
			}
			span {
				font-size: 16px;
				line-height: 26px;
				color: $black;
				display: block;
				text-transform: uppercase;
			}
			img {
				object-fit: fill;
				width: 100%;
				height: 100%;
				@include breakpoint(xl-desktop) {
					height: 100%;
					max-width: 470px;
				}
			}
		}
	}
	.content-bio {
		height: 210px;
		@include media-breakpoint-down(xl) {
			height: calc(var(--app-height) - 460px);
			padding-right: 10px;
		}
		overflow: auto;
		padding-right: 20px;
		margin-bottom: 20px;
		&::-webkit-scrollbar {
			width: 5px;
		}
		/* Track */
		&::-webkit-scrollbar-track {
			background: #f1f1f1;
		}
		&::-webkit-scrollbar-thumb {
			background: #888;
		}
		&::-webkit-scrollbar-thumb:hover {
			background: #555;
		}
		ul {
			list-style: none;
			padding: 0 0 30px;
			margin-bottom: 0px;
			li {
				font-size: 16px;
				line-height: 26px;
				color: #4e4e4e;
				padding: 0;
				display: flex;
				&::before {
					content: '\2022';
					color: #f05a23;
					display: inline-block;
					font-size: 22px;
					margin-right: 10px;
				}
			}
		}
	}
}
