#highligted-project {
	background-repeat: no-repeat;
	background-size: cover;
	position: relative;
	z-index: 9;
	&::after {
		content: '';
		position: absolute;
		z-index: -1;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		background-color: rgba(0, 0, 0, 0.712);
	}
	.full-imgage {
		margin-bottom: 40px;
		img {
			width: 100%;
			object-fit: cover;
		}
	}
	.content {
		h3 {
			@include primary-heading();
			color: $white;
			margin-bottom: 40px;
			@include media-breakpoint-down(xl) {
				font-size: 32px;
				line-height: 48px;
			}
		}
		p {
			color: $white;
			font-size: 16px;
			line-height: 26px;
		}
	}
}

.residential-project {
	background-color: #fafafa;

	&.spacing-top-none {
		.mySwiper {
			@include media-breakpoint-down(xl) {
				padding-top: 0;
			}
			.custom-row {
				@include media-breakpoint-down(xl) {
					padding-top: 0;
				}
			}
		}
	}
	h2 {
		font-weight: 400;
		@include primary-heading();
		@include media-breakpoint-down(xl) {
			font-size: 32px;
			line-height: 48px;
		}
	}
	p {
		font-size: 16px;
		line-height: 26px;
	}
	.navigation {
		display: flex;
		justify-content: flex-start;
		position: relative;
		bottom: -50px;
		right: 0;
		float: right;
		z-index: 9;
		@include breakpoint(xl-desktop) {
			justify-content: flex-end;
			position: absolute;
			bottom: -70px;
			right: 40px;
		}
		@include breakpoint(xxl-desktop) {
			bottom: 0px;
		}
		a.prev {
			width: 50px;
			height: 50px;
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: rgba(255, 99, 96, 0.6);
			border-radius: 5px 0 0;
			transition: all 0.25s ease-in;
			svg {
				path {
					fill: #f05a23;
				}
			}
			&.active {
				background-color: #f05a23;
				transition: all 0.25s ease-in-out;
				svg {
					path {
						fill: #000;
					}
				}
			}
		}
		a.next {
			width: 50px;
			height: 50px;
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: rgba(255, 99, 96, 0.6);
			border-radius: 0 0 5px;
			transition: all 0.25s ease-in-out;
			svg {
				path {
					fill: #f05a23;
				}
			}
			&.active {
				background-color: #f05a23;
				transition: all 0.25s ease-in-out;
				svg {
					path {
						fill: #000;
					}
				}
			}
		}
	}
	.mySwiper {
		padding-top: 30px;
	}
	.custom-row {
		@include media-breakpoint-down(xl) {
			padding: 15px;
		}
		.project {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			.left-part {
				flex: 50%;
				max-width: 50%;
				@include media-breakpoint-down(xl) {
					flex: 100%;
					max-width: 100%;
					order: 2;
				}
				.content {
					position: relative;
					width: 470px;
					@include media-breakpoint-down(xl) {
						width: 100%;
					}
					h3 {
						font-size: 32px;
						line-height: 48px;
						@include breakpoint(xl) {
							@include primary-heading();
						}
						margin-bottom: 40px;
					}
					.icon-row {
						padding-top: 30px;
						display: flex;
						flex-wrap: wrap;
						.icon-item {
							display: flex;
							flex-wrap: wrap;
							margin-bottom: 30px;
							width: 50%;
							.icon {
								width: 60px;
								@include media-breakpoint-down(sm) {
									width: 50px;
								}
								img {
									position: relative;
									top: 5px;
								}
							}
							.icon-content {
								h4 {
									margin-bottom: 5px;
									font-weight: 600;
									font-size: 20px;
									@include media-breakpoint-down(sm) {
										font-size: 18px;
									}
									line-height: 26px;
								}
								span {
									font-weight: 400;
									font-size: 14px;
									@include media-breakpoint-down(sm) {
										font-size: 12px;
									}
									line-height: 24px;
									color: #4e4e4e;
								}
							}
						}
					}
				}
			}
			.right-part {
				flex: 50%;
				max-width: 50%;
				@include media-breakpoint-down(xl) {
					flex: 100%;
					max-width: 100%;
					order: 1;
				}
				@include media-breakpoint-down(md) {
					padding-top: 30px;
				}
				img {
					height: auto;
					width: 100%;
					object-fit: cover;
					margin-bottom: 30px;
					height: 230px;
					@include breakpoint(xl-desktop) {
						height: 400px;
						margin-bottom: 0;
					}
					@include breakpoint(xxl-desktop) {
						height: 650px;
					}
				}
			}
		}
	}
	&.without-items {
		.navigation {
			bottom: 0;
			@include media-breakpoint-down(sm) {
				bottom: -30px;
			}
		}
	}
	&.order {
		.project {
			.left-part {
				order: 2;
			}
			.right-part {
				order: 1;
			}
		}
	}
}
