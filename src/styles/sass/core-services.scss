.core-services {
	position: relative;
	.services-highlighted-row {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		padding: 60px 0 60px;
		justify-content: space-between;
		align-items: flex-start;
		.service {
			width: 350px;
			display: flex;
			flex-wrap: wrap;
			justify-content: center;
			@include media-breakpoint-down(xl) {
				width: 320px;
			}
			@include media-breakpoint-down(lg) {
				margin-bottom: 30px;
				flex-direction: column;
				align-items: center;
				width: 50%;
			}
			@include media-breakpoint-down(md) {
				width: 100%;
			}
			.icon {
				width: 100px;
				height: 100px;
				background-color: #fff;
				border-radius: 10px;
				text-align: center;
				box-sizing: border-box;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 30px;
				box-shadow: -1px -1px 4px #6c483a1a;
				img {
					width: 50px;
					height: auto;
				}
			}
			.icon-content {
				h4 {
					font-weight: 400;
					font-size: 26px;
					@include media-breakpoint-down(xl) {
						font-size: 22px;
					}
					line-height: 38px;
					color: #000000;
					text-align: center;
				}
			}
		}
	}
	h3 {
		font-weight: 400;
		font-size: 36px;
		line-height: 48px;
		text-align: center;
		color: #000000;
	}

	.service-list-row {
		display: flex;
		justify-content: center;
		padding: 60px 0 60px;
		border-bottom: 1px solid #dddfe3;
		width: 100%;
		margin: 0 auto;
		flex-direction: column;
		@include breakpoint('xl') {
			flex-direction: row;
		}

		.list {
			display: flex;
			align-items: center;
			width: 360px;
			justify-content: center;
			@include media-breakpoint-down('xl') {
				padding-bottom: 20px;
				width: 100%;
				justify-content: flex-start;
			}
			.icon {
				margin-right: 15px;
				width: 15px;
			}
			.content {
				padding-right: 30px;
				font-weight: 400;
				font-size: 20px;
				line-height: 32px;
				color: #c0c0c0;
			}
		}
	}
	.short-list-row {
		width: 100%;
		@include breakpoint('xl') {
			width: 920px;
		}

		margin: 0 auto;
		padding-top: 60px;
		display: flex;
		flex-wrap: wrap;
		h3 {
			@include primary-heading();
			text-align: left;
			@include media-breakpoint-down('xl') {
				font-size: 32px;
				line-height: 48px;
				padding-bottom: 60px;
			}
		}
		.left {
			width: 50%;
			@include media-breakpoint-down('xl') {
				width: 100%;
			}
		}
		.right {
			width: 50%;
			@include media-breakpoint-down('xl') {
				width: 100%;
			}
			.list-row {
				.list {
					display: flex;
					@include media-breakpoint-down('xl') {
						flex-wrap: nowrap;
						align-content: center;
					}
					padding-bottom: 20px;
					.icon {
						margin-right: 10px;
						width: 40px;
						margin-top: 5px;
					}
					.content {
						width: calc(100% - 40px);

						span {
							font-weight: 400;
							font-size: 20px;
							line-height: 32px;
							color: #000000;
						}
					}
				}
			}
		}
	}
	.support-bussiness-title {
		margin: 30px 0 0;
	}
	// &::before {
	// 	content: '';
	// 	position: absolute;
	// 	background-image: url('/dist/img/pattern.png');
	// 	right: 0;
	// 	width: 230px;
	// 	height: 100%;
	// 	top: 0;
	// }
}
