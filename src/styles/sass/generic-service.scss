#generic-service {
	// background-color: $bg2;
	position: relative;
	// &::before {
	//   content: "";
	//   position: absolute;
	//   background: url(/dist/img/pattern.png);
	//   top: 0;
	//   width: 233px;
	//   height: 100%;
	//   right: 0;
	// }
	.highlight-text {
		margin-bottom: 88px;
		h3 {
			color: $black;
			font-size: 32px;
			line-height: 48px;
			margin-bottom: 20px;
			@include breakpoint('xl') {
				@include primary-heading();
				margin-bottom: 40px;
			}
		}
		p {
			font-weight: 400;
			font-size: 16px;
			line-height: 1.75;
			color: #000000;
			margin-bottom: 30px;
		}
		ul {
			list-style: none;
			padding: 0 0 30px;
			margin-bottom: 30px;
			li {
				font-size: 16px;
				line-height: 26px;
				color: #4e4e4e;
				padding: 0;
				display: flex;
				&::before {
					content: '\2022';
					color: #f05a23;
					display: inline-block;
					font-size: 22px;
					margin-right: 10px;
				}
			}
		}
	}
	.main-service-item {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}
	.single-service-item {
		padding: 30px;
		background-repeat: no-repeat;
		background-size: cover;
		position: relative;
		z-index: 9;
		min-height: 575px;
		overflow: hidden;
		border-radius: 20px 0 0;
		width: 100%;
		margin-bottom: 30px;
		min-height: auto;
		@include breakpoint(lg) {
			min-height: 410px;
			width: 30.33%;
		}
		@include breakpoint(xxl-desktop) {
			min-height: 350px;
		}
		&::after {
			content: '';
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			background-color: #dddfe3d9;
			z-index: -1;
		}
		h3 {
			color: $black;
			font-size: 36px;
			line-height: 46px;
			margin-bottom: 50px;
			position: relative;
			&::after {
				content: '';
				position: absolute;
				background: rgba(255, 255, 255, 0.4);
				height: 1px;
				width: 100%;
				left: 0;
				bottom: -25px;
			}
		}
		p {
			color: $gray;
			font-size: 20px;
			line-height: 32px;
			margin-bottom: 10px;
		}
	}
	.swiper {
		@include media-breakpoint-down(sm) {
			padding-left: 15px;
		}
	}
	.swiper-slide {
		width: 90%;
		@include breakpoint(xl-desktop) {
			width: 540px;
		}
		@include breakpoint(xxl-desktop) {
			width: 640px;
		}
	}

	.swiper-pagination-progressbar {
		position: static !important;
		height: 4px !important;
		display: block;
		margin-top: 80px;
		@include media-breakpoint-down(sm) {
			width: 95%;
		}
	}
	.swiper-pagination-progressbar-fill {
		background-color: $base-orange;
	}
	.icon-group {
		margin-top: 77px;
		.icon-prev {
			width: 35px;
			cursor: pointer;
			&:hover {
				svg {
					path {
						fill: $base-orange;
					}
				}
			}
		}
		.icon-next {
			width: 35px;
			cursor: pointer;
			svg {
				path {
					fill: $black;
				}
			}
			&:hover {
				svg {
					path {
						fill: $base-orange;
					}
				}
			}
		}
	}
}

// Generic Service Icon Box Section

#service-icon {
	.page-header {
		h2 {
			text-align: left;
		}
		p {
			color: #424143;
			font-weight: 300;
			font-size: 16px;
			line-height: 32px;
		}
	}
	.icon-area-row {
		padding-top: 60px;
		.single-icon-item {
			.thumb {
				height: 100px;
			}
			h3 {
				font-weight: 400;
				font-size: 20px;
				line-height: 32px;
				margin: 15px 0px;
			}
			p {
				font-size: 16px;
				line-height: 26px;
				color: #4e4e4e;
			}
		}
	}
	&.normal-font-weight {
		p {
			font-weight: normal;
			font-size: 16px;
			line-height: 1.75;
		}
	}
}

.fun-fact {
	padding-bottom: 0;

	.single-funfact {
		display: flex;
		margin-bottom: 40px;
		height: 55px;
		align-items: center;
		max-width: 100%;
		@include media-breakpoint-up(xl) {
			border-right: 1px solid $border;
		}
		&:last-child {
			border: none;
		}
		.thumb {
			width: 50px;
			img {
				width: 45px;
				height: auto;
			}
		}
		.text {
			width: calc(100% - 50px);
			padding-left: 15px;
			h4 {
				font-weight: 600;
				font-size: 20px;
				line-height: 26px;
				margin-bottom: 2px;
			}
			span {
				font-weight: 400;
				font-size: 14px;
				line-height: 24px;
				color: #4e4e4e;
			}
		}
	}
	.content {
		p {
			font-size: 16px;
			line-height: 26px;
			color: #4e4e4e;
		}
	}
}
