section.about-us {
	height: 100%;
	h2 {
		font-size: 32px;
		@include breakpoint('xl') {
			@include primary-heading();
		}
	}
	p {
		font-size: 16px;
		line-height: 26px;
		color: #4e4e4e;
	}
	.thumb-col {
		display: flex;
		align-items: center;
		border-top: 1px solid #dddfe3;
		border-bottom: 1px solid #dddfe3;
		padding-top: 30px;
		padding-bottom: 30px;
		margin-bottom: 30px;
		.image {
			margin-right: 20px;
		}
	}
	.main-about-content {
		h4 {
			font-weight: 400;
			font-size: 20px;
			line-height: 32px;
		}
		.list {
			display: flex;
			flex-wrap: wrap;
			margin-bottom: 10px;
			.icon {
				margin-right: 15px;
			}
			.content {
				padding: 0;
				span {
					font-size: 16px;
					line-height: 26px;
					color: #4e4e4e;
				}
			}
		}
	}

	.tilter {
		display: block;
		position: relative;
		color: $orange;
		flex: none;
		perspective: 1000px;
	}

	.tilter * {
		pointer-events: none;
	}

	.tilter:hover,
	.tilter:focus {
		color: $orange;
		outline: none;
		.tilter__deco.tilter__deco--shine {
			background-color: #00000063;
		}
	}

	/*
.tilter__figure,
.tilter__deco,
.tilter__caption {
	will-change: transform;
}*/

	.tilter__figure,
	.tilter__image {
		margin: 0;
		width: 100%;
		height: 400px;
		object-fit: cover;
		display: block;
		@include breakpoint(xl-desktop) {
			width: 100%;
			height: 600px;
			object-fit: cover;
		}
		@include breakpoint(xxl-desktop) {
			width: 100%;
			height: 810px;
		}
	}

	.tilter__figure > * {
		transform: translateZ(0px); /* Force correct stacking order */
	}

	.smooth .tilter__figure,
	.smooth .tilter__deco--overlay,
	.smooth .tilter__deco--lines,
	.smooth .tilter__deco--shine div,
	.smooth .tilter__caption {
		transition: transform 0.2s ease-out;
	}

	.tilter__figure {
		position: relative;
	}

	.tilter__figure::before {
		content: '';
		position: absolute;
		width: 90%;
		height: 90%;
		top: 5%;
		left: 5%;
		box-shadow: 0 30px 20px rgba(35, 32, 39, 0.5);
	}

	.tilter__deco {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		overflow: hidden;
	}

	.tilter__deco--shine div {
		position: absolute;
		width: 200%;
		height: 200%;
		top: -50%;
		left: -50%;
		background-image: linear-gradient(
			45deg,
			rgba(0, 0, 0, 0.5) 0%,
			rgba(255, 255, 255, 0.25) 50%,
			transparent 100%
		);
	}

	.tilter__deco--lines {
		fill: none;
		stroke: $white;
		opacity: 0.6;
		stroke-width: 1.5px;
	}
	.tilter__caption {
		position: absolute;
		top: 0;
		left: 0;
		height: 100%;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.tilter__title {
		margin: 0;
		font-family: Josefin Sans;
		font-weight: normal;
		line-height: 56px;
		opacity: 0;
		transition: all 0.25s ease;
		font-size: 36px;
		@include media-breakpoint-down(md) {
			font-size: 22px;
		}
		color: $white;
		width: 300px;
		@include media-breakpoint-down(md) {
			width: 230px;
		}
		text-align: center;
		line-height: 55px;
		text-transform: uppercase;
	}

	.tilter__description {
		margin: 1em 0 0 0;
		font-size: 0.85em;
		letter-spacing: 0.15em;
	}

	/* Example 5 (line animating) */
	.tilter--5 .tilter__deco--lines path {
		stroke-dasharray: 1270;
		stroke-dashoffset: 1270;
		transition: stroke-dashoffset 0.7s;
	}
	.tilter--5:hover .tilter__caption h3 {
		opacity: 1;
		transition: all 0.25s ease;
	}
	.tilter--5:hover .tilter__deco--lines path {
		stroke-dashoffset: 0;
	}

	.tilter--5 .tilter__figure::before {
		box-shadow: none;
	}
	.follow-social {
		display: flex;
		flex-direction: column;
		align-items: center;
		span {
			font-weight: 400;
			font-size: 20px;
			line-height: 32px;
			margin-bottom: 20px;
			display: block;
		}
		.icon-group {
			display: flex;
			flex-wrap: wrap;
			width: 130px;
			align-items: center;
			justify-content: center;
			a {
				margin-right: 20px;
				&:last-child {
					margin-right: 0;
				}
				width: 40px;
				height: 40px;
				border: 1px solid #666;
				box-sizing: border-box;
				border-radius: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.25 ease-in-out;
				svg {
					path {
						fill: #666;
					}
				}
				&:hover {
					border: 1px solid $base-orange;
					transition: all 0.25 ease-in;
					svg {
						path {
							fill: $base-orange;
						}
					}
				}
			}
		}
	}
}
