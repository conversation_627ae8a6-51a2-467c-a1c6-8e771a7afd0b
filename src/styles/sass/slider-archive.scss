section#slider-archive {
	// background-image: url('/dist/img/circular-bg.jpg');
	// background-color: gray;
	background-repeat: no-repeat;
	background-size: cover;
	height: 100%;
	overflow: hidden;
	position: relative;
	z-index: 9;
	margin: 50px 0 0px;
	min-height: 430px;
	padding-top: 50px;
	@include breakpoint('xl') {
		margin: 120px 0 0px;
		padding-top: 0;
		min-height: 450px;
	}
	.for-desktop {
		position: relative;
		.timeline-desktop-nav {
			width: 80%;
			display: flex;
			justify-content: flex-end;
			position: absolute;
			bottom: 30px;
			.navigation {
				display: flex;
				opacity: 0;
				a.prev {
					width: 50px;
					height: 50px;
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: rgba(255, 99, 96, 0.6);
					border-radius: 5px 0 0;
					transition: all 0.25s ease-in;
					svg {
						path {
							fill: #f05a23;
						}
					}
					&.active {
						background-color: #f05a23;
						transition: all 0.25s ease-in-out;
						svg {
							path {
								fill: #000;
							}
						}
					}
				}
				a.next {
					width: 50px;
					height: 50px;
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: rgba(255, 99, 96, 0.6);
					border-radius: 0 0 5px;
					transition: all 0.25s ease-in-out;
					svg {
						path {
							fill: #f05a23;
						}
					}
					&.active {
						background-color: #f05a23;
						transition: all 0.25s ease-in-out;
						svg {
							path {
								fill: #000;
							}
						}
					}
				}
			}
		}
	}

	.for-mobile {
		.timeline-mobile-nav {
			margin-top: 30px;
			justify-content: center;
			display: flex;
			position: relative;
		}

		.navigation {
			display: flex;
			justify-content: flex-end;
			position: absolute;
			z-index: 9999;
			top: 0;
			bottom: 0;
			opacity: 0;
			a.prev {
				width: 50px;
				height: 50px;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: rgba(255, 99, 96, 0.6);
				border-radius: 5px 0 0;
				transition: all 0.25s ease-in;
				svg {
					path {
						fill: #f05a23;
					}
				}
				&.active {
					background-color: #f05a23;
					transition: all 0.25s ease-in-out;
					svg {
						path {
							fill: #000;
						}
					}
				}
			}
			a.next {
				width: 50px;
				height: 50px;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: rgba(255, 99, 96, 0.6);
				border-radius: 0 0 5px;
				transition: all 0.25s ease-in-out;
				svg {
					path {
						fill: #f05a23;
					}
				}
				&.active {
					background-color: #f05a23;
					transition: all 0.25s ease-in-out;
					svg {
						path {
							fill: #000;
						}
					}
				}
			}
		}
	}
	.circular-image {
		position: relative;
		transform: translate(55px);
		img {
			position: absolute;
			top: 0px;
			left: 0px;
		}
	}
	.carousel.for-desktop {
		padding-top: 70px;
		margin-top: 20px;
		padding-bottom: 50px;
	}
	.carousel_slide {
		display: flex;
		justify-content: center;
		padding-left: 50px;
		cursor: pointer;
	}
	.swiper-wrapper {
		position: relative;
		height: auto;
		@include breakpoint(xl) {
			height: 400px;
		}
		background-size: cover;
		.archive-year {
			background: #1d1d1d;
			color: #ffffff78;
			border-radius: 50px;
			width: 118px;
			height: 50px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: bold;
			font-size: 22px;
			line-height: 38px;
		}
	}
	.carousel_container {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
	.carousel_container2 {
		height: 100%;
		// overflow: hidden;
		display: flex;
		align-items: center;
	}
	.carousel_slide {
		opacity: 0;
		// transition-duration: 0ms !important;
		&.swiper-slide-active {
			transition: all 0.25s ease;
		}
	}
	.effect {
		&2 {
			opacity: 0.5;
		}
		&3 {
			opacity: 0.65;
		}
		&4 {
			opacity: 0.75;

			.archive-year {
				background: #f05a23 !important;
				color: #000000 !important;
			}
		}
		&5 {
			opacity: 0.75;
		}
		&6 {
			opacity: 0.75;
		}

		&_hide {
			opacity: 0;
			pointer-events: none;
		}
	}

	// Content Swiper
	.carousel-two {
		position: relative;
		overflow: hidden;
		width: 80%;
		border-radius: 50px;
		@include media-breakpoint-down('xl') {
			margin: 0 auto;
			width: 90%;
		}

		.top-panel {
			h3 {
				font-size: 32px;
				line-height: 48px;
				@include breakpoint(xl) {
					@include primary-heading();
				}
				color: #f05a23;
			}
		}
		.content-panel {
			background: rgba(254, 247, 244, 0.7);
			// padding: 50px;
			border-radius: 50px;
			.content {
				padding: 25px;
				.row {
					align-content: center;
					justify-content: space-between;
					p {
						margin-bottom: 0;
						font-weight: 400;
						font-size: 20px;
						line-height: 32px;
						color: #000000;
					}
					// .list-item {
					//   display: flex;
					//   flex-wrap: wrap;
					//   align-items: center;
					//   .icon {
					//     margin-right: 20px;
					//   }
					//   .item-content {
					//     span {
					//       color: #4e4e4e;
					//       font-size: 16px;
					//       line-height: 26px;
					//     }
					//   }
					// }
					// a.vist-link {
					//   text-align: right;
					//   font-weight: bold;
					//   font-size: 16px;
					//   line-height: 24px;
					//   color: #f05a23;
					//   display: block;
					//   position: relative;
					//   right: 15px;
					//   margin-bottom: 30px;
					//   @include media-breakpoint-down(xl) {
					//     text-align: left;
					//     right: 0;
					//   }
					// }
				}
			}
			.full-banner {
				height: 300px;
				width: 100%;
				object-fit: cover;
			}
		}
	}
	.m-slider-archive {
		height: 100px;
		@include media-breakpoint-down(xl) {
			height: 65px;
		}
		.swiper-slide-active {
			position: relative;
			top: 0;
			.item-of-year {
				background: #f05a23;
				border-radius: 50px;
				color: #000000;
				font-size: 26px;
				line-height: 38px;
				opacity: 1;
			}
		}
		.item-of-year {
			background: #1d1d1d;
			border-radius: 50px;
			width: 100px;
			height: 50px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 400;
			font-size: 26px;
			line-height: 38px;
			color: #ffffff;
			opacity: 0.3;
			margin: 0 auto;
		}
	}
}
