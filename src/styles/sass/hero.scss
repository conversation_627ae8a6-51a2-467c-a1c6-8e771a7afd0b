#hero {
    height: 100vh;
    &.half-sm-height {
        height: 350px;
        @include media-breakpoint-up(md) {
            height: 100vh;
        }
        .swiper-wrapper {
            height: 350px;
            @include media-breakpoint-up(md) {
                height: 100%;
            }
            .slider-item .slider-content {
                position: absolute;
                @include media-breakpoint-up(md) {
                    position: relative;
                }
            }
        }
    }
    @include breakpoint(lg) {
        height: 50vh;
    }
    @include breakpoint(xl) {
        height: 100vh;
    }
    overflow: hidden;
    position: relative;
    .scroll-logo {
        position: absolute;
        left: 20px;
        z-index: 9;
        top: 30%;
        bottom: 30%;
        @include media-breakpoint-down(xl) {
            display: none;
        }
    }

    .mySwiper {
        overflow: hidden;
    }
    .swiper-slide {
        .slider-item {
            height: 100vh;
            @include breakpoint(lg) {
                height: 50vh;
            }
            @include breakpoint(xl) {
                height: 100vh;
            }
            overflow: hidden;
            z-index: 2;
            .slider-image {
                background-size: cover;
                width: 100%;
                height: 100%;
                position: absolute;
                left: 0;
                top: 0;
                z-index: 1;
                @include transition(20s);
                &::after {
                    content: "";
                    position: absolute;
                    z-index: 9;
                    // background-color: #00000061;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    right: 0;
                }
            }
            .slider-content {
                position: absolute;
                width: 100%;
                height: 100%;
                left: 0;
                z-index: 9999;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                @include media-breakpoint-up(xl) {
                    left: 150px;
                    justify-content: flex-start;
                }
                h1 {
                    @include transition(0.6s);
                    opacity: 0;
                    -webkit-transform: translateY(40px);
                    -moz-transform: translateY(40px);
                    -ms-transform: translateY(40px);
                    -o-transform: translateY(40px);
                    transform: translateY(40px);
                    font-size: 38px;
                    font-weight: 300;
                    line-height: 48px;
                    text-align: center;
                    width: 330px;
                    // padding: 5px 20px 5px 10px;
                    // background: rgba(57, 57, 57, 0.6);
                    // backdrop-filter: blur(5px);
                    // border-radius: 5px;
                    @include breakpoint("xl") {
                        @include primary-heading();
                        text-align: left;
                        width: calc(100% + 40px);
                    }
                    color: #ffffff;
                    position: relative;
                    &::after {
                        content: "";
                        background-image: url('data:image/svg+xml, %3Csvg viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"%3E %3Cpath d="M0.672549 12.8829C0.818954 13.039 0.97451 13.039 1.13922 12.8829L6.87647 6.73423C7.04118 6.57808 7.04118 6.42192 6.87647 6.26577L1.13922 0.117117C0.97451 -0.039039 0.818954 -0.039039 0.672549 0.117117L0.123529 0.702703C-0.0411763 0.858859 -0.0411763 1.02477 0.123529 1.20045L5.09216 6.5L0.123529 11.7996C-0.0411763 11.9752 -0.0411763 12.1411 0.123529 12.2973L0.672549 12.8829Z" fill="%23ffffff"/%3E %3C/svg%3E');
                        position: absolute;
                        width: 50px;
                        height: 50px;
                        opacity: 0;
                        bottom: 20px;
                    }
                    &:hover {
                        &::after {
                            @include media-breakpoint-up(xl) {
                                opacity: 1;
                            }
                            animation-name: arrowmymove;
                            animation-duration: 0.25s;
                            animation-fill-mode: forwards;
                        }
                    }
                }
            }
        }
        .scroll-text {
            img {
                position: absolute;
                z-index: 9;
                &:nth-child(1) {
                    left: 23px;
                    top: 28%;
                }
                &:nth-child(2) {
                    left: 20px;
                    top: 45%;
                }
            }
        }
    }
    .swiper-slide-active {
        .slider-image {
            transform: scale(1.5) !important;
        }
        .slider-content {
            h1 {
                opacity: 1 !important;
                -webkit-transition-delay: 0.6s;
                -moz-transition-delay: 0.6s;
                -ms-transition-delay: 0.6s;
                -o-transition-delay: 0.6s;
                transition-delay: 0.6s;
                -webkit-transform: translateX(-0px);
                -moz-transform: translateX(-0px);
                -ms-transform: translateX(-0px);
                -o-transform: translateX(-0px);
                transform: translateX(-0px) !important;
                text-shadow: 0px 4px 4px #000000;
            }
        }
    }
    @include heroNavigate();
    .slider-mobile {
        display: none;
        @include media-breakpoint-down(sm) {
            display: block;
        }
    }
    .slider-desktop {
        display: none;
        @include media-breakpoint-up(sm) {
            display: block;
        }
    }
}
@keyframes arrowmymove {
    0% {
        transform: translate(5px, 28px);
    }
    100% {
        transform: translate(28px, 28px);
    }
}
