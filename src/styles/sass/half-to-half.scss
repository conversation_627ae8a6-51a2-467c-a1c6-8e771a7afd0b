#half-to-half {
	h2 {
		@include breakpoint('primary-heading');
		@include media-breakpoint-down('xl') {
			font-size: 32px;
			line-height: 48px;
		}
		&.mobile-title {
			display: none;
			@include media-breakpoint-down('sm') {
				display: block;
			}
		}
		&.desktop-title {
			display: none;
			@include media-breakpoint-up('sm') {
				display: block;
			}
		}
	}

	.ceritified {
		display: flex;
		flex-wrap: wrap;
		padding-top: 20px;
		.logo {
			flex: 1;
			@include media-breakpoint-up(sm) {
				flex: none;
			}
			padding-right: 20px;
			margin-bottom: 20px;
			img {
				height: auto;
			}
		}
	}
	p {
		font-size: 16px;
		line-height: 26px;
		color: #4e4e4e;
	}
	ul {
		list-style: none;
		padding: 0 0 30px;
		margin-bottom: 0px;
		li {
			font-size: 16px;
			line-height: 26px;
			color: #4e4e4e;
			padding: 0;
			display: flex;
			&::before {
				content: '\2022';
				color: #f05a23;
				display: inline-block;
				font-size: 22px;
				margin-right: 10px;
			}
		}
	}
}
