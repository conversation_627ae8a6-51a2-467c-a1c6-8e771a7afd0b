#video-banner {
	width: 100%;
	height: 500px;
	position: relative;
	z-index: 9;
	background-size: 1920px 650px;
	background-repeat: no-repeat;
	.video-container {
		align-items: flex-end;
		display: flex;
		height: 500px;
		padding-bottom: 50px;
		.video {
			display: flex;
			align-items: center;
			.icon {
				margin-right: 20px;
				cursor: pointer;
				svg {
					path {
						transition: all 0.25 ease-in-out;
					}
				}
				&:hover {
					svg {
						path {
							fill: #f05a23;
							transition: all 0.25 ease-in;
						}
					}
				}
			}
			.content {
				h4 {
					font-weight: 400;
					font-size: 26px;
					line-height: 38px;
					color: #ffffff;
				}
			}
		}
	}
	&::after {
		content: '';
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		background-color: #000;
		opacity: 0.5;
		top: 0;
		z-index: -1;
	}
}
#banner-video {
	height: 100%;
	@include media-breakpoint-up(xl) {
		height: 100vh;
	}
	overflow: hidden;
	video {
		object-fit: fill;
		width: 100%;
		height: auto;
	}
}
