#blog-slider {
	overflow: hidden;

	h2 {
		font-size: 32px;
		line-height: 48px;
		@include breakpoint('xl') {
			@include primary-heading();
		}
	}
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.navigation {
			display: flex;
			justify-content: flex-end;
			a.prev {
				width: 50px;
				height: 50px;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: #f7f7f7;
				border: 1px solid #f05a23;
				border-radius: 5px 0 0;
				transition: all 0.25s ease-in;
				svg {
					path {
						fill: #f05a23;
					}
				}
				&.active {
					background-color: #f05a23;
					transition: all 0.25s ease-in-out;
					svg {
						path {
							fill: #000;
						}
					}
				}
			}
			a.next {
				width: 50px;
				height: 50px;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: #f7f7f7;
				border: 1px solid #f05a23;
				border-radius: 0 0 5px;
				transition: all 0.25s ease-in-out;
				svg {
					path {
						fill: #f05a23;
					}
				}
				&.active {
					background-color: #f05a23;
					transition: all 0.25s ease-in-out;
					svg {
						path {
							fill: #000;
						}
					}
				}
			}
		}
	}

	.blog-container {
		display: flex;
		width: 100%;
		column-gap: 25px;
		padding-top: 65px;
		@include media-breakpoint-down(xl) {
			padding-top: 0;
		}
		overflow: hidden;

		.blog-item {
			box-sizing: content-box;
			display: flex;
			flex-direction: column;
			margin-bottom: 30px;
			border-radius: 12px 5px 5px 12px;
			overflow: hidden;
			@include media-breakpoint-down('xl') {
				margin-top: 30px;
				flex-wrap: wrap;
			}
			.thumb {
				width: 100%;
				img {
					width: 100%;
					height: 265px;
					object-fit: cover;
				}
			}
			.content {
				width: 100%;
				padding: 20px 0;
				position: relative;
				.meta-date {
					color: #4e4e4e;
					font-size: 14px;
					line-height: 24px;
				}
				h4 {
					font-weight: 500;
					font-size: 20px;
					line-height: 32px;
					transition: all 0.25ms ease-in-out;

					&:hover {
						transition: all 0.25ms ease-in;
						color: $base-orange;
					}
				}
				p {
					font-size: 16px;
					line-height: 26px;
					color: #4e4e4e;

					@include media-breakpoint-down('xl') {
						padding-bottom: 30px;
					}
				}
				.read-more {
					position: absolute;
					left: 0;
					bottom: 0;
					border-top: 1px solid #dddfe3;
					width: 100%;
					padding: 15px 30px;
					font-size: 14px;
					line-height: 24px;
					color: #4e4e4e;

					svg {
						position: absolute;
						right: 20px;
						top: 30%;
					}
					&::after {
						content: '';
						position: absolute;
						background-color: #dddfe3;
						width: 1px;
						height: 99%;
						top: 0;
						right: 55px;
					}
					&::before {
						content: '';
						border-radius: 0 0 5px;
						position: absolute;
						background-color: transparent;
						transition: all 0.25s ease-in-out;
						width: 56px;
						height: 99%;
						top: 0;
						right: 0;
					}
					&:hover {
						&::before {
							background-color: #f05a23;
							transition: all 0.25s ease-in;
						}
					}
				}
			}
		}
	}
}

// Blog Page
#blog {
	&.section-spacing {
		padding-top: 50px;
	}
	.single-blog-column {
		margin-bottom: 60px;
		.image {
			img {
				border-radius: 20px 0 0 0;
				max-height: 400px;
				object-fit: cover;
			}
		}
		.content {
			padding-left: 20px;
			@include media-breakpoint-down(xl) {
				padding-top: 20px;
				padding-left: 0;
			}
			.post-date {
				font-size: 14px;
				line-height: 24px;
				color: #4e4e4e;
			}
			h2 {
				a {
					font-weight: 400;
				}
				font-size: 32px;
				line-height: 48px;
			}
			p {
				font-size: 16px;
				line-height: 26px;
				color: $black;
			}
		}
	}
	.three-blog-column {
		@include media-breakpoint-up(lg) {
			display: flex;
			flex-wrap: wrap;
		}
		margin-bottom: 30px;
		.image {
			margin-bottom: 30px;
			img {
				border-radius: 20px 0 0 0;
				width: 410px;
				height: 270px;
				object-fit: cover;
			}
		}
		.content {
			.post-date {
				font-size: 14px;
				line-height: 24px;
				color: #4e4e4e;
			}
			h2 {
				a {
					font-weight: 400;
				}
				font-size: 20px;
				line-height: 32px;
			}
			p {
				font-size: 16px;
				line-height: 26px;
				color: #4e4e4e;
				margin-bottom: 0;
			}
		}
	}
	.pagination {
		padding: 50px 0 0px;
		@include media-breakpoint-down(xl) {
			padding: 0;
		}
		.page-link {
			border: none;
			padding: 10px 20px;
			color: #c0c0c0;
			&.active {
				color: #f05a23;
			}
		}
		.prev {
			a {
				background-color: #f05a23;
				border-radius: 10px 0px 0px 0px;
			}
		}
		.next {
			a {
				background-color: #f05a23;
				border-radius: 10px 0px 0px 0px;
			}
		}
	}
}

.foundation__day {
	.swiper-slide {
		position: relative;
		img {
			object-fit: cover;
			width: 100%;
			height: 320px;
			@include breakpoint(xl-desktop) {
				height: 500px;
			}
			@include breakpoint(xxl-desktop) {
				height: 615px;
			}
		}
		.slide-content {
			opacity: 0;
			position: relative;
			@include media-breakpoint-up(lg) {
				position: absolute;
			}
			bottom: 0px;
			width: 100%;
			background: rgba(254, 247, 244, 0.247);
			border-radius: 0;
			padding: 10px 15px 0;
			left: 0;
			@include media-breakpoint-down(lg) {
				padding: 20px;
				width: 100%;
				left: 0;
				bottom: 0;
				border-radius: 10px 0 0 0;
			}
			h3 {
				font-weight: 400;
				@include media-breakpoint-down(lg) {
					max-width: 400px;
				}
				font-size: 28px;
				line-height: 46px;
			}
			.cta {
				// @include media-breakpoint-up(lg) {
				// 	margin-left: 30px;
				// }
				padding-top: 15px;
				&::after {
					top: 10px;
				}
			}
		}
		&.swiper-slide-active {
			.slide-content {
				opacity: 1;
			}
		}
	}
	@include heroNavigate();
}
