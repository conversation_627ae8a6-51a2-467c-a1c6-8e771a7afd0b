#career-opportunties {
	.section-page-header {
		margin-bottom: 60px;
	}

	.search-bar {
		border: 1px solid rgba(0, 0, 0, 0.1);
		box-sizing: border-box;
		border-radius: 5px;
		padding: 10px 30px;

		@include media-breakpoint-down(xl) {
			padding: 10px;
		}

		margin-bottom: 30px;

		.categories {
			width: 200px;

			@include media-breakpoint-down(xl) {
				width: 150px;
			}

			height: 100%;
			position: relative;

			select {
				width: 130px;
				border: none;
				font-weight: 400;
				font-size: 14px;
				line-height: 24px;
				color: #4e4e4e;
				outline: none;
			}

			&::after {
				content: '';
				position: absolute;
				height: 1px;
				top: 10px;
				right: -15px;
				display: inline-block;
				width: 61px;
				transform: rotate(90deg);
				background-color: $border;
			}
		}

		.search-job {
			width: calc(100% - 200px);

			@include media-breakpoint-down(xl) {
				width: calc(100% - 150px);
			}

			position: relative;

			input {
				height: 40px;
				width: 100%;
				border: none;
				outline: none;
				padding-left: 30px;
				font-weight: 400;
				font-size: 14px;
				line-height: 24px;
				color: #4e4e4e;
			}

			&::after {
				content: '';
				position: absolute;
				background-image: url('data:image/svg+xml, %3Csvg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z" stroke="%234E4E4E" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/%3E%3Cpath d="M20.9999 21L16.6499 16.65" stroke="%234E4E4E" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/%3E%3C/svg%3E');
				background-repeat: no-repeat;
				top: 8px;
				left: 0;
				width: 30px;
				height: 30px;
			}
		}
	}

	.job-post {
		.job-item {
			border: 1px solid #dddfe3;
			box-sizing: border-box;
			border-radius: 10px 0px 0px 0px;
			padding: 20px;
			margin-bottom: 20px;

			.navigate {
				text-align: right;

				a {
					background-color: #f05a23;
					border-radius: 0px 0px 10px 0px;
					width: 50px;
					height: 50px;
					display: inline-block;
					line-height: 48px;
					text-align: center;

					&:hover {
						background-color: rgba(255, 99, 96, 0.6);
					}
				}
			}

			h3 {
				a {
					font-weight: 400;
					font-size: 26px;
					line-height: 38px;
					color: #4e4e4e;
					margin-bottom: 20px;
				}
			}

			.group-item {
				@include media-breakpoint-down(xl) {
					justify-content: flex-start;
				}

				.item {
					border: 1px solid #dddfe3;
					box-sizing: border-box;
					border-radius: 5px 0px 0px 0px;
					background: #fafafa;
					padding: 10px;
					font-weight: 400;
					font-size: 14px;
					line-height: 24px;
					color: #4e4e4e;
					display: flex;
					align-items: center;
					margin-right: 15px;

					@include media-breakpoint-down(xl) {
						margin-bottom: 15px;
					}

					svg {
						margin-right: 5px;
					}
				}
			}
		}
	}
}

#drop-resume {
	background-color: #fef7f4;
	padding: 80px 0 80px;

	h3 {
		font-weight: 400;
		font-size: 36px;
		line-height: 46px;
		margin-bottom: 30px;
	}

	.primay-button {
		background-color: $base-orange;
		border-radius: 5px 0px 0px 0px;
		width: 245px;
		height: 50px;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 13px 50px;
		text-transform: uppercase;
		color: #000000;
		font-weight: bold;
		font-size: 16px;
		line-height: 24px;
		transition: all 1ms ease-in-out;

		@include media-breakpoint-up(xl) {
			float: right;
		}

		&:hover {
			transition: all 1ms ease-in;
			background-color: $gray;
			color: $white;
		}
	}
}

#application-form {
	background-color: #fafafa;

	.page-header {
		max-width: 530px;
		margin: 0 auto;
		text-align: center;
		margin-bottom: 60px;

		h2 {
			font-size: 36px;
			line-height: 48px;

			@include breakpoint('xl') {
				@include primary-heading();
			}

			color: #000000;
			margin-bottom: 20px;

			p {
				font-size: 16px;
				line-height: 26px;
				color: #4e4e4e;
			}
		}
	}

	form {

		input,
		select {
			background: #ffffff;
			border: 1px solid rgba(0, 0, 0, 0.1);
			box-sizing: border-box;
			border-radius: 4px;
			width: 100%;
			height: 50px;
			margin-bottom: 30px;
			padding: 10px;
			color: #4e4e4e;
		}

		.fake-file-input {
			border: 1px dashed #f05a23;
			box-sizing: border-box;
			border-radius: 5px;
			height: 178px;
			position: relative;

			.fake-content {
				width: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				position: absolute;
				height: 100%;

				h3 {
					font-weight: 400;
					font-size: 20px;
					line-height: 32px;
					text-align: center;
				}
			}

			input {
				height: 178px;
				opacity: 0;
				cursor: pointer;
			}
		}

		#submit {
			margin-top: 30px;
			width: 254px;
			height: 50px;
			background: #f05a23;
			border-radius: 5px 0px 0px 0px;
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 13px 50px;
			text-transform: uppercase;
			font-size: 16px;
			line-height: 24px;
			font-weight: bold;
			transition: all 1ms ease-in-out;

			&:hover {
				background-color: $gray;
				color: $white;
				transition: all 1ms ease-in;
			}
		}

		#verify-email {
			background-color: #fff;
			padding: 20px;

			span {
				color: #4e4e4e;
				font-weight: 400;
				font-size: 16px;
				line-height: 26px;
				margin-bottom: 30px;
			}

			.form-row {
				display: flex;
				margin-top: 20px;

				@include media-breakpoint-down(lg) {
					flex-direction: column;
				}
			}

			.verify-email {
				@include media-breakpoint-down(xl) {
					margin-bottom: 20px;
				}

				input {
					margin-bottom: 0;
				}
			}

			.col-lg-2 {
				@include media-breakpoint-down(lg) {
					display: flex;
					justify-content: center;
				}
			}
		}

		#applicate-form-resume {
			h3 {
				font-weight: 400;
				font-size: 26px;
				line-height: 38px;
				color: #000000;
				margin-bottom: 30px;
			}

			&.disible {

				input,
				select {
					color: rgba(78, 78, 78, 0.5);
				}

				.fake-file-input {
					border: 1px dashed #c0c0c0;

					.fake-content {
						svg {
							path {
								fill: rgba(0, 0, 0, 0.5);
							}
						}

						h3 {
							color: rgba(0, 0, 0, 0.5);
						}
					}
				}

				#submit {
					background-color: #dddfe3;
					color: #424143;
				}
			}
		}

		.arrow {
			position: relative;

			&::after {
				position: absolute;
				content: '';
				background-image: url('data:image/svg+xml, %3Csvg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath fill-rule="evenodd" clip-rule="evenodd" d="M16.0773 6.07739L17.2559 7.2559L9.99994 14.5118L2.74402 7.2559L3.92253 6.07739L9.99994 12.1548L16.0773 6.07739Z" fill="%234E4E4E"/%3E%3C/svg%3E');
				right: 0;
				width: 50px;
				height: 50px;
				background-repeat: no-repeat;
				top: 15px;
			}
		}

		input[type='submit'] {
			color: #000000;
		}

		select {
			/* for Firefox */
			-moz-appearance: none;
			/* for Chrome */
			-webkit-appearance: none;
		}

		/* For IE10 */
		select::-ms-expand {
			display: none;
		}
	}
}

#application-form form #applicate-form-resume.disible input,
#application-form form #applicate-form-resume.disible select {
	color: #7b7b7b;
}