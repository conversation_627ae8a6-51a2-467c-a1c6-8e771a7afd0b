#cheif-message {
	.cheif-message-archive {
		width: 100%;
		@include breakpoint(xl-desktop) {
			width: 930px;
		}
		margin: 0 auto 40px auto;
		height: auto;
		overflow: hidden;
		position: relative;
		border-bottom: 2px solid #dddfe3;
		.swiper-wrapper {
			padding-bottom: 10px;
			.swiper-slide {
				font-weight: bold;
				font-size: 14px;
				line-height: 24px;
				text-transform: uppercase;
				color: #4e4e4e;
				cursor: pointer;
				&.swiper-slide-active {
					color: $base-orange;
					padding-left: 20px;
				}
			}
		}
		.cheif-button-prev {
			transform: rotateY(180deg);
			position: absolute;
			top: 0;
			left: 0px;
			cursor: pointer;
			z-index: 9;
		}
		.cheif-button-next {
			position: absolute;
			top: 0;
			right: 0;
			cursor: pointer;
			z-index: 9;
		}
	}
	.cheif-message-content {
		width: 100%;
		@include breakpoint(xl-desktop) {
			width: 930px;
		}
		margin: 0 auto;
		overflow: hidden;
		border-bottom: 2px solid #dddfe3;
		h2 {
			@include breakpoint('primary-heading');
			@include media-breakpoint-down(lg) {
				font-size: 32px;
				line-height: 48px;
			}
		}
		p {
			font-size: 16px;
			line-height: 26px;
			color: #4e4e4e;
			margin-bottom: 15px;
		}
	}
	.bio {
		width: 100%;
		text-align: left;
		padding-top: 60px;
		@include breakpoint(xl) {
			width: 930px;
			margin: 0 auto;
		}
		h3 {
			font-size: 26px;
			font-weight: 400;
		}
		h4 {
			font-size: 26px;
			font-weight: 400;
		}
		.social {
			@include media-breakpoint-down(xl) {
				padding-top: 30px;
			}
			li {
				a {
					padding: 15px;
				}
			}
		}
	}
}
