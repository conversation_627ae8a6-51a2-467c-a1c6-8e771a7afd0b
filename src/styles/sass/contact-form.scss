#contact-form {
	background-color: #fafafa;
	h3 {
		color: #000000;
		text-align: center;
		font-size: 32px;
		line-height: 48px;
		@include breakpoint('xl') {
			@include primary-heading();
		}
	}
	p {
		font-weight: 400;
		font-size: 20px;
		line-height: 32px;
		color: #4e4e4e;
		text-align: center;
	}
	form {
		margin-top: 60px;
		.form-group {
			margin-bottom: 30px;
			input,
			select {
				width: 100%;
				height: 50px;
				border: 1px solid rgba(0, 0, 0, 0.1);
				box-sizing: border-box;
				border-radius: 4px;
				padding: 12px 15px;
				color: #4e4e4e;
				transition: all 1ms ease-in-out;
				&:focus {
					outline: none;
					border: 2px solid #f05a23;
					transition: all 1ms ease-in;
				}
			}
			input::placeholder {
				color: #4e4e4e;
			}
			select {
				/* for Firefox */
				-moz-appearance: none;
				/* for Chrome */
				-webkit-appearance: none;
			}
			/* For IE10 */
			select::-ms-expand {
				display: none;
			}

			&.arrow {
				position: relative;
				&::after {
					position: absolute;
					content: '';
					background-image: url('data:image/svg+xml, %3Csvg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath fill-rule="evenodd" clip-rule="evenodd" d="M16.0773 6.07739L17.2559 7.2559L9.99994 14.5118L2.74402 7.2559L3.92253 6.07739L9.99994 12.1548L16.0773 6.07739Z" fill="%234E4E4E"/%3E%3C/svg%3E');
					right: 0;
					width: 50px;
					height: 50px;
					background-repeat: no-repeat;
					top: 15px;
				}
			}
			textarea {
				width: 100%;
				border: 1px solid rgba(0, 0, 0, 0.1);
				box-sizing: border-box;
				border-radius: 4px;
				padding: 12px 15px;
				color: #4e4e4e;
				transition: all 0.25s ease-in-out;
				&:focus {
					outline: none;
					border: 2px solid #f05a23;
					transition: all 0.25s ease-in;
				}
			}
			input[type='submit'] {
				width: 215px;
				height: 50px;
				margin-top: 30px;
				background: #f05a23;
				border-radius: 5px 0px 0px 0px;
				text-transform: uppercase;
				color: $black;
				border: none;
				transition: all 0.25s ease-in-out;
				font-weight: 500;
				font-size: 16px;
				&:hover {
					background-color: $gray;
					color: $white;
					transition: all 0.25s ease-in;
				}
			}
		}
	}
}

#contact-map {
	.custom-row {
		display: flex;
		flex-wrap: wrap;
		.left-side {
			background-repeat: no-repeat;
			background-size: cover;
			max-width: 100%;
			flex: 100%;
			@include media-breakpoint-up(xl) {
				max-width: 50%;
				flex: 50%;
			}

			.custom-container {
				height: 100%;
				position: relative;
				.map-heading {
					@include media-breakpoint-up(xl) {
						padding: 100px 0;
						@include primary-heading();
						text-align: left;
					}
					text-align: center;
					padding: 30px;
					font-size: 38px;
					line-height: 48px;
					color: $white;
				}
				.box-contact-info {
					background-color: #ffffffb8;
					height: auto;
					width: 100%;
					bottom: 0;
					padding: 30px;
					padding: 30px 30px 0;
					@include media-breakpoint-up(xl) {
						border-radius: 20px 0px 0px 0px;
						position: absolute;
						height: 480px;
						padding: 50px 60px 0;
					}
					.address {
						margin-bottom: 40px;
						.item {
							display: flex;
							flex-wrap: wrap;
							padding-bottom: 40px;
							&:last-child {
								padding-bottom: 0;
							}
							.icon {
								max-width: 30px;
							}
							.content {
								max-width: calc(100% - 30px);
								padding-left: 20px;
								span {
									font-size: 16px;
									line-height: 26px;
								}
							}
						}
					}
					.social-icons {
						@include media-breakpoint-down(xl) {
							padding-bottom: 30px;
						}
						.icon {
							width: 40px;
							height: 40px;
							margin-right: 15px;
							background-color: #f05a2344;
							border-radius: 100%;
							display: flex;
							align-items: center;
							justify-content: center;
							transition: all 0.25s ease;
							&:hover {
								background-color: #f05a23;
								transition: all 0.25s ease;
								svg {
									path {
										fill: #fff;
									}
								}
							}
						}
					}
				}
			}
		}
		.right-side {
			max-width: 50%;
			flex: 100%;
			@include media-breakpoint-down(xl) {
				max-width: 100%;
				flex: 50%;
			}
			text-align: right;
			img {
				width: 100%;
				object-fit: cover;
				max-height: 550px;
				@include breakpoint(xl-desktop) {
					max-height: 550px;
				}
				@include breakpoint(xxl-desktop) {
					max-height: 650px;
				}
			}
		}
	}
}
