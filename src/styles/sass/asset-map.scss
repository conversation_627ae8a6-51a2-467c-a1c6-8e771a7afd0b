#asset-map {
	// @include breakpoint(xxl-desktop) {
	// 	background-color: #484a4e;
	// }
	// @include media-breakpoint-up(lg) {
	// 	.asset-map-mobile {
	// 		display: none;
	// 	}
	// }
	// @include media-breakpoint-down(lg) {
	// 	.asset-map-mobile {
	// 		display: block;
	// 	}
	// 	.asset-map-desktop {
	// 		display: none;
	// 	}
	// }

	.asset-large-map-desktop {
		display: none;
	}
	.asset-map-desktop {
		display: none;
	}
	.asset-map-mobile {
		display: block;
	}
	@include breakpoint(xl) {
		.asset-large-map-desktop {
			display: none;
		}
		.asset-map-desktop {
			display: block;
		}
		.asset-map-mobile {
			display: none;
		}
	}
	@include breakpoint(xxl-desktop) {
		.asset-large-map-desktop {
			display: block;
		}
		.asset-map-desktop {
			display: none;
		}
		.asset-map-mobile {
			display: none;
		}
	}
	img {
		width: 100%;
	}
	background-size: cover;
	background-repeat: no-repeat;
	overflow: hidden;
	.map-svg {
		text-align: center;
		position: relative;
		width: 100%;
		height: auto;
		@include breakpoint(xl) {
			width: 650px;
		}
		display: flex;
		margin: 0 auto;
		img {
			height: 100%;
			width: 100%;
		}
		.point {
			&.one {
				top: 180px;
				left: 184px;
				@include media-breakpoint-down(sm) {
					top: 80px;
					left: 100px;
				}
			}
			&.two {
				top: 222px;
				left: 215px;
				@include media-breakpoint-down(sm) {
					top: 110px;
					left: 120px;
				}
			}
			&.three {
				top: 214px;
				left: 394px;
				@include media-breakpoint-down(sm) {
					top: 170px;
					left: 140px;
				}
			}
			&.four {
				top: 310px;
				left: 237px;
				@include media-breakpoint-down(sm) {
					top: 100px;
					left: 190px;
				}
			}
			&.five {
				top: 310px;
				left: 320px;
				@include media-breakpoint-down(sm) {
					top: 150px;
					left: 190px;
				}
			}
			&.six {
				top: 280px;
				left: 353px;
				z-index: 8;
				@include media-breakpoint-down(sm) {
					top: 170px;
					left: 190px;
				}
			}
			&.seven {
				left: 134px;
				top: 615px;
				@include media-breakpoint-down(sm) {
					top: 270px;
					left: 80px;
				}
			}
			&.eight {
				left: 114px;
				top: 520px;
				@include media-breakpoint-down(sm) {
					top: 290px;
					left: 100px;
				}
			}
			&.nine {
				left: 150px;
				top: 554px;
				@include media-breakpoint-down(sm) {
					top: 320px;
					left: 80px;
				}
			}
			&.ten {
				left: 236px;
				top: 616px;
				@include media-breakpoint-down(sm) {
					top: 320px;
					left: 130px;
				}
			}
			&.eleven {
				right: 30px;
				top: 550px;
				@include media-breakpoint-down(sm) {
					top: 300px;
					right: 0px;
				}
			}
			&.twelve {
				right: 77px;
				top: 550px;
				@include media-breakpoint-down(sm) {
					top: 300px;
					right: 30px;
				}
			}
			&.thirty {
				right: 127px;
				top: 563px;
				@include media-breakpoint-down(sm) {
					top: 300px;
					right: 50px;
				}
			}
			&.fourten {
				right: 115px;
				top: 620px;
				@include media-breakpoint-down(sm) {
					top: 350px;
					right: 50px;
				}
			}
			position: absolute;
			.white-bullet {
				border: 1px solid #fff;
				width: 15px;
				height: 15px;
				border-radius: 100%;
				background-color: #fff;
				position: absolute;
				top: 36%;
				left: 0;
				right: 0;
				margin-left: auto;
				margin-right: auto;
				z-index: 9;
			}
			.blue-wiling {
				background-color: #118dff;
				width: 50px;
				height: 50px;
				border-radius: 100%;
				position: relative;
				z-index: 9;
				backdrop-filter: blur(4px);
				opacity: 0.45;
				border: 1px solid #118dff;
			}
		}
	}
	.item-row {
		display: flex;
		justify-content: center;
		column-gap: 30px;
		@include media-breakpoint-down('xl') {
			flex-direction: column;
		}
		.item-map {
			display: flex;
			align-items: center;
			@include media-breakpoint-down('xl') {
				padding-bottom: 20px;
			}
			.icon {
				margin-right: 15px;
			}
			.content {
				span {
					font-weight: 400;
					font-size: 26px;
					line-height: 38px;
					color: #ffffff;
				}
			}
		}
	}
}
