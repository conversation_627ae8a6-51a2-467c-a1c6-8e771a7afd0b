.team {
	.modal {
		visibility: hidden;
		height: 0;
		opacity: 0;
		&.open {
			visibility: visible;
			height: 100%;
			opacity: 1;
			position: fixed;
			top: 0;
			right: 0;
			left: 0;
			bottom: 0;
			z-index: 9999;
			-webkit-animation-duration: 1s;
			animation-duration: 1s;
			-webkit-animation-fill-mode: both;
			animation-fill-mode: both;
			@include media-breakpoint-down('md') {
				overflow: hidden;
				height: 100vh;
			}

			.modal-dialog {
				position: relative;
				width: auto;
				max-width: 1140px;
				margin: auto;
				.modal-content {
					position: relative;
					display: -webkit-box;
					display: -ms-flexbox;
					display: flex;
					-webkit-box-orient: vertical;
					-webkit-box-direction: normal;
					-ms-flex-direction: column;
					flex-direction: column;
					width: 100%;
					pointer-events: auto;
					background-color: #fff;
					background-clip: padding-box;
					border: 1px solid rgba(0, 0, 0, 0.2);
					border-radius: 0.3rem;
					outline: 0;

					position: relative;
					@include media-breakpoint-down(md) {
						height: 100vh;
					}
					.custom-row {
						display: flex;
						flex-wrap: wrap;
						@include media-breakpoint-down('md') {
							// height: 100vh;
							justify-content: space-between;
						}
						.left {
							flex: 100%;

							@include media-breakpoint-up('md') {
								order: 2;
								flex: 1 1 40%;
							}
							.member-img {
								position: relative;
								.name-section {
									position: absolute;
									bottom: 0;
									width: 100%;
									height: 100%;
									padding: 10px;
									background: linear-gradient(
										360deg,
										#4b4b4b 0%,
										rgba(75, 75, 75, 0) 33.54%
									);
									display: flex;
									flex-direction: column;
									justify-content: flex-end;
									h3 {
										font-weight: 400;
										font-size: 20px;
										line-height: 32px;
										color: #ffffff;
										margin-top: 0;
										margin-bottom: 5px;
									}
									span {
										font-weight: 400;
										font-size: 14px;
										line-height: 24px;
										color: #fff;
										margin-bottom: 0px;
									}
								}
							}
						}
						.right {
							flex: 100%;
							@include media-breakpoint-up('md') {
								order: 1;
								flex: 1 1 60%;
							}
						}
					}

					.details {
						padding: 40px 20px 40px 40px;
						@include media-breakpoint-down(md) {
							padding: 0px 15px 0px 15px;
						}
						h3 {
							margin-top: 0;
							font-weight: 400;
							font-size: 36px;
							line-height: 46px;
							margin-bottom: 15px;
							@include media-breakpoint-down('md') {
								font-size: 32px;
								line-height: 46px;
							}
						}

						span {
							font-weight: 400;
							font-size: 20px;
							line-height: 32px;
							color: #4e4e4e;
							margin-bottom: 20px;
							display: block;
							border-bottom: 1px solid #dddfe3;
							padding-bottom: 20px;
						}
						p {
							font-weight: 400;
							font-size: 14px;
							line-height: 24px;
							color: #4e4e4e;
							padding-right: 20px;
						}
						.overview {
							// background-color: red;
							overflow: auto;
							position: relative;

							@include media-breakpoint-up(md) {
								max-height: 280px;
							}
							p {
								@include media-breakpoint-down(md) {
									padding-top: 10px;
									&:last-child {
										padding-bottom: 30px;
									}
								}
							}
							&::-webkit-scrollbar {
								width: 5px;
							}
							/* Track */
							&::-webkit-scrollbar-track {
								background: #f1f1f1;
							}
							&::-webkit-scrollbar-thumb {
								background: #888;
							}
							&::-webkit-scrollbar-thumb:hover {
								background: #555;
							}
						}
					}
					.member-img {
						.close-modal {
							position: absolute;
							background: #f05a23;
							width: 60px;
							z-index: 9999;
							height: 60px;
							border-radius: 100%;
							display: flex;
							align-items: center;
							justify-content: center;
							right: 0;
							top: 0;
							transition: all 0.25s ease-in-out;
							@include media-breakpoint-down('md') {
								height: 50px;
								width: 50px;
								top: 0;
								right: 0;
							}
							margin: 20px;
							cursor: pointer;
							&:hover {
								background: $white;
								transition: all 0.25s ease-in;
								svg {
									path {
										fill: $black;
									}
								}
							}
						}
						img {
							&.team-mobile {
								display: none;
								@include media-breakpoint-down(md) {
									display: block;
								}
							}
							&.team-desktop {
								display: none;
								@include media-breakpoint-up(md) {
									display: block;
								}
							}
							width: 100%;
							border-radius: 0;
							@include media-breakpoint-down(md) {
								object-position: top center;
								object-fit: cover;
							}
						}
					}
				}
			}
		}
	}
	h2 {
		font-size: 32px;
		line-height: 48px;
		margin-bottom: 60px;
		@include breakpoint('xl') {
			@include primary-heading();
		}
	}
	.member {
		padding-bottom: 40px;
		h3 {
			font-weight: 400;
			font-size: 26px;
			line-height: 38px;
			margin: 20px 0px 10px;
		}
		img {
			height: auto;
			@include media-breakpoint-up(xl) {
				max-height: 500px;
			}
			border-radius: 20px 0 0;
		}
		span {
			font-size: 16px;
			line-height: 26px;
			color: #4e4e4e;
			margin-bottom: 10px;
		}
	}
}
@keyframes mymove {
	0% {
		left: 0px;
	}
	100% {
		left: 85px;
	}
}

@keyframes mymove2 {
	0% {
		left: 85px;
	}
	100% {
		left: 0px;
	}
}

@keyframes paddinganimation {
	0% {
		padding-left: 0;
		opacity: 0;
	}
	100% {
		padding-left: 35px;
		opacity: 1;
	}
}
@keyframes removepaddinganimation {
	0% {
		opacity: 0;
		padding-left: 35px;
	}
	100% {
		padding-left: 0;
		opacity: 1;
	}
}
