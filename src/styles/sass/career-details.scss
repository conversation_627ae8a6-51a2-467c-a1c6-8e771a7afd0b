.ug__career {
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 60px;

    &_image {
        width: 100%;
        height: 400px;
        border-radius: 20px 0px 0px 0px;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    &_content {
        display: grid;
        grid-template-columns: 1fr;
        grid-gap: 20px;

        * {
            margin-bottom: 0;
        }

        &_title {
            font-family: <PERSON>;
            font-size: 26px;
            font-weight: 500;
            line-height: 38px;
            text-align: left;
        }

        &_details {
            font-family: <PERSON>;
            font-size: 16px;
            font-weight: 400;
            line-height: 26px;
            text-align: left;
            color: #4E4E4E;
        }
    }
}

@media screen and (max-width:560px) {
    .ug__career {
        grid-gap: 20px;

        &_image {
            height: 200px;
        }
    }
}