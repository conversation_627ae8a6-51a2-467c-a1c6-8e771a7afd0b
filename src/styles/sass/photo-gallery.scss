#photo-gallery {
	.photo-gallery-nav-desktop {
		display: none;
		@include media-breakpoint-up(lg) {
			display: block;
		}
	}
	.photo-gallery-nav-mobile {
		display: none;
		@include media-breakpoint-down(lg) {
			display: block;
		}
	}
	.photo-gallery-nav-mobile {
		border-bottom: 2px solid #dddfe3;
		padding-bottom: 10px;
		position: relative;
		&::after {
			content: '';
			position: absolute;
			bottom: -1px;
			left: 0;
			background-color: $base-orange;
			height: 2px;
			width: 50%;
		}
		.swiper-slide {
			width: 40%;
			text-align: center;
			a {
				font-weight: 500;
				text-transform: uppercase;
			}
			&.swiper-slide-active {
				a {
					color: $base-orange;
					font-weight: 500;
				}
			}
		}
	}
	ul {
		display: flex;
		list-style: none;
		text-transform: uppercase;
		justify-content: center;
		position: relative;
		padding: 0;
		li {
			a {
				font-weight: 500;
				font-size: 14px;
				line-height: 24px;
				color: #4e4e4e;
				text-align: center;
				padding: 14px 16px;
				border-bottom: 2px solid #dddfe3;
				&.active {
					color: #f05a23;
					font-weight: bold;
					border-bottom: 2px solid #f05a23;
				}
			}
		}
	}
	.gallery {
		margin-top: 60px;
		.single-gallery-item {
			margin-bottom: 30px;
			border: 1px solid rgba(0, 0, 0, 0.08);
			background: #fafafa;
			border-radius: 10px;
			padding: 10px;
			box-sizing: border-box;
			display: flex;
			flex-wrap: wrap;
			.single-col {
				flex: 50%;
				img {
					width: 100%;
					object-fit: cover;
				}
				&:nth-child(1) {
					padding-right: 10px;
					img {
						height: 234px;
					}
				}
				&:nth-child(2) {
					position: relative;
					img {
						height: 117px;
						&:first-child {
							padding-bottom: 10px;
						}
					}
					&::after {
						content: attr(data-total);
						position: absolute;
						left: 0;
						width: 100%;
						height: 50%;
						background-color: #00000063;
						font-weight: 400;
						font-size: 26px;
						line-height: 38px;
						color: #ffffff;
						text-align: center;
						padding-top: 20%;
					}
				}
			}
			.content {
				h2 {
					margin: 5px 0px;
					font-weight: 400;
					font-size: 20px;
					line-height: 32px;
				}
				span {
					color: #4e4e4e;
					font-weight: 400;
					font-size: 14px;
					line-height: 24px;
				}
			}
		}
	}
}

.video-gallery {
	.video-poster-image {
		cursor: pointer;
		position: relative;
		img {
			object-fit: cover;
		}
		.play-btn {
			position: absolute;
			bottom: 15px;
			left: 0;
			z-index: 9;
			padding: 0 20px 0 20px;
			display: flex;
			width: 100%;
			flex-wrap: wrap;
			align-items: center;
			svg#play-svg {
				width: 40px;
				height: 66px;
			}
			.title {
				font-weight: 400;
				font-size: 16px;
				line-height: 26px;
				color: #ffffff;
				padding-left: 10px;
				width: calc(100% - 40px);
				text-transform: uppercase;
			}
			&:hover {
				svg {
					path {
						fill: $base-orange;
					}
				}
			}
		}
	}
	.thumb {
		position: relative;
		border-radius: 20px 0 0 0;
		overflow: hidden;
	}
	.overlay {
		position: absolute;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #000000a3;
		z-index: 0;
		display: flex;
		align-items: flex-end;
	}
}
