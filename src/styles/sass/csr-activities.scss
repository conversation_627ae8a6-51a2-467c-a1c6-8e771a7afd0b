#csr-activities {
	background-color: #2b2b2b;
	position: relative;
	background-size: cover;
	.shape {
		position: absolute;
		bottom: 0;
		left: -25px;
		img {
			-webkit-user-drag: none;
			-khtml-user-drag: none;
			-moz-user-drag: none;
			-o-user-drag: none;
			user-drag: none;
		}
	}

	h2 {
		@include primary-heading();
		color: $white;
		@include media-breakpoint-down(xl) {
			font-size: 38px;
			line-height: 48px;
		}
	}
	p {
		font-size: 16px;
		line-height: 26px;
		color: $white;
		opacity: 0.8;
		@include media-breakpoint-down('sm') {
			margin-bottom: 50px;
		}
	}
	.item {
		display: flex;
		margin-bottom: 30px;
		.icon {
			margin-right: 15px;
			width: 90px;
			height: 90px;
			@include media-breakpoint-down(xl) {
				width: 50px;
			}
			img {
				max-width: inherit;
				max-width: 50px;
				max-height: 50px;
			}
		}
		.content {
			width: calc(100% - 100px);
			h3 {
				@include primary-heading();
				color: $white;
				margin-bottom: 10px;
				@include media-breakpoint-down(xl) {
					font-size: 38px;
					line-height: 48px;
				}
			}
			h5 {
				font-weight: 400;
				font-size: 20px;
				line-height: 32px;
				color: $white;
			}
			.prefix-count {
				font-size: 40px;
				line-height: 60px;
				color: #ffffff;
				margin-bottom: 10px;
				opacity: 1;
			}

			span {
				font-size: 16px;
				line-height: 26px;
				color: $white;
				opacity: 0.6;
			}
		}
	}

	&.overlay {
		position: relative;
		z-index: 9;
		&::after {
			content: '';
			position: absolute;
			top: 0;
			width: 100%;
			height: 100%;
			background-color: #0000008c;
			@include media-breakpoint-down(lg) {
				background-color: #0000004f;
			}
			z-index: -1;
		}
	}
}
