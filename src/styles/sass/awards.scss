#awards {
	.header-highlight {
		margin-bottom: 0px;
		@include breakpoint('xl') {
			margin-bottom: 60px;
		}
		h2 {
			font-size: 32px;
			line-height: 48px;
			@include breakpoint('xl') {
				@include primary-heading();
			}
		}
		.navigation {
			display: flex;
			justify-content: flex-end;
			position: absolute;
			bottom: 26px;
			right: 17px;
			@include media-breakpoint-down('sm') {
				justify-content: flex-end;
				position: relative;
				bottom: 55px;
				right: 0;
			}
			@include sectionNavigate();
		}
	}
	.awards-main {
		overflow: hidden;
		.single-awards {
			display: flex;
			flex-wrap: wrap;
			align-content: center;
			justify-content: center;
			text-align: center;
			border: 1px solid #dddfe3;
			box-sizing: border-box;
			border-radius: 20px 0px 0px 0px;
			.thumb {
				padding: 30px;
				img {
					height: 175px;
					object-fit: contain;
				}
			}
			.content {
				border-top: 1px solid #dddfe3;
				padding: 30px;
				h3 {
					font-weight: 400;
					font-size: 26px;
					line-height: 38px;
				}
				p {
					font-weight: normal;
					font-size: 14px;
					line-height: 24px;
					color: #4e4e4e;
				}
			}
		}
	}
}
