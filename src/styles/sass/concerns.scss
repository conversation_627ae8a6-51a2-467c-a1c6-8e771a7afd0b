.concerns {
    background-color: #2b2b2b;
    h2 {
        text-align: center;
        font-weight: 400;
        font-size: 32px;
        line-height: 48px;
        color: #ffffff;
        margin-bottom: 30px;
        @include breakpoint("xl") {
            @include primary-heading();
            margin-bottom: 50px;
        }
    }
    .grid {
        border-radius: 5px;
        background-color: #424143;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        padding: 10px;
        column-gap: 10px;
        .column {
            flex: 100%;
            @include breakpoint("lg") {
                flex: 1;
            }
            .thumb {
                position: relative;
                margin-bottom: 10px;
                border: 1px solid transparent;
                border-radius: 10px;
                overflow: hidden;
                @include breakpoint("lg") {
                    overflow: auto;
                }
                h4 {
                    position: absolute;
                    padding: 10px;
                    font-weight: 400;
                    font-size: 26px;
                    line-height: 38px;
                    color: #ffffff;
                    bottom: 15px;
                    width: 100%;
                    text-align: center;
                    z-index: 9999;
                    opacity: 0;
                    text-shadow: 0px 4px 4px #000000;
                    @include media-breakpoint-down(xl) {
                        opacity: 1;
                    }
                    transition: all 0.25s ease-in-out;
                }
                a.learn-more {
                    text-shadow: 0px 4px 4px #000000;
                    position: absolute;
                    left: 10px;
                    @include breakpoint("lg") {
                        left: 0;
                    }
                    padding: 10px;
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 22px;
                    color: #ffffff;
                    bottom: 0;
                    width: 100%;
                    text-align: center;
                    z-index: 9999;
                    opacity: 0;
                    @include media-breakpoint-down(xl) {
                        opacity: 1;
                    }
                    transition: all 0.25s ease-in-out;
                    svg {
                        opacity: 0;
                    }
                }
                &:last-child {
                    margin-bottom: 10px;
                    @include breakpoint("lg") {
                        margin-bottom: 0;
                    }
                }
                img {
                    position: relative;
                    margin-bottom: 10px;
                    width: 100%;
                    filter: grayscale(80%);
                    object-fit: cover;
                    transition: all 0.25s ease;
                }
                &::after {
                    content: "";
                    position: absolute;
                    // background: linear-gradient(
                    // 	180deg,
                    // 	rgba(0, 0, 0, 0) 0%,
                    // 	rgba(0, 0, 0, 0) 0.01%,
                    // 	#000000 100%
                    // );
                    width: 100%;
                    height: 100%;
                    z-index: 9;
                    top: 0;
                    left: 0;
                }
                &:hover {
                    h4 {
                        opacity: 1;
                        transition: all 0.25s
                            cubic-bezier(0.075, 0.82, 0.165, 1);
                    }
                    a.learn-more {
                        opacity: 1;
                        transition: all 0.25s
                            cubic-bezier(0.075, 0.82, 0.165, 1);
                        text-decoration: underline;
                        svg {
                            opacity: 1;
                            animation-name: gridarrowmymove;
                            animation-duration: 0.25s;
                            animation-fill-mode: forwards;
                            animation-timing-function: linear;
                        }
                    }
                    img {
                        filter: grayscale(0);
                        transition: all 0.25s ease;
                    }
                }
            }
            @include media-breakpoint-down(sm) {
                &:last-child {
                    .thumb {
                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }
    }
}

@keyframes gridarrowmymove {
    0% {
        transform: translateX(-4px);
    }
    100% {
        transform: translateX(4px);
    }
}

// Generic
#generic-concerns {
    &.add-bg {
        .single-concerns-items {
            .content {
                background-color: #fff;
            }
        }
    }
    .page-header {
        padding-bottom: 55px;
        @include media-breakpoint-down("xl") {
            padding-bottom: 30px;
        }
    }
    .single-concerns-items {
        border: 1px solid #dddfe3;
        box-sizing: border-box;
        border-radius: 10px;
        margin-bottom: 30px;
        overflow: hidden;
        &.style2 {
            .thumb {
                img {
                    width: 100%;
                    min-height: 450px;
                    object-fit: cover;
                    @include media-breakpoint-down(xl) {
                        min-height: auto;
                    }
                }
            }
            .content {
                @include media-breakpoint-down(xl) {
                    padding-top: 20px !important;
                    padding-bottom: 20px !important;
                }
                padding-left: 15px !important;
                padding-right: 15px !important;
            }
        }
        .thumb {
            img {
                width: 100%;
                height: 250px;
                object-fit: cover;
            }
        }
        .content {
            padding: 20px;
            position: relative;
            min-height: 330px;
            h3 {
                font-weight: 400;
                font-size: 20px;
                line-height: 32px;
            }
            ul {
                list-style: none;
                padding: 0 0 30px;
                margin-bottom: 30px;
                li {
                    font-size: 16px;
                    line-height: 26px;
                    color: #4e4e4e;
                    padding: 0;
                    display: flex;
                    &::before {
                        content: "\2022";
                        color: #f05a23;
                        display: inline-block;
                        font-size: 22px;
                        margin-right: 10px;
                    }
                }
            }
            p {
                padding: 0 0 20px;
                margin-bottom: 0;
            }
            .read-more {
                position: absolute;
                left: 0;
                bottom: 0;
                border-top: 1px solid #dddfe3;
                width: 100%;
                padding: 15px 30px;
                font-size: 14px;
                line-height: 24px;
                color: #4e4e4e;
                font-weight: 500;

                svg {
                    position: absolute;
                    right: 20px;
                    top: 30%;
                }
                &::after {
                    content: "";
                    position: absolute;
                    background-color: #dddfe3;
                    width: 1px;
                    height: 100%;
                    top: 0;
                    right: 55px;
                }
                &::before {
                    content: "";
                    border-radius: 0 0 5px;
                    position: absolute;
                    background-color: transparent;
                    transition: all 0.25s ease-in-out;
                    width: 56px;
                    height: 100%;
                    top: 0;
                    right: 0;
                }
                &:hover {
                    &::before {
                        background-color: #f05a23;
                        transition: all 0.25s ease-in;
                    }
                }
            }
        }
        &.style2 {
            border: none;

            .content {
                padding: 60px 0 60px;
                p {
                    margin-bottom: 0;
                }
            }
        }
    }
    &.single-items {
        .single-concerns-items {
            .thumb {
                img {
                    width: 100%;
                    min-height: 450px;
                    object-fit: cover;
                    @include media-breakpoint-down(xl) {
                        min-height: auto;
                    }
                }
            }
        }
    }

    &.height-auto {
        .single-concerns-items .content {
            min-height: auto;
        }
    }
    &.blue-icon {
        .single-concerns-items {
            .content {
                ul {
                    li {
                        &::before {
                            color: #005282;
                        }
                    }
                }
            }
        }
    }
}

// Single Blog Post
.single-our-concerns {
    .size-full {
        img {
            width: 100%;
            height: auto;
        }
    }
}

.united-editor {
    width: 100%;
    width: 950px;
    @include media-breakpoint-down(xl) {
        padding: 60px 0 60px;
        width: 100%;
    }
    &.full-width {
        width: 100% !important;
    }
    margin: 0 auto;

    h2 {
        font-size: 32px;
        line-height: 48px;
        text-transform: uppercase;
        @include breakpoint("xl") {
            @include primary-heading();
        }
    }
    ul {
        list-style: none;
        padding: 0 0 30px;
        margin-bottom: 0px;
        li {
            font-size: 16px;
            line-height: 26px;
            color: #4e4e4e;
            padding: 0;
            display: flex;
            line-height: 34px;
            &::before {
                content: "\2022";
                color: #f05a23;
                display: inline-block;
                font-size: 22px;
                margin-right: 10px;
            }
        }
    }
    p {
        &:last-child {
            margin-bottom: 0;
        }
    }
    img {
        height: auto;
        @include media-breakpoint-down(xl) {
            margin-bottom: 15px;
        }
    }
    &.blue-icon {
        ul {
            li {
                &::before {
                    color: #005282;
                }
            }
        }
    }
}
.single-concerns-items.style2 {
    .content {
        padding: 15px;
    }
}
