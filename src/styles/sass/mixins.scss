// BORDER RADIUS
@mixin border-radius($radius) {
	-webkit-border-radius: $radius;
	-moz-border-radius: $radius;
	border-radius: $radius;
}

//   transition
@mixin transition($transition) {
	-webkit-transition: all $transition ease-in-out;
	-moz-transition: all $transition ease-in-out;
	transition: all $transition ease-in-out;
}

//  transform property
@mixin transform($property) {
	-webkit-transform: $property;
	-ms-transform: $property;
	transform: $property;
}

@mixin breakpoint($point) {
	@if $point == xxs {
		@media (min-width: 390px) {
			@content;
		}
	} @else if $point == xs {
		@media (min-width: 450px) {
			@content;
		}
	} @else if $point == sm {
		@media (min-width: 576px) {
			@content;
		}
	} @else if $point == md {
		@media (min-width: 768px) {
			@content;
		}
	} @else if $point == lg {
		@media (min-width: 992px) {
			@content;
		}
	} @else if $point == mlg {
		@media (max-width: 992px) {
			@content;
		}
	} @else if $point == xl {
		@media (min-width: 1200px) {
			@content;
		}
	} @else if $point == xl-desktop {
		@media (min-width: 1300px) {
			@content;
		}
	} @else if $point == xxl-desktop {
		@media (min-width: 1500px) {
			@content;
		}
	} @else if $point == xxxl-desktop {
		@media (min-width: 1920px) {
			@content;
		}
	} @else if $point == d-1024 {
		@media (width: 1024px) {
			@content;
		}
	} @else if $point == d-1440 {
		@media (width: 1440px) {
			@content;
		}
	} @else if $point == d-1280 {
		@media (width: 1280px) {
			@content;
		}
	} @else if $point == d-640 {
		@media (width: 640px) {
			@content;
		}
	}
}

@mixin primary-heading {
	font-size: 40px;
	line-height: 60px;
}

@mixin heroNavigate {
	.banner-slide-next-btn {
		width: 50px;
		height: 50px;
		line-height: 48px;
		right: 50px;
		background-color: rgba(255, 99, 96, 0.6);
		position: absolute;
		top: 90%;
		margin-top: -25px;
		z-index: 2;
		outline: none;
		color: #fff;
		cursor: pointer;
		text-align: center;
		border-radius: 0 0 10px;
		transition: all 1ms ease-in-out;
		svg {
			path {
				fill: $white;
			}
		}
		&.active {
			background-color: #f05a23;
			transition: all 1ms ease-in;
		}
	}
	.banner-slide-prev-btn {
		width: 50px;
		height: 50px;
		line-height: 48px;
		right: 100px;
		background-color: rgba(255, 99, 96, 0.6);
		position: absolute;
		top: 90%;
		margin-top: -25px;
		z-index: 2;
		outline: none;
		color: #fff;
		cursor: pointer;
		text-align: center;
		border-radius: 10px 0 0;
		transition: all 1ms ease-in-out;
		svg {
			path {
				fill: $white;
			}
		}
		&.active {
			background-color: #f05a23;
			transition: all 1ms ease-in;
		}
	}
}

@mixin sectionNavigate {
	a.prev {
		width: 50px;
		height: 50px;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #f7f7f7;
		border: 1px solid #f05a23;

		border-radius: 5px 0 0;
		transition: all 0.25s ease-in;
		svg {
			path {
				fill: #f05a23;
			}
		}
		&.active {
			background-color: #f05a23;
			transition: all 0.25s ease-in-out;
			svg {
				path {
					fill: #000;
				}
			}
		}
	}
	a.next {
		width: 50px;
		height: 50px;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #f7f7f7;
		border: 1px solid #f05a23;
		border-radius: 0 0 5px;
		transition: all 0.25s ease-in-out;
		svg {
			path {
				fill: #f05a23;
			}
		}
		&.active {
			background-color: #f05a23;
			transition: all 0.25s ease-in-out;
			svg {
				path {
					fill: #000;
				}
			}
		}
	}
}
