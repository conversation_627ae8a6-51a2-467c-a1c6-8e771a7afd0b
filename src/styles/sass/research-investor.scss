#reserach-investor {
	.research-nav-menu {
		margin-bottom: 60px;
		li {
			a {
				font-weight: 400;
				font-size: 14px;
				line-height: 24px;
				text-transform: uppercase;
				color: $base-black;
				border-bottom: 2px solid $border;
				padding: 0 50px 10px 50px;
				transition: all 0.25s ease-in-out;
				&.active {
					border-bottom: 2px solid $base-orange;
					transition: all 0.25s ease-in;
				}
			}
		}
	}
	#research-accordion {
		.accordion-item {
			border: none;
			margin-bottom: 30px;
			.accordion-header {
				margin-bottom: 0;
				border-top: 1px solid #00000008;
				border-bottom: 1px solid #00000008;
			}

			.accordion-button:not(.collapsed) {
				background-color: transparent;
				border-radius: 0;
				box-shadow: none;
				&::after {
					background-image: url('data:image/svg+xml, %3Csvg width="15" height="3" viewBox="0 0 15 3" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M14.7917 0.458344H0.208374V2.54168H14.7917V0.458344Z" fill="%23F05A23"/%3E%3C/svg%3E');
				}
			}
			.accordion-item:first-of-type .accordion-button {
				background-color: transparent;
				border-radius: 0;
			}

			.accordion-button {
				font-weight: 400;
				font-size: 20px;
				line-height: 32px;
				color: $black;
				text-transform: uppercase;
				&:focus {
					border: none;
					box-shadow: none;
				}
				&::after {
					background-image: url('data:image/svg+xml, %3Csvg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M14.7917 8.54167H8.54171V14.7917H6.45837V8.54167H0.208374V6.45834H6.45837V0.208336H8.54171V6.45834H14.7917V8.54167Z" fill="black"/%3E%3C/svg%3E');
				}
			}
			.accordion-body {
				margin-top: 20px;
				padding: 0;
				.item {
					background: #f5f5f582;
					padding: 15px;
					.left {
						padding-right: 30px;
					}
					h3 {
						font-weight: 400;
						font-size: 20px;
						line-height: 32px;
						margin: 0;
						color: $black;
					}
					h4 {
						font-weight: 400;
						font-size: 16px;
						line-height: 26px;
						color: #4e4e4e;
					}
					a {
						color: $base-orange;
						font-weight: bold;
						font-size: 16px;
						line-height: 20px;
						text-transform: uppercase;
						svg {
							position: relative;
							top: -4px;
						}
						span {
							@include media-breakpoint-down(sm) {
								display: none;
							}
						}
					}
					&:first-child {
						border: 1px solid #00000008;
						background: #f5f5f5;
					}
				}
			}
		}
	}
	.research-nav-mobile.for-mobile {
		margin-bottom: 30px;
		border-bottom: 2px solid #dddfe3;
		padding-bottom: 10px;
		position: relative;
		&::after {
			content: '';
			position: absolute;
			bottom: -1px;
			left: 0;
			background-color: $base-orange;
			height: 2px;
			width: 50%;
		}
		.swiper-slide {
			font-weight: 500;
			text-transform: uppercase;
			&.swiper-slide-active {
				a {
					color: $base-orange;
					font-weight: 500;
				}
			}
		}

		.swiper-slide {
			max-width: 200px;
			text-align: left;
		}
	}
}
