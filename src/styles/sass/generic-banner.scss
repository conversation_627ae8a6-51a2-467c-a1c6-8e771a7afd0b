#generic-banner {
	padding: 30px 0 30px;
	@include breakpoint('xl') {
		padding: 50px 0 50px;
	}
	.left {
		@include media-breakpoint-up('xl') {
			width: 80%;
		}
		span {
			font-weight: bold;
			font-size: 14px;
			line-height: 24px;
			text-transform: uppercase;
			color: #f05a23;
			position: relative;
			display: inline-block;
			&::after {
				content: '';
				position: absolute;
				height: 1px;
				background-color: #f05a23;
				width: 40px;
				right: -50px;
				top: 10px;
			}
		}
		h1 {
			font-size: 38px;
			line-height: 48px;
			@include breakpoint('xl') {
				@include primary-heading();
			}
			color: #000000;
		}
	}
	.right {
		@include media-breakpoint-up('xl') {
			margin-top: 30px;
		}
		p {
			font-weight: 400;
			font-size: 14px;
			line-height: 30px;
			max-width: 350px;
			color: #4e4e4e;
		}
		.banner-link {
			a {
				font-weight: bold;
				font-size: 16px;
				line-height: 24px;
				color: #f05a23;
				transition: all 0.25s ease-in-out;
				&:hover {
					transition: all 0.25s ease-in;
					color: $black;
					svg {
						path {
							fill: $black;
						}
					}
				}
			}
		}
		.chief-note {
			h4 {
				max-width: 350px;
				padding-bottom: 5px;
				font-size: 26px !important;
				font-weight: 400 !important;
			}
			p {
				font-size: 26px !important;
				font-weight: 400 !important;
				padding-bottom: 0;
				margin-bottom: 15px;
			}
		}
	}
}
#single-banner {
	.overlay-text {
		position: relative;
		img {
			width: 100%;
			object-fit: cover;
			max-height: 700px;
			@include media-breakpoint-down(md) {
				height: 200px;
			}
		}
		h3 {
			font-weight: 300;
			font-size: 50px;
			line-height: 75px;
			color: #fff;
			z-index: 9;
			position: absolute;
			top: 0;
			left: 0;
			display: flex;
			width: 100%;
			height: 100%;
			align-items: center;
			justify-content: center;
			@include media-breakpoint-down(xl) {
				font-size: 38px;
				line-height: 48px;
				text-align: center;
				padding: 20px;
			}
		}
	}
	&.career-section {
		.overlay-content {
			position: absolute;
			bottom: 100px;
			z-index: 10;
			left: 0;
			padding-left: 100px;
			@include media-breakpoint-down(md) {
				padding-left: 15px;
				bottom: 20px;
			}
			h4 {
				color: #fff;
				font-weight: 400;
				font-size: 30px;
				@include media-breakpoint-down(md) {
					margin-bottom: 5px;
				}
			}
			h5 {
				color: #fff;
				font-weight: 400;
				font-size: 26px;
				@include media-breakpoint-down(md) {
					font-size: 22px;
				}
			}
		}
		.overlay-text {
			&::after {
				content: '';
				position: absolute;
				z-index: 9;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: #00000066;
			}
		}
	}
}
