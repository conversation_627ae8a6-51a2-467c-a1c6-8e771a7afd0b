/*
  Template Name: United Group
  Description: ;
  Version: 1.0.0;
  Text Domain United Group;
*/
/*
.........................................
        Table of Contents:

# Reseat Css
# Global CSS
# Heading CSS
# Banner CSS
# Features CSS
# Screenshot CSS
# Video CSS
# Interface Slider CSS
# Pricing CSS
# Testimonial CSS
# FAQ CSS
# Blog CSS
# Contact CSS
# Footer CSS
.........................................
*/

*,
*::before,
*::after {
	box-sizing: border-box;
}

html {
	font-family: $base-font;
	line-height: 1.15;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
	-ms-overflow-style: scrollbar;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	scroll-behavior: smooth;
}

@-ms-viewport {
	width: device-width;
}

article,
aside,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
	display: block;
}

body {
	margin: 0;
	font-family: $base-font;
	font-size: $fs-base;
	font-weight: 400;
	line-height: 1.4;
	color: $dark;
	text-align: left;
}

[tabindex='-1']:focus {
	outline: 0 !important;
}

hr {
	box-sizing: content-box;
	height: 0;
	overflow: visible;
}

// Typography
h1 {
	font-size: $fs-h1;
	@include media-breakpoint-down('xl') {
		font-size: 32px;
		line-height: 48px;
	}
}
h2 {
	@include primary-heading();
	@include media-breakpoint-down('xl') {
		font-size: 32px;
		line-height: 48px;
	}
}
h3 {
	font-size: $fs-h3;
	font-weight: 400;
}
h4 {
	font-size: $fs-h4;
	line-height: 30px;
}
h5 {
	font-size: $fs-h5;
	margin: 5px 0;
}
h6 {
	font-size: $fs-h6;
	margin: 5px 0;
}
h1,
h2,
h3,
h4,
h5,
h6 {
	color: $dark;
	font-weight: 400;
	font-family: $base-font;
	line-height: 1.5;
	text-transform: uppercase;
}
h1 > a,
h2 > a,
h3 > a,
h4 > a,
h5 > a,
h6 > a {
	color: inherit;
	font-weight: 400;
	font-family: $base-font;
}
p {
	font-size: 15px;
	margin: 0 0 1.6em;
	color: $dark;
	line-height: 1.75;
	font-family: $base-font;
	font-weight: 400;
	@include breakpoint(xl) {
		font-size: $fs-base;
	}
}

abbr[title],
abbr[data-original-title] {
	text-decoration: underline;
	-webkit-text-decoration: underline dotted;
	text-decoration: underline dotted;
	cursor: help;
	border-bottom: 0;
}

address {
	margin-bottom: 1rem;
	font-style: normal;
	line-height: inherit;
}

ol,
ul,
dl {
	margin-top: 0;
	margin-top: 0;
	padding: 0 20px;
	line-height: 30px;
	margin-bottom: 1rem;
	margin-bottom: 1rem;
}

dt {
	font-weight: 700;
}

dd {
	margin-bottom: 0.5rem;
	margin-left: 0;
}

blockquote {
	margin: 0 0 1rem;
}

dfn {
	font-style: italic;
}

b,
strong {
	font-weight: 400;
}

small {
	font-size: 80%;
}

sub,
sup {
	position: relative;
	font-size: 75%;
	line-height: 0;
	vertical-align: baseline;
}

sub {
	bottom: -0.25em;
}

sup {
	top: -0.5em;
}

a {
	color: $dark;
	text-decoration: none;
	background-color: transparent;
	-webkit-text-decoration-skip: objects;
}

a:hover {
	text-decoration: none;
	color: inherit;
}

a:not([href]):not([tabindex]) {
	color: inherit;
	text-decoration: none;
}

a:not([href]):not([tabindex]):hover,
a:not([href]):not([tabindex]):focus {
	color: inherit;
	text-decoration: none;
}

a:not([href]):not([tabindex]):focus {
	outline: 0;
}

pre,
code,
kbd,
samp {
	font-family: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
		'Courier New', monospace;
	font-size: 1em;
}

pre {
	margin-top: 0;
	margin-bottom: 1rem;
	overflow: auto;
	-ms-overflow-style: scrollbar;
}

figure {
	margin: 0 0 1rem;
}

img {
	vertical-align: middle;
	border-style: none;
	max-width: 100%;
}

svg {
	overflow: hidden;
	vertical-align: middle;
}

table {
	border-collapse: collapse;
}

caption {
	padding-top: 0.75rem;
	padding-bottom: 0.75rem;
	color: #6c757d;
	text-align: left;
	caption-side: bottom;
}

th {
	text-align: inherit;
}

label {
	display: inline-block;
	margin-bottom: 0.5rem;
}

button {
	border-radius: 0;
}

button:focus {
	outline: 1px dotted;
	outline: 5px auto -webkit-focus-ring-color;
}

input,
button,
select,
optgroup,
textarea {
	margin: 0;
	font-family: inherit;
	font-size: inherit;
	line-height: inherit;
}

button,
input {
	overflow: visible;
}

button,
select {
	text-transform: none;
}

button,
html [type='button'],
[type='reset'],
[type='submit'] {
	-webkit-appearance: button;
}

button::-moz-focus-inner,
[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner {
	padding: 0;
	border-style: none;
}

input[type='radio'],
input[type='checkbox'] {
	box-sizing: border-box;
	padding: 0;
}

input[type='date'],
input[type='time'],
input[type='datetime-local'],
input[type='month'] {
	-webkit-appearance: listbox;
}

textarea {
	overflow: auto;
	resize: vertical;
}

fieldset {
	min-width: 0;
	padding: 0;
	margin: 0;
	border: 0;
}

legend {
	display: block;
	width: 100%;
	max-width: 100%;
	padding: 0;
	margin-bottom: 0.5rem;
	font-size: 1.5rem;
	line-height: inherit;
	color: inherit;
	white-space: normal;
}

progress {
	vertical-align: baseline;
}

[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
	height: auto;
}

[type='search'] {
	outline-offset: -2px;
	-webkit-appearance: none;
}

[type='search']::-webkit-search-cancel-button,
[type='search']::-webkit-search-decoration {
	-webkit-appearance: none;
}

::-webkit-file-upload-button {
	font: inherit;
	-webkit-appearance: button;
}

output {
	display: inline-block;
}

summary {
	display: list-item;
	cursor: pointer;
}

template {
	display: none;
}

[hidden] {
	display: none !important;
}
