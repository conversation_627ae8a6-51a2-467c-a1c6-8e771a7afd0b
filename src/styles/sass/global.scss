a {
	color: $base-orange;
}

.position-relative {
	position: relative;
}

.text-center {
	text-align: center;
}

.for-desktop {
	display: none;

	@include media-breakpoint-up(xl) {
		display: block;
	}
}

.for-mobile {
	display: none;

	@include media-breakpoint-down(xl) {
		display: block;
	}
}

.for-md-mobile {
	display: none;

	@include media-breakpoint-down(xl) {
		display: block;
	}
}

.for-md-desktop {
	display: none;

	@include media-breakpoint-up(xl) {
		display: block;
	}
}

.font-weight-bold {
	font-weight: 400;
}

.page-header {
	h2 {
		text-align: center;
		font-size: 32px;
		line-height: 48px;

		@include breakpoint('xl') {
			@include primary-heading();
		}
	}

	&.black {
		h2 {
			color: #000000;
		}
	}

	&.white {
		h2 {
			color: #ffffff;
		}
	}
}

.add-bg {
	background-color: #fafafa;
	padding-bottom: 120px !important;

	@include media-breakpoint-down(xl) {
		padding-bottom: 60px !important;
	}
}

// All Section Spacing Primary
.section-spacing {
	padding-top: 60px;
	padding-bottom: 60px;

	@include breakpoint('xl-desktop') {
		padding-top: 80px;
		padding-bottom: 80px;
	}

	@include breakpoint('xxl-desktop') {
		padding-top: 120px;
		padding-bottom: 120px;
	}
}

.spacing-bottom-none {
	padding-bottom: 0 !important;
}

.spacing-top-none {
	padding-top: 0 !important;
}

.half-top-space {
	padding-top: 40px !important;
}

.margin-top {
	margin-top: 120px;

	@include media-breakpoint-down(xl) {
		margin-top: 60px;
	}
}

.list-underlist-none {
	list-style: none;
}

.custom-width {
	max-width: 730px;
	margin: 0 auto;
}

// preloader scss
#loading {
	background-color: $base-orange;
	height: 100%;
	width: 100%;
	position: fixed;
	z-index: 9999;
	margin-top: 0px;
	top: 0px;
}

#loading-center {
	width: 100%;
	height: 100%;
	position: relative;
}

#loading-center-absolute {
	position: absolute;
	left: 50%;
	top: 50%;
	height: 60px;
	width: 60px;
	margin-top: -30px;
	margin-left: -30px;
	-webkit-animation: loading-center-absolute 1s infinite;
	animation: loading-center-absolute 1s infinite;
}

.object {
	width: 20px;
	height: 20px;
	background-color: #fff;
	float: left;
	-moz-border-radius: 50% 50% 50% 50%;
	-webkit-border-radius: 50% 50% 50% 50%;
	border-radius: 50% 50% 50% 50%;
	margin-right: 20px;
	margin-bottom: 20px;
}

.object:nth-child(2n + 0) {
	margin-right: 0px;
}

#object_one {
	-webkit-animation: object_one 1s infinite;
	animation: object_one 1s infinite;
}

#object_two {
	-webkit-animation: object_two 1s infinite;
	animation: object_two 1s infinite;
}

#object_three {
	-webkit-animation: object_three 1s infinite;
	animation: object_three 1s infinite;
}

#object_four {
	-webkit-animation: object_four 1s infinite;
	animation: object_four 1s infinite;
}

// preloader

@-webkit-keyframes loading-center-absolute {
	100% {
		-ms-transform: rotate(360deg);
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes loading-center-absolute {
	100% {
		-ms-transform: rotate(360deg);
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@-webkit-keyframes object_one {
	50% {
		-ms-transform: translate(20px, 20px);
		-webkit-transform: translate(20px, 20px);
		transform: translate(20px, 20px);
	}
}

@keyframes object_one {
	50% {
		-ms-transform: translate(20px, 20px);
		-webkit-transform: translate(20px, 20px);
		transform: translate(20px, 20px);
	}
}

@-webkit-keyframes object_two {
	50% {
		-ms-transform: translate(-20px, 20px);
		-webkit-transform: translate(-20px, 20px);
		transform: translate(-20px, 20px);
	}
}

@keyframes object_two {
	50% {
		-ms-transform: translate(-20px, 20px);
		-webkit-transform: translate(-20px, 20px);
		transform: translate(-20px, 20px);
	}
}

@-webkit-keyframes object_three {
	50% {
		-ms-transform: translate(20px, -20px);
		-webkit-transform: translate(20px, -20px);
		transform: translate(20px, -20px);
	}
}

@keyframes object_three {
	50% {
		-ms-transform: translate(20px, -20px);
		-webkit-transform: translate(20px, -20px);
		transform: translate(20px, -20px);
	}
}

@-webkit-keyframes object_four {
	50% {
		-ms-transform: translate(-20px, -20px);
		-webkit-transform: translate(-20px, -20px);
		transform: translate(-20px, -20px);
	}
}

@keyframes object_four {
	50% {
		-ms-transform: translate(-20px, -20px);
		-webkit-transform: translate(-20px, -20px);
		transform: translate(-20px, -20px);
	}
}

// to top
.to-top {
	width: 40px;
	height: 40px;
	display: block;
	background-color: $base-orange;
	color: $white;
	@include border-radius(3px);
	position: fixed;
	bottom: 30px;
	right: 30px;
	z-index: 99;
	text-align: center;
	line-height: 40px;
	font-size: 20px;
	cursor: pointer;
	transform: translateY(100px);
	opacity: 0;
	border-radius: 50%;
	@include transition(0.3s);

	svg {
		transform: rotate(90deg);
		position: relative;
		top: -4px;
	}

	&:hover {
		color: $white;
		background-color: $gray;
	}

	&.fixed-totop {
		transform: translateY(0px);
		opacity: 1;
	}
}

.white-bg {
	background-color: #ffffff !important;
}

main.wp-block-group {
	padding-left: 15px;
	padding-right: 15px;
}

.section-page-header {
	h2 {
		font-size: 32px;
		line-height: 48px;

		@include breakpoint(xl) {
			@include primary-heading();
		}
	}

	div {
		max-width: 530px;
		margin: 0 auto;

		@include media-breakpoint-down(xl) {
			max-width: 100%;
		}
	}
}

.primary-btn {
	width: 186px;
	height: 50px;
	background: $base-orange;
	border: none;
	border-radius: 5px 0px 0px 0px;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	padding: 13px 50px;
	text-transform: uppercase;
	color: $black;
	font-weight: bold;
	font-size: 16px;
	line-height: 24px;
	transition: all 1ms ease-in-out;

	&:hover {
		background-color: $gray;
		color: $white;
		transition: all 1ms ease-in;
	}
}

.primary-btn-anim {
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	border: none;

	span {
		position: relative;
		transition: all 0.3s ease;

		&::after {
			content: '\00bb';
			position: absolute;
			opacity: 0;
			top: 0;
			right: -10px;
			transition: all 0.3s ease;
		}
	}

	&:hover {
		span {
			&::after {
				right: -20px;
				opacity: 1;
			}
		}
	}
}

#application-form form .ug-application-submit {
	input {
		display: none !important;
	}
}

.modal-open-window {
	position: relative;

	&::before {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: black;
		content: '';
		pointer-events: none;
		z-index: 100;
		opacity: 0.5;
	}
}

.white-circle {
	background: #ffffff;
}

.circle {
	width: 15px;
	height: 15px;
	border-radius: 50%;
	box-shadow: 0px 0px 1px 1px #118dff;
}

.pulse {
	animation: pulse-animation 1s infinite alternate;

	&.effect1 {
		animation-delay: 1s;
	}

	&.effect2 {
		animation-delay: 1.2s;
	}

	&.effect3 {
		animation-delay: 1.4s;
	}

	&.effect4 {
		animation-delay: 1.6s;
	}

	&.effect5 {
		animation-delay: 1.8s;
	}

	&.effect6 {
		animation-delay: 2s;
	}

	&.effect7 {
		animation-delay: 1s;
	}

	&.effect8 {
		animation-delay: 1.2s;
	}

	&.effect9 {
		animation-delay: 1.4s;
	}

	&.effect10 {
		animation-delay: 1.6s;
	}

	&.effect11 {
		animation-delay: 1.8s;
	}

	&.effect12 {
		animation-delay: 2s;
	}

	&.effect13 {
		animation-delay: 1.5s;
	}

	&.effect14 {
		animation-delay: 2s;
	}
}

@keyframes pulse-animation {
	0% {
		box-shadow: 0 0 0 0px #118cff70;
	}

	100% {
		box-shadow: 0 0 0 20px #118cff70;
	}
}

.master {
	color: black;
	float: right;
}

@media (max-width: 900px) {
	.master {
		display: none;
	}
}

/*======================
    404 page
=======================*/

.page_404 {
	padding: 40px 0;
	background: #fff;
}

.page_404 img {
	width: 100%;
}

.four_zero_four_bg {
	background-image: url(https://cdn.dribbble.com/users/285475/screenshots/2083086/dribbble_1.gif);
	height: 400px;
	background-position: center;
	background-repeat: no-repeat;
}

.four_zero_four_bg h1 {
	font-size: 80px;
}

.four_zero_four_bg h3 {
	font-size: 80px;
}

.link_404 {
	color: #fff !important;
	padding: 10px 20px;
	background: #39ac31;
	margin: 20px 0;
	display: inline-block;
}

.contant_box_404 {
	margin-top: -50px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

p.paragraph-heading {
	font-weight: 400 !important;
	font-size: 20px !important;
	line-height: 32px !important;
	color: #000000 !important;
}

.parallax-window {
	min-height: 500px;
	background: transparent;
}

.custom-parallax {
	margin-bottom: 30px;
	width: 100%;
	min-height: 300px;
	background-size: cover;
	background-position: 60%;

	@include media-breakpoint-up(lg) {
		min-height: 500px;
	}
}

.banner-parallax {
	min-height: 700px;
}

.paragraph {
	max-width: 960px;
}

.banner-parallax {
	min-height: 600px;
	background-size: cover;
	margin-bottom: 0;

	@include media-breakpoint-down(lg) {
		min-height: 300px;
	}
}

.single-image {
	img {
		max-height: 500px;
		width: 100%;
		object-fit: cover;
	}
}

.alignright {
	float: right;
}

.partner-logo {
	text-align: center;
	background: #f7f7f7;

	.row {
		@include media-breakpoint-down(md) {
			justify-content: center;
			align-content: center;
		}
	}

	h2 {
		margin-bottom: 30px;
	}

	.partner-items {
		margin-bottom: 20px;
		max-width: 280px;

		@include media-breakpoint-down(md) {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	&.large-logo {
		.partner-items {
			max-width: 350px !important;
		}
	}
}

.lg-backdrop {
	background-color: #0009;
}

.headroom--pinned {
	display: block;
}

.headroom--unpinned {
	display: none;
}

/**
 * Note: I have omitted any vendor-prefixes for clarity.
 * Adding them is left as an exercise for the reader.
 */
.headroom {
	will-change: transform;
	transition: transform 200ms linear;
}

.headroom--pinned {
	transform: translateY(0%);
}

.headroom--unpinned {
	transform: translateY(-100%);
}

.lg-sub-html h3 {
	margin: 0;
	font-size: 20px;
	font-weight: bold;
	color: #fff;
}

.lg-sub-html p {
	font-size: 12px;
	margin: 5px 0 0;
	color: #fff;
}

.image-group-grid {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 30px;

	img {
		width: 100%;
		height: auto;
		margin-bottom: 15px;

		@include breakpoint(xl) {
			width: 50%;
			margin-bottom: 0;
			object-fit: cover;
		}
	}
}

.our-concerns-template-default.single.single-our-concerns.postid-1795,
.our-concerns-template-default.single.single-our-concerns.postid-1179 {
	#image-slider .swiper .swiper-slide img {
		display: block;
		width: 100%;
		height: auto;
		object-fit: fill;
	}
}

// Healthcare
.page-id-240 #message-from-ceo {
	margin-bottom: 0;
}

.d-flex-dekstop {
	@include media-breakpoint-up(xl) {
		display: flex;
	}
}

span.filename {
	font-weight: 500;
	margin-top: 10px;
	display: block;
	color: #424143;
}

.postid-1102 {
	#image-slider {
		img {
			object-fit: contain !important;
		}
	}
}

@include media-breakpoint-up(sm) {
	.for-sm-desktop {
		display: block;
	}

	.for-sm-mobile {
		display: none;
	}
}

@include media-breakpoint-down(sm) {
	.for-sm-desktop {
		display: none;
	}

	.for-sm-mobile {
		display: block;
	}
}

.postid-1102 {
	#full-single-image {
		.for-sm-desktop {
			img {
				height: auto;
				object-fit: contain;
			}
		}
	}
}

.postid-3632 {
	#image-slider {
		img {
			object-fit: contain !important;
			height: auto !important;
		}
	}
}

#cheif-message .cheif-message-content p {
	text-align: justify;
}

.wpcf7-submit {
	border-radius: 10px 0px 0px 0px;
	background-color: #f05a23;
	border: none;
	color: #000000;
	text-align: center;
	font-size: 16px;
	padding: 9px;
	width: 200px;
	transition: all 0.5s;
	cursor: pointer;
	margin: 10px;
	font-weight: 500;
}

.wpcf7-submit span {
	cursor: pointer;
	display: inline-block;
	position: relative;
	transition: 0.5s;
}

.wpcf7-submit span:after {
	content: '\00bb';
	position: absolute;
	opacity: 0;
	top: 0;
	right: -20px;
	transition: 0.5s;
}

.wpcf7-submit:hover span {
	padding-right: 25px;
}

.wpcf7-submit:hover span:after {
	opacity: 1;
	right: 0;
}

// drop-resume
#drop-resume .primay-button {
	width: 260px !important;
}

#drop-resume a span {
	cursor: pointer;
	display: inline-block;
	position: relative;
	transition: 0.5s;
}

#drop-resume a span {
	cursor: pointer;
	display: inline-block;
	position: relative;
	transition: 0.5s;
}

#drop-resume a span:after {
	content: '\00bb';
	position: absolute;
	opacity: 0;
	top: 0;
	right: -40px;
	transition: 0.5s;
}

#drop-resume a:hover span {
	padding-right: 5px;
}

#drop-resume a:hover span:after {
	opacity: 1;
	right: -20px;
}

//
#verify-email-btn {
	// border-radius: 10px 0px 0px 0px;
	background-color: #f05a23;
	border: none;
	color: #000000;
	text-align: center;
	font-size: 16px;
	padding: 12px;
	width: 200px;
	transition: all 0.5s;
	cursor: pointer;
	font-weight: 500;
	transition: all 1ms ease-in-out;

	&:hover {
		transition: all 1ms ease-in;
		background-color: #424143;

		span {
			color: #ffffff !important;
		}
	}
}

#verify-email-btn span {
	cursor: pointer;
	display: inline-block;
	position: relative;
	transition: 0.5s;
	color: #000000 !important;
	margin-bottom: 0 !important;
	font-weight: 500 !important;
}

#verify-email-btn span:after {
	content: '\00bb';
	position: absolute;
	opacity: 0;
	top: 0;
	right: -40px;
	transition: 0.5s;
}

#verify-email-btn:hover span {
	padding-right: 5px;
}

#verify-email-btn:hover span:after {
	opacity: 1;
	right: -20px;
}