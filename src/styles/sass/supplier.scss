.supplier {
	background-color: #fef7f4;
	h2 {
		font-size: 32px;
		line-height: 48px;
		margin-bottom: 50px;
		@include breakpoint('xl') {
			@include primary-heading();
		}
	}
	.fuc-kit-heading {
		max-width: 960px;
		display: flex;
		flex-wrap: wrap;
		h4 {
			font-weight: 400;
			font-size: 32px;
			line-height: 46px;
		}
		.left {
			width: 400px;
			@include media-breakpoint-down(sm) {
				width: 100%;
			}
		}
		.right {
			width: 400px;
			@include media-breakpoint-down(sm) {
				width: 100%;
			}
		}
	}
	.fun-kit {
		max-width: 960px;
		display: flex;
		flex-wrap: wrap;
		padding-top: 20px;
		padding-bottom: 20px;
		.left {
			width: 400px;
			h3 {
				font-weight: 400;
				font-size: 32px;
				line-height: 46px;
				margin-bottom: 40px;
			}
			h4 {
				font-weight: 400;
				font-size: 28px;
				line-height: 46px;
				margin-bottom: 40px;
			}
		}
		.right {
			width: 400px;
			h3 {
				font-weight: 400;
				font-size: 32px;
				line-height: 46px;
				margin-bottom: 40px;
			}
			.list-row {
				padding-top: 10px;
				.list {
					margin-bottom: 20px;
					display: flex;
					align-items: center;
					.icon {
						width: 40px;
					}
					.icon-content {
						width: calc(100% - 40px);
						span {
							font-weight: 400;
							font-size: 20px;
							line-height: 32px;
							color: $black;
						}
					}
				}
			}
		}
	}
}
