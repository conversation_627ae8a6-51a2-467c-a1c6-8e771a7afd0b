<?php
$data = getData();
$social_link = carbon_get_theme_option('social_link');
?>

<section id="contact-map">
    <div class="custom-row">
        <div class="left-side" style="background-image:url(<?php echo isset($data['background_image']) ? $data['background_image'] : ''; ?>)">
            <div class="custom-container">
                <!-- <h2 class="map-heading"><?php echo $data['title']; ?></h2> -->
                <div class="box-contact-info">
                    <div class="address">
                        <div class="item">
                            <div class="icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 23.3276L12.6577 22.7533C18.1887 17.9237 21 13.7068 21 10C21 4.75066 16.9029 1 12 1C7.09705 1 3 4.75066 3 10C3 13.7068 5.81131 17.9237 11.3423 22.7533L12 23.3276ZM12 20.6634C7.30661 16.4335 5 12.8492 5 10C5 5.8966 8.16411 3 12 3C15.8359 3 19 5.8966 19 10C19 12.8492 16.6934 16.4335 12 20.6634ZM12 5C14.7614 5 17 7.23858 17 10C17 12.7614 14.7614 15 12 15C9.23858 15 7 12.7614 7 10C7 7.23858 9.23858 5 12 5ZM9 10C9 8.34315 10.3431 7 12 7C13.6569 7 15 8.34315 15 10C15 11.6569 13.6569 13 12 13C10.3431 13 9 11.6569 9 10Z" fill="#F05A23" />
                                </svg>
                            </div>
                            <div class="content">
                                <span>
                                    <?php echo isset($data['location_name']) ? $data['location_name'] : ''; ?>
                                </span>
                            </div>
                        </div>
                        <div class="item">
                            <div class="icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M10.8566 8.33802C11.3747 7.63981 11.5605 6.90396 10.9745 6.25443C9.65955 4.41445 8.77521 3.27662 8.22051 2.72866C7.16595 1.68693 5.43118 1.82784 4.51785 2.72777C4.0273 3.21113 3.86122 3.37712 3.35753 3.88873C0.551877 6.69589 2.26291 12.6301 6.81145 17.1831C11.359 21.7351 17.2926 23.4471 20.1042 20.634C20.569 20.1859 20.9625 19.7921 21.2729 19.4641C22.1679 18.5181 22.3038 16.8598 21.267 15.7825C20.7354 15.2302 19.6504 14.3886 17.733 13.0171C17.1457 12.492 16.4495 12.6058 15.8111 13.0246C15.504 13.226 15.2806 13.4298 14.8586 13.8522L14.0924 14.6188C13.9914 14.7198 12.621 14.0335 11.2908 12.702C9.95978 11.3697 9.27396 9.99918 9.37441 9.89874L10.1412 9.13155C10.2751 8.99754 10.3391 8.93266 10.4211 8.84639C10.5921 8.66666 10.7337 8.50356 10.8566 8.33802ZM15.5058 16.0331L16.2722 15.2662C16.5044 15.0338 16.6549 14.8909 16.7774 14.7923C18.457 15.9985 19.4298 16.757 19.8271 17.1698C20.0657 17.4177 20.0286 17.87 19.8212 18.0892C19.5342 18.3925 19.1614 18.7656 18.7038 19.2069C16.8858 21.0257 12.096 19.6438 8.22519 15.7692C4.35321 11.8934 2.97194 7.10291 4.77626 5.29761C5.27792 4.7881 5.43686 4.62924 5.92042 4.15278C6.10172 3.97413 6.59558 3.93401 6.81614 4.15189C7.2432 4.57376 8.0354 5.58811 9.20077 7.21145C9.14044 7.28635 9.06509 7.37099 8.97339 7.46737C8.90609 7.5381 8.85002 7.59494 8.72744 7.71764L7.96142 8.48408C6.65827 9.78712 7.76808 12.0048 9.87703 14.1159C11.9845 16.2254 14.2031 17.3365 15.5058 16.0331Z" fill="#F05A23" />
                                </svg>
                            </div>
                            <div class="content">
                                <span>
                                    <?php echo isset($data['phone']) ? $data['phone'] : ''; ?>
                                </span>
                            </div>
                        </div>
                        <div class="item">
                            <div class="icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M3 3H21C22.1046 3 23 3.89543 23 5V19C23 20.1046 22.1046 21 21 21H3C1.89543 21 1 20.1046 1 19V5C1 3.89543 1.89543 3 3 3ZM3 9.61811V19H21V9.61853L12 14.1185L3 9.61811ZM3 7.38199L12 11.8825L21 7.38247V5H3V7.38199Z" fill="#F05A23" />
                                </svg>
                            </div>
                            <div class="content">
                                <span>
                                    <?php echo isset($data['email']) ? $data['email'] : ''; ?>
                                </span>
                            </div>
                        </div>
                        <div class="item">
                            <div class="icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 1C5.92525 1 1 5.92525 1 12C1 18.0748 5.92525 23 12 23C18.0748 23 23 18.0748 23 12C23 5.92525 18.0748 1 12 1ZM11.0833 6.47342C10.1823 6.42667 9.33158 6.30933 8.535 6.14067C9.21792 4.57683 10.1383 3.49333 11.0833 3.05608V6.47342ZM11.0833 8.30492V11.0833H7.44233C7.50283 9.91917 7.67883 8.8485 7.9355 7.88508C8.93008 8.10783 9.98425 8.25358 11.0833 8.30492ZM11.0833 12.9167V15.6951C9.98425 15.7464 8.93008 15.8922 7.9355 16.1149C7.67883 15.1515 7.50283 14.0808 7.44233 12.9167H11.0833ZM11.0833 17.5266V20.9448C10.1383 20.5057 9.21792 19.4232 8.535 17.8603C9.33158 17.6898 10.1823 17.5733 11.0833 17.5266ZM12.9167 17.5266C13.8178 17.5733 14.6675 17.6898 15.465 17.8593C14.7821 19.4223 13.8618 20.5058 12.9167 20.9439V17.5266ZM12.9167 15.6951V12.9167H16.5586C16.4981 14.0808 16.3212 15.1515 16.0645 16.1149C15.0699 15.8922 14.0158 15.7464 12.9167 15.6951ZM12.9167 11.0833V8.30492C14.0158 8.25358 15.0699 8.10783 16.0645 7.88508C16.3212 8.84942 16.4981 9.92008 16.5586 11.0833H12.9167ZM12.9167 6.47342V3.05608C13.8618 3.49425 14.7821 4.57775 15.465 6.14067C14.6675 6.30933 13.8178 6.42667 12.9167 6.47342ZM16.3505 3.93058C17.0252 4.29542 17.6421 4.74825 18.2022 5.26433C17.8951 5.40183 17.5706 5.53017 17.2333 5.64842C16.9738 5.02325 16.6787 4.44942 16.3505 3.93058ZM6.76767 5.64842C6.43033 5.52925 6.10583 5.40183 5.79875 5.26433C6.35792 4.74825 6.97575 4.29542 7.65042 3.93058C7.32225 4.44942 7.02617 5.02325 6.76767 5.64842ZM6.18008 7.39008C5.87208 8.5295 5.67408 9.7725 5.60992 11.0833H2.87917C3.0405 9.45992 3.62992 7.96667 4.5255 6.70533C5.0425 6.962 5.598 7.1875 6.18008 7.39008ZM5.60992 12.9167C5.67317 14.2275 5.87208 15.4696 6.18008 16.6099C5.598 16.8125 5.0425 17.038 4.5255 17.2947C3.62992 16.0333 3.04142 14.5401 2.87917 12.9167H5.60992ZM6.76767 18.3525C7.02617 18.9777 7.32225 19.5506 7.6495 20.0694C6.97483 19.7055 6.35792 19.2518 5.79783 18.7366C6.10492 18.5982 6.42942 18.4708 6.76767 18.3525ZM17.2333 18.3525C17.5697 18.4708 17.8951 18.5991 18.2022 18.7366C17.643 19.2527 17.0261 19.7055 16.3505 20.0694C16.6787 19.5497 16.9738 18.9768 17.2333 18.3525ZM17.8208 16.6099C18.1288 15.4696 18.3268 14.2266 18.3901 12.9167H21.1208C20.9595 14.5401 20.371 16.0333 19.4754 17.2947C18.9575 17.038 18.402 16.8125 17.8208 16.6099ZM18.3901 11.0833C18.3268 9.7725 18.1288 8.5295 17.8208 7.39008C18.4029 7.18842 18.9575 6.962 19.4754 6.70533C20.3701 7.96667 20.9586 9.45992 21.1199 11.0833H18.3901Z" fill="#F05A23" />
                                </svg>
                            </div>
                            <div class="content">
                                <span>
                                    <?php echo isset($data['webmail']) ? $data['webmail'] : ''; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="social-icons d-flex flex-wrap">
                        <?php foreach ($social_link as $icon) : ?>
                            <a href="<?php echo esc_url($icon['icon_url']); ?>" class="icon">
                                <?php echo $icon['icon_svg']; ?>
                            </a>
                        <?php endforeach; ?>
                        <div class="icons">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="right-side">
            <img src="<?php echo isset($data['map_image']) ? $data['map_image'] : ''; ?>" alt="">
        </div>
    </div>
</section>