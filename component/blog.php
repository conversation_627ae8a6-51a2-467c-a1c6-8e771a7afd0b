<?php
$data = getData();
$args = array(
    'post_type' => 'post',
    'paged' => $paged,
    'category_name' => $data['categories_name'],

);
$the_query = new WP_Query($args);
$current_page =  get_query_var('paged'); // get page number
$max_number_page = $the_query->max_num_pages; // get max page number
?>
<?php if ($the_query->have_posts()) : ?>
    <section id="blog" class="section-spacing">
        <div class="container">
            <div class="row">
                <?php
                $count = 1;
                while ($the_query->have_posts()) : $the_query->the_post();
                    $thumbnail = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'full');
                ?>
                    <?php if ($count === 1) : ?>
                        <div class="col-lg-12 single-blog-column">
                            <div class="row align-items-lg-center">
                                <div class="col-lg-6">
                                    <div class="image">
                                        <a href="<?php the_permalink(); ?>">
                                            <img src="<?php echo $thumbnail['0']; ?>" />
                                        </a>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="content">
                                        <span class="post-date"><?php the_date();  ?></span>
                                        <h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
                                        <?php the_excerpt(); ?>

                                        <div class="cta wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.12s">
                                            <a href="<?php the_permalink(); ?>">
                                                Learn More</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else : ?>
                        <div class="col-lg-4 three-blog-column">
                            <div class="image">
                                <a href="<?php the_permalink(); ?>">
                                    <img src="<?php echo $thumbnail['0']; ?>" />
                                </a>
                            </div>
                            <div class="content">
                                <span class="post-date"> <?php the_date();  ?></span>
                                <h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
                                <?php the_excerpt(); ?>
                                <div class="cta wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.12s">
                                    <a href="<?php the_permalink(); ?>">
                                        Learn More</a>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>


                <?php $count++;
                endwhile; ?>
            </div>
            <!-- Pagination -->

            <nav class="d-flex justify-content-center">
                <ul class="pagination">
                    <?php foreach (united_get_paginated_links($the_query) as $link) : ?>
                        <?php if ($link->page > 1) : ?>
                            <li class="page-item"><a class="page-link <?php echo $link->isCurrent ? 'active' : ''; ?>" href="<?php esc_attr_e($link->url) ?>"><?php echo $link->page; ?></a></li>
                        <?php endif; ?>
                    <?php endforeach; ?>

                </ul>
            </nav>

            <!-- End pagination  -->
            <?php wp_reset_postdata(); ?>
        </div>
    </section>
<?php endif; ?>
<!-- End Blog -->