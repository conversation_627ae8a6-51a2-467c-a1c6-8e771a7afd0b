<?php
$data = getData();

?>
<section style="background-image:url(<?php echo isset($data['bg_image']) ? $data['bg_image'] : ''; ?>)" id="csr-activities" class="pt_lg--130 pt--60  <?php echo (!isset($data['padding_bottom']) || $data['padding_bottom'] != 1) ? ' pb_lg--130 pb--60' : '';
                                                                                                                        echo (isset($data['bg_image']) && !empty($data['bg_image'])) ? ' overlay' : '' ?>">
    <!-- <div class="shape">
        <img src="<?php echo IMG . '/shape1.png' ?>" alt="">
    </div> -->
    <div class="container">
        <div class="row">
            <div class="col-lg-5">
                <h2 class="wow fadeInDown" data-wow-duration="1s" data-wow-delay="0s"><?php echo isset($data['csr_heading']) ? $data['csr_heading'] : ''; ?></h2>
                <p class="wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.5s"><?php echo isset($data['csr_short_details']) ? $data['csr_short_details'] : ''; ?></p>
            </div>
            <div class="col-lg-7">
                <div class="row">
                    <?php if (isset($data['csr-item-tab-conditional']) && is_array($data['csr-item-tab-conditional'])) : ?>
                        <?php foreach ($data['csr-item-tab-conditional'] as $item) : ?>
                        <div class="item col-lg-6">
                            <div class="icon">
                                <?php echo wp_get_attachment_image($item['image']); ?>
                            </div>
                            <div class="content">
                                <?php if (isset($item['number']) && $item['number']) : ?>
                                    <div class="d-flex align-items-center">
                                        <h3 class="counter-up <?php echo isset($item['animated_countdown']) && $item['animated_countdown'] == 1 ? 'animated-countdown' : ''; ?>"><?php echo $item['number']; ?> </h3>
                                        <span class="prefix-count">&nbsp;<?php echo isset($item['prefix']) ? $item['prefix'] : ''; ?></span>
                                    </div>
                                <?php endif; ?>
                                <div>
                                    <?php if (isset($item['heading_short_text']) && $item['heading_short_text'] == 1) : ?>
                                        <h5><?php echo isset($item['short_text']) ? $item['short_text'] : ''; ?></h5>
                                    <?php else : ?>
                                        <span><?php echo isset($item['short_text']) ? $item['short_text'] : ''; ?></span>
                                    <?php endif; ?>
                                </div>

                            </div>
                        </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>