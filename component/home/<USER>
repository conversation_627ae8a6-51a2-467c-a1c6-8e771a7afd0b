<?php
$data = getData();
// pre_inline($data['grid-column-tab-conditional'])
?>

<section class="concerns section-spacing">
    <div class="container">
        <h2 class="wow fadeInDown" data-wow-duration="1s" data-wow-delay="0">Our Divisions</h2>
        <div class="grid">
            <?php $count = 1; ?>
            <?php foreach ($data['grid-column-tab-conditional'] as $col) : ?>
                <div class="column wow fadeIn" data-wow-duration="<?php echo $count; ?>.5s" data-wow-delay="0.<?php echo $count; ?>0s">
                    <?php foreach ($col['grid-thumb-tab-conditional'] as $thumb) : ?>
                        <div class="thumb">
                            <?php echo wp_get_attachment_image($thumb['grid_image'], 'full'); ?>
                            <a href="<?php echo $thumb['grid_title_url']; ?>">
                                <h4><?php echo $thumb['grid_title'] ?></h4>
                            </a>
                            <a class="learn-more" href="<?php echo $thumb['grid_title_url']; ?>">
                                Learn More
                                <svg width=" 16" height="12" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12.5542 6.83317L1.33334 6.83317C0.873098 6.83317 0.500002 6.46007 0.500002 5.99984C0.500002 5.5396 0.873098 5.1665 1.33334 5.1665L12.5542 5.1665L9.83962 2.45189C9.49791 2.11018 9.49791 1.55616 9.83962 1.21445C10.1813 0.872742 10.7353 0.872742 11.0771 1.21445L15.2437 5.38112C15.5854 5.72283 15.5854 6.27685 15.2437 6.61856L11.0771 10.7852C10.7353 11.1269 10.1813 11.1269 9.83962 10.7852C9.49791 10.4435 9.49791 9.88949 9.83962 9.54778L12.5542 6.83317Z" fill="white" />
                                </svg>
                            </a>

                        </div>

                    <?php endforeach ?>
                </div>
                <?php $count++; ?>
            <?php endforeach ?>
        </div>
    </div>
</section>