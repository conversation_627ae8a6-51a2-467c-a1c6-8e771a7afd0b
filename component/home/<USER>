<?php
$data = getData();

?>
<section style="background-image:url(<?php echo $data['bg_image']; ?>)" id="csr-activities" class="pt_lg--130 pt--60  <?php echo $data['padding_bottom'] != 1 ? ' pb_lg--130 pb--60' : '';
                                                                                                                        echo !empty($data['bg_image']) ? ' overlay' : '' ?>">
    <!-- <div class="shape">
        <img src="<?php echo IMG . '/shape1.png' ?>" alt="">
    </div> -->
    <div class="container">
        <div class="row">
            <div class="col-lg-5">
                <h2 class="wow fadeInDown" data-wow-duration="1s" data-wow-delay="0s"><?php echo $data['csr_heading']; ?></h2>
                <p class="wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.5s"><?php echo $data['csr_short_details']; ?></p>
            </div>
            <div class="col-lg-7">
                <div class="row">
                    <?php foreach ($data['csr-item-tab-conditional'] as $item) : ?>
                        <div class="item col-lg-6">
                            <div class="icon">
                                <?php echo wp_get_attachment_image($item['image']); ?>
                            </div>
                            <div class="content">
                                <?php if ($item['number']) : ?>
                                    <div class="d-flex align-items-center">
                                        <h3 class="counter-up <?php echo $item['animated_countdown'] == 1 ? 'animated-countdown' : ''; ?>"><?php echo $item['number']; ?> </h3>
                                        <span class="prefix-count">&nbsp;<?php echo $item['prefix']; ?></span>
                                    </div>
                                <?php endif; ?>
                                <div>
                                    <?php if ($item['heading_short_text'] == 1) : ?>
                                        <h5><?php echo $item['short_text']; ?></h5>
                                    <?php else : ?>
                                        <span><?php echo $item['short_text']; ?></span>
                                    <?php endif; ?>
                                </div>

                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>