<?php
$data   = getData();
$terms  = get_terms('cheif-message-year', array(
    'hide_empty' => true,
));
$year_slider = isset($data['year-sub-item']) ? $data['year-sub-item'] : array();
$the_query = new WP_Query(
    array(
        'post_type'         => 'cheif-message-note',
        'posts_per_page'    => -1,
        'tax_query' => array(
            'relation' => 'AND',
            array(
                'taxonomy' => 'cheif-message-categories',
                'field'    => 'slug',
                'terms'    => $data['slug'],
            ),
            array(
                'taxonomy' => 'cheif-message-note-categories',
                'field'    => 'slug',
                'terms'    => $data['note_slug'],
            ),
        ),
    )
);

?>
<?php if ($the_query->have_posts()) : ?>
    <section id="cheif-message" class="section-spacing">
        <div class="container">
            <?php if ($data['hide_slider'] != 1 && !empty($year_slider)) : ?>
                <!-- Slider main container -->
                <div class="cheif-message-archive wow fadeInUp" data-wow-duration="1s" data-wow-delay="0s">
                    <!-- Additional required wrapper -->
                    <div class="swiper-wrapper">
                        <!-- Slides -->
                        <?php foreach ($year_slider as $item) : ?>
                            <div class="swiper-slide" data-year="<?php echo $item['year']; ?>">
                                <span> <?php echo $item['year']; ?> </span>
                            </div>
                        <?php endforeach; ?>

                    </div>

                    <!-- If we need navigation buttons -->
                    <div class="cheif-button-prev">
                        <svg width="10" height="16" viewBox="0 0 10 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.92497 8.59167C9.08018 8.43554 9.1673 8.22433 9.1673 8.00418C9.1673 7.78402 9.08018 7.57281 8.92497 7.41668L2.2583 0.750009C2.09889 0.613486 1.89382 0.542149 1.68409 0.55025C1.47436 0.558351 1.27541 0.645293 1.127 0.793706C0.978589 0.942118 0.891646 1.14107 0.883545 1.3508C0.875444 1.56053 0.946782 1.76559 1.0833 1.92501L7.1583 8.00001L1.07497 14.075C0.918052 14.2319 0.829895 14.4448 0.829895 14.6667C0.829895 14.8886 0.918052 15.1014 1.07497 15.2583C1.23189 15.4153 1.44472 15.5034 1.66664 15.5034C1.88856 15.5034 2.10139 15.4153 2.2583 15.2583L8.92497 8.59167Z" fill="black" />
                        </svg>
                    </div>
                    <div class="cheif-button-next">
                        <svg width="10" height="16" viewBox="0 0 10 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.92497 8.59167C9.08018 8.43554 9.1673 8.22433 9.1673 8.00418C9.1673 7.78402 9.08018 7.57281 8.92497 7.41668L2.2583 0.750009C2.09889 0.613486 1.89382 0.542149 1.68409 0.55025C1.47436 0.558351 1.27541 0.645293 1.127 0.793706C0.978589 0.942118 0.891646 1.14107 0.883545 1.3508C0.875444 1.56053 0.946782 1.76559 1.0833 1.92501L7.1583 8.00001L1.07497 14.075C0.918052 14.2319 0.829895 14.4448 0.829895 14.6667C0.829895 14.8886 0.918052 15.1014 1.07497 15.2583C1.23189 15.4153 1.44472 15.5034 1.66664 15.5034C1.88856 15.5034 2.10139 15.4153 2.2583 15.2583L8.92497 8.59167Z" fill="black" />
                        </svg>
                    </div>

                </div>
            <?php endif; ?>

            <div class="cheif-message-content wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.2s">
                <!-- Additional required wrapper -->
                <div class="swiper-wrapper">
                    <!-- Slides -->
                    <?php while ($the_query->have_posts()) : $the_query->the_post(); ?>
                        <?php $term_obj_list = get_the_terms($post->ID, 'cheif-message-year');
                        $terms_string = join(', ', wp_list_pluck($term_obj_list, 'slug'));
                        $GLOBALS['z'] = $terms_string
                        ?>
                        <div class="swiper-slide" data-target="<?php echo $terms_string; ?>">
                            <h2><?php the_title(); ?></h2>
                            <?php the_content();  ?>
                        </div>
                    <?php endwhile; ?>
                </div>

            </div>

            <div class="bio align-items-center wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
                <div class="left">
                    <h3><?php echo $data['name']; ?> </h3>
                    <h4><?php echo $data['designation']; ?></h4>
                    <h4><?php echo $data['company_name']; ?></h4>
                </div>
                <!-- <div class="right">
                    <?php if (is_array($data['social-item'])) : ?>
                        <ul class="social d-flex list-underlist-none">
                            <?php foreach ($data['social-item'] as $item) : ?>
                                <li>
                                    <a href="<?php echo $item['social_url']; ?>">
                                        <?php echo wp_get_attachment_image($item['social_icon'], 'full', true); ?>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div> -->
            </div>

        </div>
    </section>

<?php else : ?>
    <p><?php _e('Sorry, no posts matched your criteria.'); ?></p>
<?php endif; ?>


<!-- End Cheif Message -->