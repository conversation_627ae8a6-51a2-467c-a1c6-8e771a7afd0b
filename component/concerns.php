<?php
$data = getData();
$bg = $data['add_bg'];
$style2 = $data['change_style'];

?>
<?php if ($data['hide_section'] != 1) : ?>
    <section id="generic-concerns" class="<?php echo $bg == 1 ? 'add-bg' : '';
                                            echo $data['single_item'] == 1 ? ' single-items ' : '';
                                            echo $data['height_auto'] == 1 ? ' height-auto ' : '';
                                            echo $data['blue_bullet'] == 1 ? ' blue-icon ' : '';  ?>  section-spacing ">
        <div class="container">
            <?php if ($data['hide_title'] != 1) : ?>
                <?php if (empty($data['title'])) : ?>
                    <div class="page-header">
                        <h2 class="wow fadeInUp" data-wow-duration="1s" data-wow-delay="0s"><?php echo esc_html('Our concerns') ?></h2>
                    </div>
                <?php else : ?>
                    <div class="page-header">
                        <h2 class="wow fadeInUp" data-wow-duration="1s" data-wow-delay="0s"><?php echo $data['title']; ?></h2>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
            <div class="row">
                <?php $count = 1; ?>
                <?php foreach ($data['generic-concerns-conditional'] as $item) : ?>

                    <?php if ($count === 1 && $style2 == 1) :  ?>
                        <div class="col-lg-12 wow fadeInUp" data-wow-duration="1s" data-wow-delay="0s">
                            <div class="single-concerns-items style2">
                                <div class="thumb">
                                    <a href="<?php echo get_permalink($item['selected_ids']); ?>"> <?php echo wp_get_attachment_image($item['image'], 'full');  ?>
                                    </a>
                                </div>
                                <div class="content">
                                    <h3><?php echo $item['title'] ?></h3>
                                    <?php echo $item['concerns_details'] ?>
                                    <?php if ($item['selected_ids']) : ?>
                                        <div class="cta wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.12s">
                                            <a href="<?php echo get_permalink($item['selected_ids']); ?>">
                                                Learn More</a>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($item['website_link']) : ?>
                                        <div class="cta wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.12s">
                                            <a href="<?php echo $item['website_link']; ?>">
                                                Visit Link</a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php else : ?>
                        <div class="<?php col_size($data['col_size']) ?> wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.<?php echo $count; ?>s">
                            <div class="single-concerns-items">
                                <div class="thumb">
                                    <a href="<?php echo get_permalink($item['selected_ids']); ?>"> <?php echo wp_get_attachment_image($item['image'], 'full');  ?>
                                    </a>
                                </div>
                                <div class="content">
                                    <h3><?php echo $item['title'] ?></h3>
                                    <?php echo $item['concerns_details'] ?>
                                    <?php if ($item['selected_ids']) : ?>
                                        <a href="<?php echo get_permalink($item['selected_ids']); ?>" class="read-more">
                                            Learn More
                                            <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M13.0988 10.1977L3 10.1977C2.58579 10.1977 2.25 9.86132 2.25 9.44633C2.25 9.03133 2.58579 8.69491 3 8.69491L13.0988 8.69492L10.6557 6.24716C10.3481 5.93904 10.3481 5.43949 10.6557 5.13137C10.9632 4.82325 11.4618 4.82325 11.7693 5.13137L15.5193 8.88843C15.8269 9.19655 15.8269 9.69611 15.5193 10.0042L11.7693 13.7613C11.4618 14.0694 10.9632 14.0694 10.6557 13.7613C10.3481 13.4532 10.3481 12.9536 10.6557 12.6455L13.0988 10.1977Z" fill="black"></path>
                                            </svg>
                                        </a>
                                    <?php endif; ?>
                                    <?php if ($item['website_link']) : ?>
                                        <div class="cta wow fadeInDown website-link" data-wow-duration="1s" data-wow-delay="0.12s">
                                            <a href="<?php echo $item['website_link']; ?>">
                                                Visit Website </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php $count++; ?>
                <?php endforeach; ?>

            </div>
        </div>
    </section>
<?php endif; ?>
<!-- End Concerns -->