<?php
$data = getData();
?>
<section id="reserach-investor" class="section-spacing">
    <div class="container">
        <div class="custom-width">
            <div class=" for-desktop">
                <ul class="research-nav-menu d-flex justify-content-center list-underlist-none">
                    <?php $count = 1;
                    foreach ($data['reserach-categories-item'] as $item) : ?>
                        <li><a data-target="<?php echo fixForUri($item['title']); ?>" class="<?php echo $count === 1 ? 'active' : ''  ?>" href="#"><?php echo $item['title']; ?></a></li>
                    <?php $count++;
                    endforeach; ?>
                </ul>
            </div>
            <div class="research-nav-mobile for-mobile">
                    <!-- Slider main container -->
                    <div class="swiper">
                        <!-- Additional required wrapper -->
                        <div class="swiper-wrapper">
                            <!-- Slides -->
                            <?php $count = 1;
                                foreach ($data['reserach-categories-item'] as $item) : ?>
                                <div class="swiper-slide">
                                    <a data-target="<?php echo fixForUri($item['title']); ?>" class="<?php echo $count === 1 ? 'active' : ''  ?>" href="#"><?php echo $item['title']; ?></a>
                                </div>
                            <?php $count++;
                                endforeach; ?>
                        </div>
                    </div>
                </div>

            <div class="accordion" id="research-accordion">
                <?php
                foreach ($data['reserach-categories-item'] as $categories) :
                    foreach ($categories['reserach-sub-categories-item'] as $sub_categories) :
                        ?>
                        <div class="accordion-item" data-target="<?php echo fixForUri($categories['title']); ?>">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button">
                                    <?php echo $sub_categories['title']; ?>
                                </button>
                            </h2>
                            <div class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $download = $sub_categories['reserach-sub-item'] ?>
                                    <?php if (is_array($download)) : ?>
                                        <div class="item d-flex justify-content-between">
                                            <div class="left">
                                                <h3><?php esc_html_e('Title', 'united'); ?></h3>
                                            </div>
                                            <div class="right">
                                                <h3><?php esc_html_e('Download', 'united'); ?></h3>
                                            </div>
                                        </div>
                                        <?php foreach ($download as $item) : ?>
                                            <div class="item d-flex justify-content-between align-items-center">
                                                <div class="left">
                                                    <h4><?php echo $item['title']; ?></h4>
                                                </div>
                                                <div class="right">
                                                    <a href="<?php echo $item['download_pdf']; ?>" download>
                                                        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M21.6605 15V19C21.6605 19.5304 21.4498 20.0391 21.0747 20.4142C20.6997 20.7893 20.191 21 19.6605 21H5.66052C5.13009 21 4.62138 20.7893 4.24631 20.4142C3.87124 20.0391 3.66052 19.5304 3.66052 19V15" stroke="#F05A23" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                            <path d="M7.66052 10L12.6605 15L17.6605 10" stroke="#F05A23" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                            <path d="M12.6605 15V3" stroke="#F05A23" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                        </svg>
                                                        <span>Download pdf</span>
                                                    </a>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                <?php
                    endforeach;
                endforeach; ?>
            </div>
        </div>
    </div>
</section>

<!-- End Research Investor -->