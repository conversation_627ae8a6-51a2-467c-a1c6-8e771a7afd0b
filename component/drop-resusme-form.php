<?php
$data = getData();
?>
<section id="application-form" class="section-spacing">
    <div class="container">
        <div class="page-header">
            <h2><?php echo $data['title']; ?></h2>

            <?php if ($data['sub_text']) : ?>
                <p><?php echo $data['sub_text']; ?></p>
            <?php endif; ?>

        </div>
        <form method="POST" action="<?php echo esc_url(admin_url('admin-post.php')) ?>" enctype="multipart/form-data">
            <input type="hidden" name="action" value="add_resume_default">

            <div id="verify-email">
                <span><?php echo $data['sub_input_title']; ?></span>
                <div class="form-row">
                    <div class="verify-email col-lg-10">
                        <input id="Email" type="email" name="email" placeholder="Enter your Email" required>
                    </div>
                    <div class="col-lg-2">
                        <!-- <input id="verify-email-btn" type="submit" name="submission_submit" value="verify email"> -->
                        <button id="verify-email-btn" type="submit" name="submission_submit" value="verify email"><span>VERIFY EMAIL</span></button>
                    </div>
                </div>
            </div>
            <div class="alert mt--10" role="alert"> </div>

            <div id="applicate-form-resume" class="disible">
                <h3>Application Form</h3>
                <div class="row">
                    <div class="col-lg-6">
                        <input type="text" id="verifyCode" name="verification_code" placeholder="Enter the Verification Code" disabled>
                    </div>

                    <div class="col-lg-6">
                        <input type="text" id="Name" name="name" placeholder="Full Name" disabled>
                    </div>
                    <div class="col-lg-6">
                        <input type="text" id="Phone" name="phone" placeholder="Phone Number" disabled>
                    </div>
                    <div class="col-lg-6 arrow">
                        <select name="division" id="Division" disabled>
                            <option value="">Division</option>
                            <?php foreach ($data['division-department'] as $item) : ?>
                                <option value="<?php echo $item['title']; ?>"><?php echo $item['title']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-6 arrow">
                        <select name="interest_department" id="interestDepartment" disabled>
                            <option value="">Interested Department</option>
                            <?php foreach ($data['interest-department'] as $item) : ?>
                                <option value="<?php echo $item['title']; ?>"><?php echo $item['title']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-lg-6">
                        <input type="text" id="currentCompany" name="current_company" placeholder="Current Company" disabled>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-12">
                        <div class="fake-file-input d-flex align-content-center">
                            <div class="fake-content">
                                <svg width="40" height="35" viewBox="0 0 40 35" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M19.9061 15.3198L20.0533 15.4469L25.3033 20.6969C25.8158 21.2095 25.8158 22.0405 25.3033 22.5531C24.8373 23.019 24.1081 23.0614 23.5943 22.6802L23.4471 22.5531L20.4377 19.5425V33C20.4377 33.7249 19.8501 34.3125 19.1252 34.3125C18.4607 34.3125 17.9116 33.8187 17.8247 33.1781L17.8127 33V19.5425L14.8033 22.5531C14.3373 23.019 13.6081 23.0614 13.0943 22.6802L12.9471 22.5531C12.4811 22.0871 12.4388 21.358 12.82 20.8441L12.9471 20.6969L18.1971 15.4469C18.6631 14.981 19.3922 14.9386 19.9061 15.3198ZM17.9243 0.1875C24.3745 0.1875 29.7284 4.93965 30.537 11.1456L30.5684 11.4365L30.5967 11.4375C35.3874 11.4375 39.3017 15.1742 39.5044 19.8674L39.5127 20.25C39.5127 24.4577 36.5172 28.1134 32.4626 28.9726L32.0795 29.0449L31.865 29.0625H27.8752C27.1503 29.0625 26.5627 28.4749 26.5627 27.75C26.5627 27.0855 27.0565 26.5364 27.6971 26.4495L27.8752 26.4375H31.7409L31.9671 26.394C34.6855 25.7941 36.7313 23.3834 36.8791 20.5756L36.8877 20.25C36.8877 16.8368 34.0751 14.0625 30.5967 14.0625C30.3584 14.0625 30.1233 14.0755 29.8921 14.1012L29.5484 14.1494L28.032 14.4065L28.0127 12.4568C27.8554 7.09735 23.3914 2.8125 17.9243 2.8125C12.3463 2.8125 7.83166 7.26573 7.83166 12.75L7.83876 13.1235L7.87615 14.0948L6.95792 14.4138C4.46232 15.2807 2.7627 17.6084 2.7627 20.25C2.7627 23.5531 5.39673 26.2578 8.71918 26.4289L9.05364 26.4375H10.3752C11.1001 26.4375 11.6877 27.0251 11.6877 27.75C11.6877 28.4145 11.1939 28.9636 10.5533 29.0505L10.3752 29.0625H9.05364C4.1335 29.0625 0.137695 25.1211 0.137695 20.25C0.137695 16.8815 2.06656 13.879 4.99908 12.3991L5.21445 12.294L5.23484 11.9068C5.66465 5.49775 10.9586 0.408163 17.4962 0.194486L17.9243 0.1875Z" fill="#F05A23" />
                                </svg>
                                <br>
                                <h3><?php echo esc_html('Upload Your Resume') ?></h3>
                            </div>

                            <input id="pdf_file" type="file" name="resume" disabled>
                        </div>
                        <span class="filename">No file choosen</span>

                    </div>
                </div>
                <input id="submit" class="verify_sent_resume" type="submit" name="submit" value="submit application" disabled>

            </div>

            <?php wp_nonce_field('cpt_nonce_action', 'cpt_nonce_field') ?>
        </form>
    </div>
</section>