<?php $data = getData(); ?>

<section class="core-services section-spacing">

    <div class="container">
        <div class="page-header">

            <h2 class="wow fadeInUp" data-wow-duration="1s" data-wow-delay="0s"><?php echo esc_html('Core Business'); ?></h2>

            <?php if (!empty($data['core-item-tab-conditional'])) : ?>
                <div class="services-highlighted-row">
                    <?php $count = 1; ?>
                    <?php foreach ($data['core-item-tab-conditional'] as $coreItem) : ?>
                        <div class="service wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.<?php echo $count * 2; ?>s">
                            <div class="icon">
                                <?php echo wp_get_attachment_image($coreItem['image'], 'full');  ?>
                            </div>
                            <div class="icon-content">
                                <h4> <span><?php echo $coreItem['title']; ?></span>
                                </h4>
                            </div>
                        </div>
                        <?php $count++; ?>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>


            <!-- Loop -->
            <h2 class="support-bussiness-title"><?php echo esc_html('Support Business'); ?></h2>
            <?php if (!empty($data['core-item2-tab-conditional'])) : ?>
                <div class="services-highlighted-row">

                    <?php $count = 1; ?>
                    <?php foreach ($data['core-item2-tab-conditional'] as $coreItem) : ?>
                        <div class="service wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.<?php echo $count * 2; ?>s">
                            <div class="icon">
                                <?php echo wp_get_attachment_image($coreItem['image'], 'full');  ?>
                            </div>
                            <div class="icon-content">
                                <h4> <span><?php echo $coreItem['title']; ?></span>
                                </h4>
                            </div>
                        </div>
                        <?php $count++; ?>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>



            <?php if (!empty($data['core-list-item-tab-conditional'])) : ?>
                <div class="short-list-row">
                    <div class="left">
                        <h3><?php echo $data['core_content_title']; ?></h3>
                    </div>
                    <div class="right">
                        <div class="list-row">
                            <?php $count = 1; ?>
                            <?php foreach ($data['core-list-item-tab-conditional'] as $list) : ?>
                                <div class="list wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.<?php echo $count * 2; ?>s">
                                    <div class="icon">
                                        <img src="<?php echo IMG ?>/blue-list-icon.png" alt="">
                                    </div>
                                    <div class="content">
                                        <span> <?php echo $list['title'] ?> </span>
                                    </div>
                                </div>
                                <?php $count++; ?>
                            <?php endforeach ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- End Core Busniness -->