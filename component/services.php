<?php
$data = getData();
?>


<section id="generic-service" class="section-spacing <?php echo isset($data['padding_bottom']) && $data['padding_bottom'] == 1 ? ' spacing-bottom-none ' : '' ?>">
    <div class="container position-relative">
        <div class="highlight-text">
            <?php if ($data['title']) : ?>
                <h3 class="wow fadeInUp" data-wow-duration="1s" data-wow-delay="0s"><?php echo $data['title']; ?></h3>
            <?php endif; ?>
            <?php if ($data['description']) : ?>
                <?php echo $data['description']; ?>
            <?php endif; ?>
        </div>
    </div>
    <div class="container">
        <?php if (is_array($data['service-item-tab-conditional'])) : ?>
            <!-- Slider main container -->
            <!-- Slides -->
            <div class="main-service-item">
                <?php
                $count = 0; // Initialize counter
                foreach ($data['service-item-tab-conditional'] as $item) : ?>
                    <div class="single-service-item" style="background-image: url(<?php echo IMG . '/service-bg2.png' ?>);">
                        <h3><?php echo $item['heading']; ?></h3>
                        <?php echo wpautop($item['short_text']); ?>
                    </div>
                    <?php $count++; ?>
                <?php endforeach; ?>
            </div>
            <!-- End Slider -->
        <?php endif; ?>
    </div>
</section>

<!-- End Service -->