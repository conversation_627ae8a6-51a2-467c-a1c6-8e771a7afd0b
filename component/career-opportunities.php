<?php
$data   = getData();
// pre_inline($data);
$args = array(
    'post_type'     => 'career',
    'post_status'   => 'publish',
    'orderby' => 'name',
    'order' => 'ASC',
    // 's' => "Senior",
    // 'tax_query' => array(
    //     array(
    //         'taxonomy' => 'career-categories',
    //         'field'    => 'slug',
    //         'terms'    => 'marketing-department',
    //     ),
    // ),
);

$the_query  = new WP_Query($args);
$department = get_terms('career-categories', array(
    'orderby'    => 'name',
    'hide_empty' => 0,
));

?>


<section id="career-opportunties" class="section-spacing">
    <div class="container">
        <div class="section-page-header text-center">
            <h2><?php echo $data['title']; ?></h2>
            <div class="text-center">
                <p><?php echo $data['content']; ?></p>
            </div>
        </div>
        <div class="search-bar d-flex flex-wrap align-items-center" data-slug="" data-keyword="">
            <div class="categories">
                <?php if (is_array($department)) : ?>
                    <select id="select-department">
                        <option value="all"><?php echo esc_html('All Department') ?></option>
                        <?php foreach ($department as $item) : ?>
                            <option value="<?php echo $item->slug; ?>"><?php echo $item->name; ?></option>
                        <?php endforeach; ?>
                    </select>
                <?php endif; ?>
            </div>
            <div class="search-job">
                <input type="text" id="find-job" placeholder="<?php echo esc_html('Search job', 'united') ?>">
            </div>
        </div>
        <div class="job-post">
            <?php if ($the_query->have_posts()) : ?>
                <?php while ($the_query->have_posts()) : $the_query->the_post(); ?>
                    <?php get_template_part('template-parts/loop/content', 'job-item') ?>
                <?php endwhile; ?>
            <?php else :  ?>
                <p><?php _e('Sorry, no posts matched your criteria.'); ?></p>
            <?php endif; ?>
        </div>
    </div>
</section>


<!-- End Career Opportunties -->