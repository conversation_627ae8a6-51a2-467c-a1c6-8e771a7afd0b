<?php
$data = getData();
if ($data['section_background']) {
    $background = 'style="background-color:' . $data['section_background'] . '"';
}
?>
<section id="contribution" <?php echo $background; ?> class="section-spacing <?php echo $data['style_change'] == 1 ? 'another-style '  : '';
                                                                                echo $data['padding_top'] == 1 ? ' spacing-top-none ' : '';
                                                                                echo $data['padding_bottom'] == 1 ? ' spacing-bottom-none ' : '';
                                                                                echo $data['item_flex'] == 1 ? ' item_flex ' : '';  ?> ">
    <div class="container">
        <div class="page-header black">
            <h2 class="wow fadeInUp" data-wow-duration="1s" data-wow-delay="0s"><?php echo $data['title'] ?></h2>
        </div>
        <div class="row">
            <?php $count = 1; ?>
            <?php foreach ($data['contribution-tab-conditional'] as $item) : ?>
                <div class="col-md-4 single-contribute-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
                    <?php if ($item['image']) : ?>
                        <div class="icon">
                            <?php echo wp_get_attachment_image($item['image'], 'full');  ?>
                        </div>
                    <?php endif; ?>
                    <div class="content">
                        <?php if ($item['title']) : ?>
                            <h3><?php echo $item['title'] ?></h3>
                        <?php endif; ?>
                        <?php if ($item['content']) : ?>
                            <p><?php echo $item['content'] ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php $count++; ?>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- End Contribution -->