<?php
$data = getData();
if (isset($data['section_background']) && $data['section_background']) {
    $background = 'style="background-color:' . $data['section_background'] . '"';
} else {
    $background = '';
}
?>
<section id="contribution" <?php echo $background; ?> class="section-spacing <?php echo isset($data['style_change']) && $data['style_change'] == 1 ? 'another-style '  : '';
                                                                                echo isset($data['padding_top']) && $data['padding_top'] == 1 ? ' spacing-top-none ' : '';
                                                                                echo isset($data['padding_bottom']) && $data['padding_bottom'] == 1 ? ' spacing-bottom-none ' : '';
                                                                                echo isset($data['item_flex']) && $data['item_flex'] == 1 ? ' item_flex ' : '';  ?> ">
    <div class="container">
        <div class="page-header black">
            <h2 class="wow fadeInUp" data-wow-duration="1s" data-wow-delay="0s"><?php echo isset($data['title']) ? $data['title'] : ''; ?></h2>
        </div>
        <div class="row">
            <?php $count = 1; ?>
            <?php foreach ($data['contribution-tab-conditional'] as $item) : ?>
                <div class="col-md-4 single-contribute-item wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
                    <?php if ($item['image']) : ?>
                        <div class="icon">
                            <?php echo wp_get_attachment_image($item['image'], 'full');  ?>
                        </div>
                    <?php endif; ?>
                    <div class="content">
                        <?php if ($item['title']) : ?>
                            <h3><?php echo $item['title'] ?></h3>
                        <?php endif; ?>
                        <?php if ($item['content']) : ?>
                            <p><?php echo $item['content'] ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php $count++; ?>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- End Contribution -->