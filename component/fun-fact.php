<?php
$data = getData();
?>
<section class="fun-fact section-spacing">
    <?php if (!empty($data['fun-fact-item-tab-conditional'])) : ?>
        <div class="container">
            <div class="row align-items-center <?php echo isset($data['space_around']) && $data['space_around'] == 'yes' ? 'justify-content-around' : 'justify-content-between' ?> ">
                <?php foreach ($data['fun-fact-item-tab-conditional'] as $item) : ?>
                    <div class="single-funfact col-lg-3">
                        <div class="thumb">
                            <?php echo wp_get_attachment_image($item['image'], 'full');  ?>
                        </div>
                        <div class="text">
                            <h4><?php echo $item['heading']; ?></h4>
                            <span><?php echo $item['short_text']; ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
    <div class="container">
        <div class="content">
            <?php echo isset($data['details']) ? $data['details'] : ''; ?>
        </div>
    </div>
</section>