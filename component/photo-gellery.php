<?php
$terms = get_terms('photos_categories', array(
    'hide_empty' => true,
));
$args = array(
    'post_type' => 'gallery',
    'tax_query' => array(
        array(
            'taxonomy' => 'photos_categories',
            'field'    => 'slug',
            'terms'    => $terms[0]->slug,
        ),
    ),
);
$the_query = new WP_Query($args);
?>
<div id="animated-thumbnails" style="display: none;"></div>

<?php if ($the_query->have_posts()) : ?>
    <section id="photo-gallery" class="section-spacing">
        <div class="container">
            <div class="photo-gallery-nav">
                <div class="photo-gallery-nav-desktop">
                    <ul>
                        <?php $count = 1;
                        foreach ($terms as $term) : ?>
                            <li><a class="<?php echo $count == 1 ? 'active' : ''; ?>" href="#" data-category="<?php echo $term->slug;  ?>"><?php echo $term->name;  ?></a></li>
                        <?php $count++;
                        endforeach; ?>
                    </ul>
                </div>
                <div class="photo-gallery-nav-mobile">
               
                    <!-- Slider main container -->
                    <div class="swiper">
                        <!-- Additional required wrapper -->
                        <div class="swiper-wrapper">
                            <!-- Slides -->
                            <?php $count = 1;
                            foreach ($terms as $term) : ?>
                                <div class="swiper-slide"><a class="<?php echo $count == 1 ? 'active' : ''; ?>" href="#" data-category="<?php echo $term->slug;  ?>"><?php echo $term->name;  ?></a></div>
                            <?php $count++;
                            endforeach; ?>
                        </div>
                    </div>
                    
                </div>
            </div>

            <div class="gallery">
                <div class="row">
                    <?php while ($the_query->have_posts()) : $the_query->the_post(); ?>
                        <?php get_template_part('template-parts/content', 'gallery-item') ?>
                    <?php endwhile; ?>
                </div>
            </div>
        </div>
    </section>

<?php endif; ?>

<!-- End Photogallery -->