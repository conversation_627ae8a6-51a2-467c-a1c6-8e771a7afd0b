<?php
$data = getData();

?>
<section id="united-city" class="<?php echo $data['white_bg'] == 1 ? 'white-bg' : ''; ?> section-spacing <?php echo $data['remove_padding_bottom'] == 1 ? 'spacing-bottom-none' : ''  ?>">
    <div class="container">
        <h2><?php echo $data['page_heading']; ?></h2>
        <div class="custom-parallax" style="background-image:url(<?php echo wp_get_attachment_url($data['image']);  ?>)"> </div>
        <?php if ($data['use_full_paragraph'] != 1) : ?>
            <div class="row">
                <div class="col-lg-6">
                    <?php echo $data['left_editor']; ?>
                </div>
                <div class="col-lg-6">
                    <?php echo $data['right_editor']; ?>
                    <?php if (is_array($data['residential-icon-sub-item'])) : ?>
                        <div class="icon-row">
                            <?php foreach ($data['residential-icon-sub-item']  as $icon) : ?>
                                <div class="icon-item">
                                    <div class="icon">
                                        <img src="<?php echo $icon['image']; ?>" alt="icon">
                                    </div>
                                    <div class="icon-content">
                                        <h4><?php echo $icon['title']; ?></h4>
                                        <span><?php echo $icon['details']; ?> </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php else : ?>
            <div class="row">
                <div class="col-lg-12">
                    <?php echo $data['left_editor']; ?>
                </div>
            </div>
        <?php endif; ?>

    </div>
</section>
<!-- End United City -->