<?php
$data = getData();
?>
<section id="half-to-half" class="section-spacing <?php echo isset($data['remove_padding_bottom']) && $data['remove_padding_bottom'] == 1 ? 'spacing-bottom-none' : ''  ?>">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 order-lg-2">
                <h2 class="wow fadeInUp mobile-title" data-wow-duration="1s" data-wow-delay="0s"><?php echo isset($data['title']) ? $data['title'] : ''; ?></h2>

                <div class="desc wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
                    <?php echo isset($data['details']) ? $data['details'] : ''; ?>
                </div>
            </div>
            <div class="col-lg-6 order-lg-1">
                <h2 class="wow fadeInUp desktop-title" data-wow-duration="1s" data-wow-delay="0s"><?php echo isset($data['title']) ? $data['title'] : ''; ?></h2>
                <?php if (is_array($data['certified-tab-conditional']) && !empty($data['certified-tab-conditional'])) : ?>
                    <div class="ceritified">
                        <?php foreach ($data['certified-tab-conditional'] as $logo) : ?>
                            <div class="logo">
                                <?php echo wp_get_attachment_image($logo['image'], 'full') ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

            </div>

        </div>
    </div>
</section>
<!-- End Half to Half -->