<?php
$categories = get_the_terms(get_the_ID(), 'category')[0]->slug;
?>
<div class="container">
    <article class="section-spacing" id="post-<?php the_ID(); ?>">
        <h2><?php the_title(); ?></h2>
        <div class="post-meta">
            <div class="left">
                <span class="post-date"><?php the_date(); ?></span>
            </div>
            <div class="right"></div>
        </div>

        <?php $image = carbon_get_post_meta(get_the_ID(), 'post_banner'); ?>
        <?php if ($image) : ?>
            <img class="post-banner" src="<?php echo $image; ?>" alt="post-banner">
        <?php endif; ?>
        
        <?php the_content(); ?>
    </article>
</div>
<?php
$related_query = new WP_Query(array(
    'post_type' => 'post',
    'category_name' => $categories,
    'post__not_in' => array(get_the_ID()),
    'posts_per_page' => 3,
    'orderby' => 'date',
));
?>
<div class="may-also-like">
    <div class="container">
        <div class="page-section">
            <div class="row align-items-end">
                <div class="col-lg-6">
                    <h3><?php echo esc_html__('You may also like', 'united'); ?></h3>
                    <p><?php echo carbon_get_theme_option('united_may_also_like') ?></p>
                </div>
                <div class="col-lg-6">
                    <?php
                    if ($categories === 'news') {
                        $url = home_url('/news');
                    }
                    if ($categories === 'life-at-united') {
                        $url = home_url('/life-at-united');
                    }

                    ?>
                    <div class="cta wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.12s">
                        <a href="<?php echo $url; ?>">
                            View More</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="three-blog-column">
            <div class="row">
                <?php
                while ($related_query->have_posts()) : $related_query->the_post();
                    $thumbnail = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'full');

                    ?>
                    <div class="col-lg-4  mb--30">
                        <div class="image">
                            <a href="<?php the_permalink(); ?>"><img src="<?php echo $thumbnail['0']; ?>" /></a>
                        </div>
                        <div class="content">
                            <span class="post-date"> </span>
                            <h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
                            <?php the_excerpt(); ?>
                            <div class="cta wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.12s">
                                <a href="<?php the_permalink(); ?>">
                                    Learn More</a>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        </div>
    </div>
</div>