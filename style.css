/*!
Theme Name: United group
Theme URI: https://united.com.bd/
Author: Notionhive
Author URI: https://notionhive.com/
Description: Description
Version: 1.0.0
Tested up to: 5.4
Requires PHP: 5.6
License: GNU General Public License v2 or later
License URI: LICENSE
Text Domain: united group
Tags: custom-background, custom-logo, custom-menu, featured-images, threaded-comments, translation-ready

This theme, like WordPress, is licensed under the GPL.
Use it to make something cool, have fun, and share what you've learned.

ILC is based on Underscores https://underscores.me/, (C) 2012-2020 Automattic, Inc.
Underscores is distributed under the terms of the GNU GPL v2 or later.

Normalizing styles have been helped along thanks to the fine work of
<PERSON> and <PERSON> https://necolas.github.io/normalize.css/
*/

/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Generic
	- Normalize
	- Box sizing
# Base
	- Typography
	- Elements
	- Links
	- Forms
## Layouts
# Components
	- Navigation
	- Posts and pages
	- Comments
	- Widgets
	- Media
	- Captions
	- Galleries
# plugins
	- Jetpack infinite scroll
# Utilities
	- Accessibility
	- Alignments

--------------------------------------------------------------*/

/*-------------------------------------------------------------- */
.nav-menu ul {
	box-shadow: 0 0 5px 0 rgb(0 0 0 / 8%);
}
.headroom--not-top.headroom--pinned .main-menu-area {
	box-shadow: 0 0 5px 0 rgb(0 0 0 / 8%);
}
.main-menu-area.fixed-menu {
	box-shadow: 0 0 5px 0 rgb(0 0 0 / 8%);
}

.single-blog-column img {
	max-height: 400px;
	object-fit: cover;
}
.single-post .post-banner {
	max-height: 500px;
}

#cheif-message .cheif-message-content p {
	text-align: justify;
}

/* PART 1 - Before Lazy Load */
img[data-lazyloaded] {
	opacity: 0;
}
/* PART 2 - Upon Lazy Load */
img.litespeed-loaded {
	-webkit-transition: opacity 0.5s linear 0.2s;
	-moz-transition: opacity 0.5s linear 0.2s;
	transition: opacity 0.5s linear 0.2s;
	opacity: 1;
}

@media (max-width: 460px) {
	.page-id-240 #image-slider .swiper .swiper-slide .slider-content img {
		height: auto;
	}
}
